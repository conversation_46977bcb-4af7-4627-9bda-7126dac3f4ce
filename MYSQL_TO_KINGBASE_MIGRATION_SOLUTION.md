# MySQL到人大金仓数据库迁移解决方案

## 问题分析

### 错误现象
外部项目启动时报错：
```
java.sql.SQLException: com.mysql.cj.jdbc.Driver
Caused by: java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
```

### 根本原因
1. **动态数据源机制**：项目使用了动态数据源功能，数据源配置存储在数据库的 `datasource_info` 表中
2. **配置不一致**：虽然代码中已经移除了MySQL依赖并添加了人大金仓依赖，但数据库中的数据源配置仍然是MySQL驱动
3. **运行时加载失败**：`TenantDynamicDataSourceProvider` 在运行时从数据库读取数据源配置，尝试加载MySQL驱动类但找不到

### 技术细节
- 动态数据源提供者：`com.qm.tds.dynamic.config.TenantDynamicDataSourceProvider`
- 数据源配置表：`datasource_info`
- 租户关联表：`tenant_datasource_rel`
- 驱动加载位置：`TenantDynamicDataSourceProvider.loadDataSources()` 方法中的 `Class.forName(driverClassName)`

## 解决方案

### 1. 立即解决方案（数据库配置更新）

#### 步骤1：检查当前配置
```sql
SELECT 
    id, name, driver_class_name, conn_url, service_name, type
FROM datasource_info 
WHERE driver_class_name IN ('com.mysql.cj.jdbc.Driver', 'com.mysql.jdbc.Driver');
```

#### 步骤2：备份数据
```sql
CREATE TABLE datasource_info_backup AS SELECT * FROM datasource_info;
CREATE TABLE tenant_datasource_rel_backup AS SELECT * FROM tenant_datasource_rel;
```

#### 步骤3：执行迁移
使用提供的迁移脚本：`cloud-common/tds-base-dynamic-starter/DB/migrate_mysql_to_kingbase.sql`

关键更新语句：
```sql
-- 更新驱动类名
UPDATE datasource_info 
SET driver_class_name = 'com.kingbase8.Driver'
WHERE driver_class_name IN ('com.mysql.cj.jdbc.Driver', 'com.mysql.jdbc.Driver');

-- 更新连接URL（根据实际情况调整）
UPDATE datasource_info 
SET conn_url = REPLACE(conn_url, 'jdbc:mysql://', 'jdbc:kingbase8://')
WHERE conn_url LIKE 'jdbc:mysql://%';
```

#### 步骤4：验证配置
```sql
SELECT id, name, driver_class_name, conn_url, service_name
FROM datasource_info 
ORDER BY service_name, name;
```

#### 步骤5：重启应用
更新数据库配置后，重启相关的应用服务。

### 2. 代码层面确认

#### 依赖配置已正确
✅ 父pom中MySQL依赖已注释：
```xml
<!--        <dependency>-->
<!--            <groupId>com.mysql</groupId>-->
<!--            <artifactId>mysql-connector-j</artifactId>-->
<!--        </dependency>-->
```

✅ 人大金仓依赖已添加：
```xml
<dependency>
    <groupId>cn.com.kingbase</groupId>
    <artifactId>kingbase8</artifactId>
</dependency>
```

#### 版本升级已完成
✅ 项目版本已从 7.0.0 升级到 7.1.0

### 3. 驱动配置对照表

| 数据库类型 | 驱动类名 | 连接URL格式 |
|-----------|---------|------------|
| MySQL | `com.mysql.cj.jdbc.Driver` | `**************************************` |
| 人大金仓 | `com.kingbase8.Driver` | `******************************************` |

## 文件清单

### 新增文件
1. `cloud-common/tds-base-dynamic-starter/DB/migrate_mysql_to_kingbase.sql` - 迁移脚本
2. `cloud-common/tds-base-dynamic-starter/DB/README_MIGRATION.md` - 迁移指南
3. `MYSQL_TO_KINGBASE_MIGRATION_SOLUTION.md` - 本解决方案文档

### 修改文件
1. `cloud-common/tds-base-dynamic-starter/DB/datasource_info.sql` - 添加了迁移说明

## 注意事项

1. **备份重要性**：执行迁移前务必备份数据库
2. **环境验证**：建议先在测试环境验证配置正确性
3. **连接参数**：某些MySQL特有的连接参数可能需要调整
4. **SQL兼容性**：检查应用中是否有MySQL特有的SQL语法需要适配
5. **多服务协调**：如果有多个微服务，需要确保所有相关服务的配置都已更新

## 验证清单

- [ ] 数据库中所有MySQL驱动配置已更新为人大金仓驱动
- [ ] 连接URL格式已正确更新
- [ ] 应用服务已重启
- [ ] 数据源连接测试通过
- [ ] 业务功能正常运行

## 常见问题

### Q: 更新后仍然报错怎么办？
A: 检查以下几点：
- 确认人大金仓驱动jar包已正确加载
- 验证数据库连接URL格式正确
- 检查数据库服务器是否可访问
- 确认用户名密码正确

### Q: 如何回滚配置？
A: 使用备份表恢复：
```sql
DELETE FROM datasource_info;
INSERT INTO datasource_info SELECT * FROM datasource_info_backup;
```

### Q: 多个微服务如何处理？
A: 需要更新所有相关微服务的数据源配置，可以通过 `service_name` 字段筛选特定服务的配置。

## 联系支持

如果在迁移过程中遇到问题，请提供以下信息：
1. 错误日志详情
2. 数据库配置查询结果
3. 应用配置文件
4. 人大金仓数据库版本信息

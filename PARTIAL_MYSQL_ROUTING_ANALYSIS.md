# 部分功能找MySQL问题分析与解决方案

## 🎯 问题现象确认

您描述的现象完全正确：
- ✅ **部分功能正常**：可以走人大金仓数据库
- ❌ **部分功能异常**：仍然尝试寻找MySQL驱动
- ✅ **@DS注解有效**：加上@DS注解可以强制走人大金仓

## 🔍 问题根源分析

### 动态数据源路由机制

```mermaid
graph TD
    A[方法调用] --> B{是否有@DS注解?}
    B -->|有| C[使用指定数据源]
    B -->|无| D[使用默认路由策略]
    D --> E[tenantId + 'w']
    E --> F{查找数据源}
    F -->|YAML配置| G[人大金仓✅]
    F -->|数据库配置| H[MySQL❌]
    C --> I[成功连接]
    G --> I
    H --> J[驱动加载失败]
```

### 关键代码分析

#### 1. 默认路由逻辑（无@DS注解）
```java
// TenantDynamicDataSourceInterceptor.java:137
groupKey = tenantId + DataSourceType.W;  // W = "w"
```

#### 2. @DS注解路由逻辑
```java
// TenantDynamicDataSourceInterceptor.java:133
groupKey = tenantId + ds.value();  // 使用指定的数据源
```

#### 3. 数据源加载失败点
```java
// TenantDynamicDataSourceProvider.java:70
Class.forName(driverClassName);  // 尝试加载MySQL驱动
```

## 🎯 问题定位

### 数据源配置层次

1. **YAML配置数据源**（已正确配置人大金仓）
   - 用于@DS注解指定的数据源
   - 配置位置：`spring.datasource.dynamic.datasource.xxx`

2. **数据库存储数据源**（仍然是MySQL配置）
   - 用于默认路由（无@DS注解时）
   - 存储位置：`datasource_info` 表

### 路由Key规则

- **无@DS注解**：`tenantId + "w"` → 查找数据库中wrflg='w'的数据源
- **有@DS注解**：`tenantId + ds.value()` → 查找指定的数据源

## 🛠️ 解决方案

### 步骤1：诊断当前状态

执行诊断脚本：
```bash
mysql -u username -p database < cloud-common/tds-base-dynamic-starter/DB/diagnose_datasource_routing.sql
```

重点关注：
- 写数据源(wrflg='w')的驱动类型
- 租户路由key的分布情况

### 步骤2：执行修复

```bash
mysql -u username -p database < cloud-common/tds-base-dynamic-starter/DB/fix_partial_mysql_routing.sql
```

### 步骤3：验证修复

1. **检查数据库配置**：
```sql
SELECT tenantId, wrflg, name, driver_class_name, 
       CONCAT(tenantId, name) as routing_key
FROM tenant_datasource_rel t
LEFT JOIN datasource_info d ON t.datasource_id = d.id
WHERE wrflg = 'w';
```

2. **重启应用服务**

3. **测试功能**：
   - 测试无@DS注解的方法
   - 测试有@DS注解的方法
   - 确认不再出现MySQL驱动错误

## 🔧 临时解决方案

如果不能立即修改数据库配置，可以考虑：

### 方案1：添加@DS注解
为所有数据库操作方法添加@DS注解：
```java
@DS("your-kingbase-datasource-name")
public void yourMethod() {
    // 数据库操作
}
```

### 方案2：配置默认数据源
在YAML中配置默认的写数据源：
```yaml
spring:
  datasource:
    dynamic:
      primary: tenant
      datasource:
        tenant:
          driver-class-name: com.kingbase8.Driver
          url: ***********************************
          username: your-username
          password: your-password
```

## 📋 验证清单

- [ ] 诊断脚本执行完成，确认问题数据源
- [ ] 备份数据库配置
- [ ] 执行修复脚本
- [ ] 验证所有MySQL驱动已更新为人大金仓驱动
- [ ] 重启应用服务
- [ ] 测试无@DS注解的功能正常
- [ ] 测试有@DS注解的功能正常
- [ ] 确认不再出现MySQL驱动错误

## 🚨 注意事项

1. **备份重要性**：修复前务必备份数据库
2. **分批验证**：建议先在测试环境验证
3. **监控日志**：修复后密切关注应用日志
4. **回滚准备**：准备好回滚脚本以防万一

## 📞 技术支持

如果修复过程中遇到问题，请提供：
1. 诊断脚本的执行结果
2. 应用启动日志
3. 具体的错误信息
4. 租户ID和服务名称

package com.qm.common.uiep.mp.pagination;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Getter;
import lombok.Setter;
import net.sf.jsqlparser.statement.select.SelectItem;

import java.util.List;
import java.util.Map;

/**
 * 自定义分页信息
 *
 * @param <T> 查询的实体类
 */
public class UiepPage<T> extends Page<T> {
    /**
     * 未使用高级过滤功能情况下的总记录数。
     * <p>
     * 在未使用表格过滤的情况下{@link Page&lt;T&gt;#total}与{@link #totalAll}是相同的；
     * 在使用表格过滤的情况下二者是不相等的。
     */
    @Setter
    @Getter
    private long totalAll;

    @Setter
    @Getter
    private List<SelectItem> aggregateItems;

    @Setter
    @Getter
    private Map aggregateResult;

    /**
     * 设置分页信息
     *
     * @param pageInfo 分页信息
     * @return 分页信息对象
     */
    public UiepPage<T> setPageInfo(IPage<T> pageInfo) {
        this.setCurrent(pageInfo.getCurrent());
        this.setPages(pageInfo.getPages());
        this.setRecords(pageInfo.getRecords());
        this.setSize(pageInfo.getSize());
        this.setTotal(pageInfo.getTotal());

        //如果未设置过总记录数，则用当前查询出来的总记录数
        if (this.totalAll <= 0) {
            this.totalAll = this.getTotal();
        }
        return this;
    }

    /**
     * 将结果设置到JsonResult对象中
     *
     * @param jsonResultObj json返回对象
     * @return 调整后的json返回对象
     *    public JsonResultObj<T> copy2JsonResult(JsonResultObj<T> jsonResultObj) {
     *        // 完整性校验
     *        if (jsonResultObj == null) {
     *            jsonResultObj = new JsonResultObj<>();
     *        }
     *
     *        // 设置分页数据的返回结果
     *        jsonResultObj.setCount(new Long(this.getTotal()).intValue());
     *        jsonResultObj.setDataList(this.getRecords());
     *        jsonResultObj.setTotalCount(new Long(this.getTotalAll()).intValue());
     *
     *        return jsonResultObj;
     *    }
     *
     */
}

package com.qm.common.uiep.mp.service.impl;

import com.baomidou.mybatisplus.core.conditions.ISqlSegment;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.segments.OrderBySegmentList;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qm.common.uiep.mp.pagination.UiepPage;
import com.qm.common.uiep.table.domain.*;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义Mybatis Plus的ServiceImpl基类
 *
 * <AUTHOR>
 * @param <M> 对应数据层Mapper
 * @param <T> 对应业务实体
 */
public class UiepBaseServiceImpl<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> implements IService<T> {


    /**
     * 附带表格各种功能（排序、过滤、分页等）的查询
     *
     * @param queryWrapper 查询条件构造器
     * @param tableInfo    表格信息
     * @return 查询结果
     */
    public UiepPage<T> table(QueryWrapper<T> queryWrapper, TableInfo tableInfo) {
        UiepPage<T> queryPage = new UiepPage<>();

        //未进行表格高级过滤前的总记录数
        if (tableInfo != null && !tableInfo.getFilterInfo().isEmpty()) {
            /*
              只有当有高级过滤信息的情况下，才需要自定义计算；
              其他情况使用默认值即可（默认值为QmPage.total）
             */
            queryPage.setTotalAll(this.count(queryWrapper));
        }

        //添加表格附加的高级信息（过滤、排序等）
        appendTableAdditional(queryWrapper, tableInfo);

        //分页信息
        TablePageInfo pageInfo = tableInfo == null ? new TablePageInfo() : tableInfo.getPageInfo();

        if (pageInfo.getIndex() <= 0) {
            queryPage.setCurrent(1);
        } else {
            queryPage.setCurrent(pageInfo.getIndex());
        }
        queryPage.setSize(pageInfo.getSize());
        queryPage.setSearchCount(tableInfo.isSearchCount());
        queryPage.setAggregateItems(tableInfo.getAggregateItems());

        //查询数据
        if (pageInfo.isNeedPage()) {
            //分页模式查询
            queryPage.setPageInfo(baseMapper.selectPage(queryPage, queryWrapper));
        } else {
            //非分页模式查询
            queryPage.setRecords(baseMapper.selectList(queryWrapper));
            queryPage.setTotal(queryPage.getRecords().size());
            //如果“未表格高级过滤前的总记录数”为0则使用默认值QmPage.total。
            if (queryPage.getTotalAll() <= 0) {
                queryPage.setTotalAll(queryPage.getTotal());
            }
        }

        return queryPage;
    }

    /**
     * 添加表格附加的高级信息（过滤、排序等）
     *
     * @param queryWrapper 查询构造器
     * @param tableInfo    表格附加的高级信息
     * @return 查询构造器
     */
    public QueryWrapper<T> appendTableAdditional(QueryWrapper<T> queryWrapper, TableInfo tableInfo) {
        // 兼容null值
        if (tableInfo == null) {
            tableInfo = new TableInfo();
        }

        // 排序信息。将前台传递过来的排序信息放到业务代码排序键之前
        appendTableOrderAdditional(queryWrapper, tableInfo.getSortInfo());

        // 老版过滤信息
        appendTableFilterAdditional(queryWrapper, tableInfo.getFilterInfo());

        // 新版过滤信息
        appendTableWhereAdditional(queryWrapper, tableInfo.getWhereInfo());

        // 分组聚合信息
        appendTableGroupByAdditional(queryWrapper, tableInfo.getGroupInfo());
        return queryWrapper;
    }

    /**
     * 添加分组聚合条件
     *
     * @param queryWrapper
     * @param groupInfo
     * @return
     */
    private QueryWrapper<T> appendTableGroupByAdditional(QueryWrapper<T> queryWrapper, TableGroupInfo groupInfo) {
        if (!groupInfo.isEmpty()) {
            // 设置分组信息
            queryWrapper.groupBy(String.valueOf(groupInfo.getGroupBy().toArray(new String[groupInfo.getGroupBy().size()])));

            // 设置聚合信息。先加上分组列，再加上聚合列。
            List<String> selectSql = new ArrayList<>();
            groupInfo.getGroupBy().forEach(x -> {
                if (!StringUtils.isEmpty(x)) {
                    selectSql.add(x);
                }
            });
            groupInfo.getSummaries().forEach(x -> {
                if (x != null && !StringUtils.isEmpty(x.getSql())) {
                    selectSql.add(x.getSql());
                }
            });
            queryWrapper.select(selectSql.toArray(new String[selectSql.size()]));
        }
        return queryWrapper;
    }

    /**
     * 将前台传递过来的排序信息放到业务代码排序键之前
     *
     * @param queryWrapper 查询构造器
     * @param sortList     排序信息
     * @return 附加排序信息后的查询构造器
     */
    private QueryWrapper<T> appendTableOrderAdditional(QueryWrapper<T> queryWrapper, List<TableSortInfo> sortList) {
        OrderBySegmentList orderSegList = queryWrapper.getExpression().getOrderBy();
        if (!orderSegList.isEmpty()) {
            appendTableOrderAdditionalInsert(queryWrapper, sortList);
        } else {
            appendTableOrderAdditionalAppend(queryWrapper, sortList);
        }
        return queryWrapper;
    }

    /**
     * 将排序信息添加到原有的前面
     *
     * @param queryWrapper 查询构造器
     * @param sortList     排序信息
     */
    private void appendTableOrderAdditionalInsert(QueryWrapper<T> queryWrapper, List<TableSortInfo> sortList) {
        List<ISqlSegment> orderByList = new ArrayList<>();
        if(!sortList.isEmpty()){
            ISqlSegment tt = queryWrapper.getExpression().getOrderBy().get(0);
            //清空原来排序
            queryWrapper.getExpression().getOrderBy().clear();
            // 组装前台传递过来的排序信息
            for (TableSortInfo sortInfo : sortList) {
                String orderFieldName = sortInfo.getFieldName();
               /* if (!orderByList.isEmpty()) {
                    orderByList.add(() -> ",");
                }*/
                /*orderByList.add(() -> orderFieldName);
                orderByList.add(sortInfo.isAsc() ? SqlKeyword.ASC : SqlKeyword.DESC);*/

                queryWrapper.orderBy(true, sortInfo.isAsc(), orderFieldName);
            }
            queryWrapper.getExpression().getOrderBy().add(tt);//把原来排序拼接到最后面
        }
        /*// 如果之前有order by字段，则需要多拼接一个逗号
        if (!orderByList.isEmpty() && !queryWrapper.getExpression().getOrderBy().isEmpty()) {
            orderByList.add(() -> ",");
        }*/
       /* // 将前台传递过来的排序键放在业务排序之前
        if (!orderByList.isEmpty()) {
            queryWrapper.getExpression().getOrderBy().addAll(0, orderByList);
        }*/
    }

    /**
     * 将排序信息添加到后面
     *
     * @param queryWrapper 查询构造器
     * @param sortList     排序信息
     */
    private void appendTableOrderAdditionalAppend(QueryWrapper<T> queryWrapper, List<TableSortInfo> sortList) {
        for (TableSortInfo sortInfo : sortList) {
            queryWrapper.orderBy(true, sortInfo.isAsc(), sortInfo.getFieldName());
        }
    }

    /**
     * 查询构造器中附加过滤条件（旧版条件）
     *
     * @param queryWrapper 查询构造器
     * @param filterList   附加过滤条件
     * @return 附加过滤条件后的查询构造器
     */
    private QueryWrapper<T> appendTableFilterAdditional(QueryWrapper<T> queryWrapper, List<TableFilterInfo> filterList) {
        for (TableFilterInfo filterInfo : filterList) {
            String vColName = filterInfo.getFieldName();
            String vValue = filterInfo.getFieldValue();
            switch (filterInfo.getAction()) {
                case "em":
                    queryWrapper.isNull(vColName);
                    break;
                case "nem":
                    queryWrapper.isNotNull(vColName);
                    break;
                case "eq":
                    queryWrapper.eq(vColName, vValue);
                    break;
                case "ne":
                    queryWrapper.ne(vColName, vValue);
                    break;
                case "gt":
                    queryWrapper.gt(vColName, vValue);
                    break;
                case "ge":
                    queryWrapper.ge(vColName, vValue);
                    break;
                case "lt":
                    queryWrapper.lt(vColName, vValue);
                    break;
                case "le":
                    queryWrapper.le(vColName, vValue);
                    break;
                case "lk":
                    // "lk": "like", // 类似，[like] 大小写敏感 暂未考虑跨库写法。
                    queryWrapper.apply(" lower(" + vColName + ") " + " like LOWER( {0} )", "%" + vValue + "%");
                    break;
                case "ln":
                    // "ln": "linkNoMatch", // 类似且无须大小写匹配，[like and needn't match case]
                    queryWrapper.like(vColName, vValue);
                    break;
                case "sw":
                    queryWrapper.likeRight(vColName, vValue);
                    break;
                case "ew":
                    queryWrapper.likeLeft(vColName, vValue);
                    break;
                case "mt":
                    // 多条件
                    appendTableFilterAdditionalMT(queryWrapper, vColName, vValue);
                    break;
                case "in":
                    // "in": "in", // 在集合内（defaultValidater方法不支持此比较符）
                    appendTableFilterAdditionalIN(queryWrapper, vColName, vValue);
                    break;
                default:
                    /*
                     "mt": "match", // 匹配，[match] （要求比较值是正则表达式）
                     "nsw": "notStartWith", // 不起始于，[not start with]
                     "new": "notEndWith", // 终止于，[not end with]
                     "ct": "containsItem" //
                     值里包含集合内的元素，[contains]（defaultValidater方法不支持此比较符）
                     */
                    //log.info("---error--"+"不支持表格过滤条件[" + filterInfo.getAction() + "]");
                    break;
            }
        }

        return queryWrapper;
    }

    /**
     * 多条件，匹配任何模式p1或p2或p3（p1 | p2 | p3）/d|c/i 暂未考虑跨库写法
     *
     * @param queryWrapper 查询构造器
     * @param vColName     列名
     * @param vValue       查询值
     */
    private void appendTableFilterAdditionalMT(QueryWrapper<T> queryWrapper, String vColName, String vValue) {
        if (vValue != null) {
            if (vValue.startsWith("/")) {
                vValue = vValue.substring(1);
            }
            if (vValue.endsWith("/i")) {
                // 不区分大小写
                vValue = vValue.substring(0, vValue.length() - 2);
            } else if (vValue.endsWith("/")) {
                // 区分大小写
                vValue = vValue.substring(0, vValue.length() - 1);
            }
            queryWrapper.apply(vColName + " REGEXP {0} ", vValue.trim());
        }
    }

    /**
     * In 条件，在集合内（defaultValidater方法不支持此比较符）
     *
     * @param queryWrapper 查询构造器
     * @param vColName     列名
     * @param vValue       查询值
     */
    private void appendTableFilterAdditionalIN(QueryWrapper<T> queryWrapper, String vColName, String vValue) {
        if (vValue != null && vValue.split("#").length > 0) {
            queryWrapper.in(vColName, vValue.split("#"));
        }
    }

    /**
     * 查询构造器中附加过滤条件（新版条件）
     *
     * @param queryWrapper 查询构造器
     * @param whereList    附加过滤条件
     * @return 附加过滤条件后的查询构造器
     */
    private QueryWrapper<T> appendTableWhereAdditional(QueryWrapper<T> queryWrapper, List<TableWhereInfo> whereList) {
        boolean exitFlag = false;
        for (int i = 0; i < whereList.size(); ) {
            /*
              先入先出原则，从wherelist中获取条件对象，并补充到QueryWrapper中。
              正常来说应该写一个 Queue 实现类处理，而不是通过for+remove来处理。
             */
            TableWhereInfo whereInfo = whereList.get(i);
            whereList.remove(whereInfo);//先入先出原则从队列中移除

            switch (whereInfo.getAction()) {
                case TableWhereInfo.J_AND:
                    // and条件什么都不需要做
                    break;
                case TableWhereInfo.J_OR:
                    queryWrapper.or();
                    break;
                case TableWhereInfo.KUOHAO_L:
                    queryWrapper.nested(q -> appendTableWhereAdditional(q, whereList));
                    break;
                case TableWhereInfo.KUOHAO_R:
                    // 跳出循环
                    exitFlag = true;
                    break;
                case TableWhereInfo.O_GT:
                    queryWrapper.gt(whereInfo.getFieldName(), whereInfo.getFieldValue());
                    break;
                case TableWhereInfo.O_GE:
                    queryWrapper.ge(whereInfo.getFieldName(), whereInfo.getFieldValue());
                    break;
                case TableWhereInfo.O_LT:
                    queryWrapper.lt(whereInfo.getFieldName(), whereInfo.getFieldValue());
                    break;
                case TableWhereInfo.O_LE:
                    queryWrapper.le(whereInfo.getFieldName(), whereInfo.getFieldValue());
                    break;
                case TableWhereInfo.O_EQ:
                    queryWrapper.eq(whereInfo.getFieldName(), whereInfo.getFieldValue());
                    break;
                case TableWhereInfo.O_NE:
                    queryWrapper.ne(whereInfo.getFieldName(), whereInfo.getFieldValue());
                    break;
                case TableWhereInfo.O_LK:
                    queryWrapper.like(whereInfo.getFieldName(), whereInfo.getFieldValue());
                    break;
                case TableWhereInfo.O_NLIKE:
                    queryWrapper.notLike(whereInfo.getFieldName(), whereInfo.getFieldValue());
                    break;
                case TableWhereInfo.O_IN:
                    queryWrapper.in(whereInfo.getFieldName(), whereInfo.getFieldValueList());
                    break;
                case TableWhereInfo.O_NIN:
                    queryWrapper.notIn(whereInfo.getFieldName(), whereInfo.getFieldValueList());
                    break;
                default:
                    //log.info("---error--"+"不支持表格过滤子条件[" + whereInfo.getWhere() + "]");
                    break;
            }

            // 需要跳出循环
            if (exitFlag) {
                break;
            }
        }
        return queryWrapper;
    }
}
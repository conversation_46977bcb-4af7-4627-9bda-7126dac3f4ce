package com.qm.common.uiep.table.domain;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 表格过滤条件
 */
@Slf4j
@Data
public class TableFilterInfo {
    /**
     * 列名
     */
    private String fieldName;
    /**
     * 列值
     */
    private String fieldValue;

    /**
     * 比较运算符
     * "em": "isEmpty", // 为空，[empty]
     * "nem": "isNotEmpty", // 不为空，[not empty]
     * "eq": "==", // 等于，= [equals]
     * "ne": "!=", // 不等于，!= [not equals]
     * "gt": ">", // 大于，> [great than]
     * "ge": ">=", // 大于等于，>= [great than or equals]
     * "lt": "<", // 小于，< [less than]
     * "le": "<=", // 小于等于，<= [less than or equals]
     * "lk": "like", // 类似，[like]
     * "ln": "linkNoMatch", // 类似且无须大小写匹配，[like and needn't match case]
     * "mt": "match", // 匹配，[match] （要求比较值是正则表达式）
     * "sw": "startWith", // 起始于，[start with]
     * "nsw": "notStartWith", // 不起始于，[not start with]
     * "ew": "endWith", // 终止于，[end with]
     * "new": "notEndWith", // 终止于，[not end with]
     * "in": "in", // 在集合内（defaultValidater方法不支持此比较符）
     * "ct": "containsItem" // 值里包含集合内的元素，[contains]（defaultValidater方法不支持此比较符）
     */
    private String action;

    /**
     * 单一条件过滤类型翻译
     *
     * @return 翻译对照表
     */
    private static Map<String, String> getActionMap() {
        Map<String, String> actionMap = new HashMap<>();
        actionMap.put("text", "lk");
        actionMap.put("checkbox", "in");
        actionMap.put("radio", "eq");
        actionMap.put("number", "eq");
        actionMap.put("date", "eq");
        return actionMap;
    }

    /**
     * 范围条件过滤类型
     *
     * @return 类型清单
     */
    private static List<String> getActionBetween() {
        List<String> actionList = new ArrayList<>();
        actionList.add("range-date");
        actionList.add("range-number");
        return actionList;
    }

    /**
     * 将字符串转换成过滤条件
     *
     * @param fileterMap 过滤信息。示例数据：
     *                   {
     *                   "name|text": "姓名",
     *                   "createOn|range-date": "2020-05-11#2020-05-20"
     *                   }
     * @return 过滤条件
     */
    public static List<TableFilterInfo> parser(Map<String, String> fileterMap) {
        List<TableFilterInfo> filterList = new ArrayList<>();

        if (fileterMap != null) {
            for (Map.Entry<String, String> item : fileterMap.entrySet()) {
                String[] vFilterKey = item.getKey().split("\\|");
                if (StringUtils.isEmpty(vFilterKey)) {
                    //Key为空不合法
                    log.info("---error--"+"过滤条件的Key为空！");
                } else if (vFilterKey.length == 1) {
                    //只有字段名，默认类型为text
                    appendActionSingle(filterList, vFilterKey[0], item.getValue(), "lk");
                } else if (vFilterKey.length == 2) {
                    //字段名+类型
                    appendActionAuto(filterList, vFilterKey[0], item.getValue(), vFilterKey[1]);
                }
            }
        }

        return filterList;
    }

    /**
     * 补充过滤条件，自动识别是单一条件还是范围条件
     *
     * @param filterList  过滤条件
     * @param vFieldName  字段名
     * @param vFieldValue 过滤条件值
     * @param vFilterType 过滤类型
     */
    private static void appendActionAuto(List<TableFilterInfo> filterList, String vFieldName, String vFieldValue, String vFilterType) {
        vFilterType = vFilterType == null ? "" : vFilterType.toLowerCase();
        Map<String, String> actionMap = getActionMap();
        List<String> actionBetweenList = getActionBetween();
        if (actionMap.containsKey(vFilterType)) {
            // 单一条件
            appendActionSingle(filterList, vFieldName, vFieldValue, actionMap.get(vFilterType));
        } else if (actionBetweenList.contains(vFilterType)) {
            // 范围条件
            appendActionBetween(filterList, vFieldName, vFieldValue);
        } else {
            log.info("---error--"+"不支持[" + vFilterType + "]过滤类型！");
        }
    }

    /**
     * 补充单一过滤条件
     *
     * @param filterList  过滤条件
     * @param vFieldName  字段名
     * @param vFieldValue 过滤条件值
     * @param vAction     过滤条件动作。getActionMap中定义的查询动作。
     */
    private static void appendActionSingle(List<TableFilterInfo> filterList, String vFieldName, String vFieldValue, String vAction) {
        TableFilterInfo filterInfo = new TableFilterInfo();
        filterInfo.setAction(vAction);
        filterInfo.setFieldName(vFieldName);
        filterInfo.setFieldValue(vFieldValue);
        filterList.add(filterInfo);
    }

    /**
     * 补充范围过滤条件
     *
     * @param filterList  过滤条件
     * @param vFieldName  字段名
     * @param vFieldValue 过滤条件值
     */
    private static void appendActionBetween(List<TableFilterInfo> filterList, String vFieldName, String vFieldValue) {
        if (!StringUtils.isEmpty(vFieldValue)) {
            String[] vValues = vFieldValue.split("#");
            if (vValues != null && vValues.length == 2) {
                TableFilterInfo filterInfoBegin = new TableFilterInfo();
                filterInfoBegin.setAction("ge");
                filterInfoBegin.setFieldName(vFieldName);
                filterInfoBegin.setFieldValue(vValues[0]);
                filterList.add(filterInfoBegin);

                TableFilterInfo filterInfoEnd = new TableFilterInfo();
                filterInfoEnd.setAction("le");
                filterInfoEnd.setFieldName(vFieldName);
                filterInfoEnd.setFieldValue(vValues[1]);
                filterList.add(filterInfoEnd);
            }
        }
    }

    /**
     * 原始函数。留作以后对比使用
     * public static List<TableFilterInfo> parser1(Map<String, String> fileterMap) {
     *     List<TableFilterInfo> filterList = new ArrayList<>();
     *     if (fileterMap != null) {
     *         for (Map.Entry<String, String> item : fileterMap.entrySet()) {
     *             String[] vFilterKey = item.getKey().split("\\|");
     *             if (StringUtils.isEmpty(vFilterKey)) {
     *                 //Key为空不合法
     *                 continue;
     *             } else if (vFilterKey.length == 1) {
     *                 //只有字段名，默认类型为text
     *                 TableFilterInfo filterInfo = new TableFilterInfo();
     *                 filterInfo.setAction("lk");
     *                 filterInfo.setFieldName(vFilterKey[0]);
     *                 filterInfo.setFieldValue(item.getValue());
     *                 filterList.add(filterInfo);
     *             } else if (vFilterKey.length == 2) {
     *                 //字段名+类型
     *                 String vFilterType = vFilterKey[1];
     *                 if ("range-date".equalsIgnoreCase(vFilterType)) {
     *                     //日期范围
     *                     if (!StringUtils.isEmpty(item.getValue())) {
     *                         String[] vValues = item.getValue().split("#");
     *                         if (vValues != null && vValues.length == 2) {
     *                             TableFilterInfo filterInfoBegin = new TableFilterInfo();
     *                             filterInfoBegin.setAction("ge");
     *                             filterInfoBegin.setFieldName(vFilterKey[0]);
     *                             filterInfoBegin.setFieldValue(vValues[0]);
     *                             filterList.add(filterInfoBegin);
     *
     *                             TableFilterInfo filterInfoEnd = new TableFilterInfo();
     *                             filterInfoEnd.setAction("le");
     *                             filterInfoEnd.setFieldName(vFilterKey[0]);
     *                             filterInfoEnd.setFieldValue(vValues[1]);
     *                             filterList.add(filterInfoEnd);
     *                         }
     *                     }
     *                 } else if ("text".equalsIgnoreCase(vFilterType)) {
     *                     TableFilterInfo filterInfo = new TableFilterInfo();
     *                     filterInfo.setAction("lk");
     *                     filterInfo.setFieldName(vFilterKey[0]);
     *                     filterInfo.setFieldValue(item.getValue());
     *                     filterList.add(filterInfo);
     *                 } else if ("checkbox".equalsIgnoreCase(vFilterType)) {
     *                     TableFilterInfo filterInfo = new TableFilterInfo();
     *                     filterInfo.setAction("in");
     *                     filterInfo.setFieldName(vFilterKey[0]);
     *                     filterInfo.setFieldValue(item.getValue());
     *                     filterList.add(filterInfo);
     *                 } else if ("radio".equalsIgnoreCase(vFilterType)) {
     *                     TableFilterInfo filterInfo = new TableFilterInfo();
     *                     filterInfo.setAction("eq");
     *                     filterInfo.setFieldName(vFilterKey[0]);
     *                     filterInfo.setFieldValue(item.getValue());
     *                     filterList.add(filterInfo);
     *                 } else if ("number".equalsIgnoreCase(vFilterType)) {
     *                     TableFilterInfo filterInfo = new TableFilterInfo();
     *                     filterInfo.setAction("eq");
     *                     filterInfo.setFieldName(vFilterKey[0]);
     *                     filterInfo.setFieldValue(item.getValue());
     *                     filterList.add(filterInfo);
     *                 } else if ("date".equalsIgnoreCase(vFilterType)) {
     *                     TableFilterInfo filterInfo = new TableFilterInfo();
     *                     filterInfo.setAction("eq");
     *                     filterInfo.setFieldName(vFilterKey[0]);
     *                     filterInfo.setFieldValue(item.getValue());
     *                     filterList.add(filterInfo);
     *                 } else if ("range-number".equalsIgnoreCase(vFilterType)) {
     *                     //数字范围
     *                     if (!StringUtils.isEmpty(item.getValue())) {
     *                         String[] vValues = item.getValue().split("#");
     *                         if (vValues != null && vValues.length == 2) {
     *                             TableFilterInfo filterInfoBegin = new TableFilterInfo();
     *                             filterInfoBegin.setAction("ge");
     *                             filterInfoBegin.setFieldName(vFilterKey[0]);
     *                             filterInfoBegin.setFieldValue(vValues[0]);
     *                             filterList.add(filterInfoBegin);
     *
     *                             TableFilterInfo filterInfoEnd = new TableFilterInfo();
     *                             filterInfoEnd.setAction("le");
     *                             filterInfoEnd.setFieldName(vFilterKey[0]);
     *                             filterInfoEnd.setFieldValue(vValues[1]);
     *                             filterList.add(filterInfoEnd);
     *                         }
     *                     }
     *                 }
     *             }
     *         }
     *     }
     *     return filterList;
     * }
     */
}

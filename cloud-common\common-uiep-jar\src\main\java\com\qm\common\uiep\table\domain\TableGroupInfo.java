package com.qm.common.uiep.table.domain;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 分组聚合信息
 */
@Slf4j
public class TableGroupInfo {
    /**
     * 分割字符
     */
    private static final String SPLIT_CHAR = ",";
    /**
     * 分组信息
     */
    @Getter
    private List<String> groupBy;

    /**
     * 聚合信息
     */
    @Getter
    private List<TableSummary> summaries;

    /**
     * 是否存在分组聚合信息
     *
     * @return true, 存在分组聚合信息；false, 不存在；
     */
    public boolean isEmpty() {
        // 异常数据给出warning信息。
        if (CollectionUtils.isEmpty(groupBy) && !CollectionUtils.isEmpty(summaries)) {
            log.warn("[group by]为空，但是[summaries]不为空！");
        } else if (!CollectionUtils.isEmpty(groupBy) && CollectionUtils.isEmpty(summaries)) {
            log.warn("[group by]不为空，但是[summaries]为空！");
        }

        // 分组和聚合信息同时不为空，才认为有分组聚合信息
        if (CollectionUtils.isEmpty(groupBy) || CollectionUtils.isEmpty(summaries)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 分组聚合信息
     *
     * @param groupBy   分组信息。<tt>vMasterNO,vSupplierSpa</tt>
     * @param summaries 聚合信息。<tt>count|vSpa,sum|nSalPrice</tt>
     */
    private TableGroupInfo(List<String> groupBy, List<TableSummary> summaries) {
        this.groupBy = groupBy;
        this.summaries = summaries;
    }

    /**
     * 解析分组聚合信息
     *
     * @param groupBy   分组信息。vMasterNO,vSupplierSpa
     * @param summaries 聚合信息
     * @return 分组聚合信息
     */
    public static TableGroupInfo parser(String groupBy, String summaries) {
        // 解析分组信息
        List<String> groupByList = null;
        if (!StringUtils.isEmpty(groupBy)) {
            groupByList = Arrays.asList(groupBy.split(SPLIT_CHAR));
        }

        // 解析聚合信息
        List<TableSummary> summaryList = null;
        if (!StringUtils.isEmpty(summaries)) {
            summaryList = new ArrayList<>();
            for (String s : summaries.split(SPLIT_CHAR)) {
                if (!StringUtils.isEmpty(s)) {
                    summaryList.add(TableSummary.parser(s));
                }
            }
        }

        return new TableGroupInfo(groupByList, summaryList);
    }
}

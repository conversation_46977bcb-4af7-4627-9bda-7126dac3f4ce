package com.qm.common.uiep.table.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import net.sf.jsqlparser.statement.select.SelectItem;

import java.util.HashMap;
import java.util.List;

/**
 * QM表格附加信息
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TableInfo {
    /**
     * 排序信息
     */
    private String sort;
    /**
     * 过滤条件信息
     */
    private HashMap<String, String> filter;
    /**
     * 过滤条件信息
     */
    private String where;
    /**
     * 分组聚合中统计信息
     */
    private String summary;
    /**
     * 分组聚合中分组信息
     */
    private String group;
    /**
     * 每页记录数
     */
    private long pageSize;
    /**
     * 当前页码
     */
    private long pageIndex;

    /**
     * 是否查询总条数
     */
    private boolean isSearchCount;

    /**
     * 指定追加聚合函数
     */
    private List<SelectItem> aggregateItems;

    /**
     * 获取过滤条件信息列表
     *
     * @return 过滤条件信息列表
     */
    public List<TableFilterInfo> getFilterInfo() {
        return TableFilterInfo.parser(filter);
    }

    /**
     * 获取过滤条件信息列表
     *
     * @return 过滤条件信息列表
     */
    public List<TableWhereInfo> getWhereInfo() {
        return TableWhereInfo.parser(where);
    }

    /**
     * 获取排序信息列表
     *
     * @return 排序信息列表
     */
    public List<TableSortInfo> getSortInfo() {
        return TableSortInfo.parser(sort);
    }

    /**
     * 获取分组聚合信息
     *
     * @return 分组聚合信息
     */
    public TableGroupInfo getGroupInfo() {
        return TableGroupInfo.parser(this.group, this.summary);
    }

    /**
     * 获取分页信息
     *
     * @return 分页信息
     */
    public TablePageInfo getPageInfo() {
        if (pageSize <= 0) {
            return new TablePageInfo();
        } else {
            return new TablePageInfo(pageSize, pageIndex);
        }
    }
}

package com.qm.common.uiep.table.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 表格分页信息
 */
@Slf4j
public class TablePageInfo {

    /**
     * 每页记录数
     */
    @Setter
    @Getter
    private long size;
    /**
     * 当前页码
     */
    @Setter
    @Getter
    private long index;

    /**
     * 是否需要做分页
     */
    @Getter
    private boolean needPage;

    /**
     * 表格分页信息。
     * 默认当前第一页，每页99999条记录，也就是相当于不分页。
     */
    public TablePageInfo() {
        this.size = 99999;
        this.index = 1;
        this.needPage = false;
    }

    /**
     * 表格分页信息
     *
     * @param size    每页记录数
     * @param current 当前页码
     */
    public TablePageInfo(long size, long current) {
        this.size = size;
        this.index = current;
        this.needPage = true;
    }

    /**
     * 将字符串转换为分页信息
     *
     * @param vRangeStr 查询记录范围(起始行，截止行)
     * @param pageIndex 当前页码
     * @return 分页信息
     */
    public static TablePageInfo parser(String vRangeStr, long pageIndex) {
        TablePageInfo pageInfo = null;
        long pageSize = 0;

        //解析每页记录数
        if (!StringUtils.isEmpty(vRangeStr)) {
            try {
                String[] vRangeList = vRangeStr.split(",");
                if (vRangeList != null && vRangeList.length == 2) {
                    long nBegin = 0;
                    long nEnd = 0;
                    nBegin = Long.parseLong(vRangeList[0]);
                    nEnd = Long.parseLong(vRangeList[1]);
                    pageSize = nEnd - nBegin + 1;
                    if (pageSize <= 0) {
                        log.warn("解析表格分页信息1[" + vRangeStr + "]不正确！");
                    }
                } else {
                    log.warn("解析表格分页信息2[" + vRangeStr + "]不正确！");
                }
            } catch (Exception ex) {
                log.info("---error--"+"解析表格分页信息3[" + vRangeStr + "]异常！" + ex.getMessage(), ex);
            }
        }

        //创建表格分页信息对象
        if (pageIndex >= 0 && pageSize > 0) {
            //分页模式
            pageInfo = new TablePageInfo(pageSize, pageIndex + 1);
        } else {
            //非分页模式
            pageInfo = new TablePageInfo();
        }

        return pageInfo;
    }
}

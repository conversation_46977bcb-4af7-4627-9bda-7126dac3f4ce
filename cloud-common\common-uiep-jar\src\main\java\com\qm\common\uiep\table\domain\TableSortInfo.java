package com.qm.common.uiep.table.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 表格排序信息
 */
@Slf4j
@Data
@AllArgsConstructor
public class TableSortInfo {

    /**
     * 列名
     */
    private String fieldName;

    /**
     * 是否为Asc
     */
    private boolean isAsc = true;

    /**
     * 表格排序信息。排序条件为asc。
     *
     * @param fieldName 列名
     */
    public TableSortInfo(String fieldName) {
        this(fieldName, true);
    }

    /**
     * 将字符串转换成排序信息
     *
     * @param vSortStr 排序字符串。示例数据：name|ascend,createOn|descend
     * @return 排序信息
     */
    public static List<TableSortInfo> parser(String vSortStr) {
        List<TableSortInfo> sortList = new ArrayList<>();

        if (!StringUtils.isEmpty(vSortStr)) {
            String[] vSortList = vSortStr.split(",");
            for (String vSortItem : vSortList) {
                if (StringUtils.isEmpty(vSortItem)) {
                    //异常数据，无需添加
                } else if (vSortItem.endsWith("|descend")) {
                    sortList.add(new TableSortInfo(vSortItem.replaceAll("\\|descend", ""), false));
                } else if (vSortItem.endsWith("|ascend")) {
                    sortList.add(new TableSortInfo(vSortItem.replaceAll("\\|ascend", ""), true));
                } else {
                    sortList.add(new TableSortInfo(vSortItem, true));
                }
            }
        }

        return sortList;
    }
}

package com.qm.common.uiep.table.domain;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 聚合类
 */
@Slf4j
public class TableSummary {
    private static final String SPLIT_CHAR = "\\|";
    @Getter
    private String col;
    @Getter
    private String fun;
    @Getter
    private String sql;

    public static final String RECORDS_COUNT = "nRecordsCount";

    private TableSummary(String col, String fun) {
        this.fun = fun;
        this.col = col;
        StringBuilder sb = new StringBuilder();
        sb.append(this.fun).append("(").append(this.col).append(") as ");
        if (col.equals("*"))
            sb.append(RECORDS_COUNT);
        else
            sb.append(this.col);
        this.sql = sb.toString();
    }

    /**
     * 创建聚合类
     *
     * @param summaries 前台表格的聚合字符串
     * @return 聚合类。如果转换失败则返回null。
     */
    public static TableSummary parser(String summaries) {
        TableSummary ret = null;
        String[] tmpList = summaries.split(SPLIT_CHAR);
        if (tmpList == null || tmpList.length != 2) {
            log.warn("无效聚合信息[{}]！", summaries);
        } else {
            String fun = tmpList[0];
            String col = tmpList[1];
            if ("sum".equalsIgnoreCase(fun) || "max".equalsIgnoreCase(fun) || "min".equalsIgnoreCase(fun)
                    || "avg".equalsIgnoreCase(fun) || "count".equalsIgnoreCase(fun)) {
                ret = new TableSummary(col, fun);
            } else {
                log.warn("无效聚合函数[{}]！", fun);
            }
        }
        return ret;
    }
}

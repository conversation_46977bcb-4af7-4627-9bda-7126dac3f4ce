package com.qm.common.uiep.table.domain;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 表格过滤条件
 */
@Data
@Slf4j
public class TableWhereInfo {
    /**
     * 列名
     */
    private String fieldName;
    /**
     * 列值
     */
    private Object fieldValue;
    /**
     * 列值
     */
    private List<Object> fieldValueList;

    /**
     * 比较运算符。
     */
    private String action;

    private String where;

    public static final String J_AND = " and ";
    public static final String J_OR = " or ";
    public static final String KUOHAO_L = "(";
    public static final String KUOHAO_R = ")";

    public static final String O_GT = " > ";
    public static final String O_GE = " >= ";
    public static final String O_LT = " < ";
    public static final String O_LE = " <= ";
    public static final String O_EQ = " == ";
    public static final String O_NE = " != ";
    public static final String O_LK = " like ";
    public static final String O_IN = " in ";
    public static final String O_NIN = " nin ";
    public static final String O_NLIKE = " nlike ";

    public TableWhereInfo(String where) {
        this.where = where;
        this.fieldValueList = new ArrayList<>();
        this.convert();
    }

    private void convert() {
        if (J_AND.equals(where) || J_OR.equals(where) || KUOHAO_L.equals(where) || KUOHAO_R.equals(where)) {
            this.action = where;
        } else {
            List<String> oprList = new ArrayList<>();
            oprList.add(O_GT);
            oprList.add(O_GE);
            oprList.add(O_LT);
            oprList.add(O_LE);
            oprList.add(O_EQ);
            oprList.add(O_NE);
            oprList.add(O_LK);
            oprList.add(O_IN);
            oprList.add(O_NIN);
            oprList.add(O_NLIKE);
            for (String opr : oprList) {
                String[] tmp = where.split(opr);
                if (tmp == null) {
                    // 什么都不处理
                } else if (tmp.length > 1) {
                    this.init(tmp[0], opr, tmp[1]);
                    break;
                } else if (tmp.length == 1 && where.endsWith(opr)) {
                    // 值部分为空，相当于输入的值为空
                    this.init(tmp[0], opr, "");
                    break;
                }
            }
        }
    }

    private void init(String fieldName, String action, String fieldValue) {
        this.fieldName = fieldName;
        this.action = action;
        String tmpFieldValue = fieldValue;
        if (isSingelValue(tmpFieldValue)) {
            // 去掉左右单引号后保存为值
            this.fieldValue = tmpFieldValue.substring(1, tmpFieldValue.length() - 1);
        } else if (isMutilValue(tmpFieldValue)) {
            // 转换成数组
            appendMutilValue(tmpFieldValue);
        } else {
            // 直接保存为值
            this.fieldValue = fieldValue;
        }
    }

    /**
     * 是否为单引号引起来的值
     *
     * @param tmpFieldValue 字段值
     * @return true：是；false：否
     */
    private boolean isSingelValue(String tmpFieldValue) {
        return tmpFieldValue != null && tmpFieldValue.startsWith("'") && tmpFieldValue.endsWith("'");
    }

    /**
     * 是否为范围值
     *
     * @param tmpFieldValue 字段值
     * @return true：是；false：否
     */
    private boolean isMutilValue(String tmpFieldValue) {
        return tmpFieldValue != null && tmpFieldValue.trim().startsWith("[") && tmpFieldValue.trim().endsWith("]");
    }

    /**
     * 添加范围条件
     *
     * @param tmpFieldValue 字段值
     */
    private void appendMutilValue(String tmpFieldValue) {
        String[] tmpList = tmpFieldValue.substring(tmpFieldValue.indexOf('[') + 1, tmpFieldValue.lastIndexOf(']')).split(",");
        for (String item : tmpList) {
            if (isSingelValue(item.trim())) {
                this.fieldValueList.add(item.substring(item.indexOf('\'') + 1, item.lastIndexOf('\'')));
            } else {
                this.fieldValueList.add(item);
            }
        }
    }

    /**
     * @param whereStr 过滤信息。示例数据：
     *                 dstop > '2020-07-15' and dstop < '2020-07-16' and vsex in ['1','2'] and vrealName like ''a'a' or vpersoncode like '11' and ( vrealName like 'uu' or vstop in ['1'] )
     *                 vpersoncode like '111' and vpersoncode == '333' and vpersoncode != '444' and dstop < '2020-07-09' and dstop <= '2020-07-22' and dstop > '2020-07-06' and dstop >= '2020-07-22' and dstop == '2020-07-28' and dstop != '2020-07-28' and vsex in ['0','1'] and vsex nin ['0','1'] and vsex == '0' and vsex != '1'  and ( vrealName like 'uu' or vstop in ['1'] and ( vpersoncode like 'uu' and vstop in ['2'] ) )
     *                 dstop > '2020-07-15' and vsex in ['1','2'] and vrealName like ''a'a' or vpersoncode like '11' and (( vrealName like 'uu' and vsex in ['2'] ) and vrealName like 'uu' or vstop in ['1'] and ( vpersoncode like 'uu' and vstop in ['2', '9'] )) and dstop < '2020-07-16'
     * @return 过滤条件
     */
    public static List<TableWhereInfo> parser(String whereStr) {
        List<TableWhereInfo> filterList = new ArrayList<>();

        if (!StringUtils.isEmpty(whereStr)) {
            // 初始化条件数组
            List<String> conditionList = new ArrayList<>();
            conditionList.add(whereStr);

            // 解析 and or ( )
            conditionList = parserAnd(conditionList);
            conditionList = parserOr(conditionList);
            conditionList = parserKuohao(conditionList);

            // 转换为数组
            for (String condition : conditionList) {
                filterList.add(new TableWhereInfo(condition));
            }
        }

        return filterList;
    }

    private static List<String> parserAnd(List<String> whereList) {
        List<String> tmpOutAnd = new ArrayList<>();
        for (String item : whereList) {
            String[] tmpList = item.split(J_AND);
            for (int i = 0; i < tmpList.length; i++) {
                if (i > 0) {
                    tmpOutAnd.add(J_AND);
                }
                tmpOutAnd.add(tmpList[i]);
            }
        }
        return tmpOutAnd;
    }

    private static List<String> parserOr(List<String> whereList) {
        List<String> tmpOutOr = new ArrayList<>();
        for (String item : whereList) {
            String[] tmpList = item.split(J_OR);
            for (int i = 0; i < tmpList.length; i++) {
                if (i > 0) {
                    tmpOutOr.add(J_OR);
                }
                tmpOutOr.add(tmpList[i]);
            }
        }
        return tmpOutOr;
    }

    private static List<String> parserKuohao(List<String> whereList) {
        List<String> tmpOutB = new ArrayList<>();
        for (String item : whereList) {
            if (item.trim().startsWith("(")) {
                tmpOutB.add(KUOHAO_L);
                String tmpStr = item.substring(item.indexOf(KUOHAO_L) + 1);
                if (tmpStr.trim().startsWith("(")) {
                    List<String> tmpList = new ArrayList<>();
                    tmpList.add(tmpStr);
                    // 递归处理多个括号
                    tmpOutB.addAll(parserKuohao(tmpList));
                } else {
                    tmpOutB.add(tmpStr);
                }
            } else if (item.trim().endsWith(")")) {
                String tmpStr = item.substring(0, item.lastIndexOf(KUOHAO_R));
                if (tmpStr.trim().endsWith(")")) {
                    List<String> tmpList = new ArrayList<>();
                    tmpList.add(tmpStr);
                    // 递归处理多个括号
                    tmpOutB.addAll(parserKuohao(tmpList));
                } else {
                    tmpOutB.add(tmpStr);
                }
                tmpOutB.add(KUOHAO_R);
            } else {
                tmpOutB.add(item);
            }
        }
        return tmpOutB;
    }
}

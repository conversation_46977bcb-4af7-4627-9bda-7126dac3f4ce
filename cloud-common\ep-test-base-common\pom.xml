<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.qm.tds</groupId>
        <artifactId>tds-base-service-parent</artifactId>
        <version>7.1.0-rebate-SNAPSHOT</version>
    </parent>

    <groupId>com.qm.tds</groupId>
    <artifactId>ep-test-base-common</artifactId>
    <version>7.1.0-rebate-SNAPSHOT</version>
    <name>ep-test-base-common</name>
    <description>TDS产品JUnitTestCase基础类库</description>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-common</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>nl.jqno.equalsverifier</groupId>
            <artifactId>equalsverifier</artifactId>
            <version>3.7.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <target>17</target>
                    <source>17</source>
                    <encoding>UTF-8</encoding>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 上传jar包至私有maven仓库-->
    <distributionManagement>
        <repository>
            <id>dayu-maven-releases</id>
            <url>https://devops-nexus.faw.cn/repository/bp4655969726-releases/</url>
        </repository>
        <snapshotRepository>
            <id>dayu-maven-snapshots</id>
            <url>https://devops-nexus.faw.cn/repository/bp4655969726-snapshots/</url>
            <uniqueVersion>true</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

</project>

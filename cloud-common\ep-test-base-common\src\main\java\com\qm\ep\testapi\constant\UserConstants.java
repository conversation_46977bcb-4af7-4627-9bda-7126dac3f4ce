package com.qm.ep.testapi.constant;

import com.qm.ep.testapi.domain.bean.UserInfoDO;

import java.util.HashMap;
import java.util.Map;

public class UserConstants {

    public static final Map<String, UserInfoDO> USER_MAP = new HashMap<>();
    /**
     * 超级管理员
     */
    public static final String USER_CODE_ADMIN = "wjq1";
    /**
     * 公司普通用户
     */
    public static final String USER_CODE_COMPANY = "wjq";
    /**
     * 销售经销商
     */
    public static final String USER_CODE_DEALER_SAL = "crm03";

    static {
        USER_MAP.put(USER_CODE_ADMIN, UserInfoDO.createEpPcUser("639583f187dc4ec46f290ad8be979f95", USER_CODE_ADMIN, "Admin(JUnit Test Case User)"));
        USER_MAP.put(USER_CODE_COMPANY, UserInfoDO.createEpPcUser("639583f187dc4ec46f290ad8be979f95", USER_CODE_COMPANY, "Admin(JUnit Test Case User)"));
        USER_MAP.put(USER_CODE_DEALER_SAL, UserInfoDO.createEpPcUser("639583f187dc4ec46f290ad8be979f95", USER_CODE_DEALER_SAL, "Sal Dealer(JUnit Test Case User)"));
    }
}

package com.qm.ep.testapi.controller;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.qm.ep.testapi.constant.UserConstants;
import com.qm.ep.testapi.domain.bean.UserInfoDO;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public abstract class BaseTestController<T extends BaseController> {

    @Autowired
    protected T testController;

    /**
     * 模拟Request请求上下文
     */
    @Autowired
    private MockHttpServletRequest requstMock;
    /**
     * 模拟Session上下文
     */
    @Autowired
    private MockHttpSession sessionMock;
    /**
     * 模拟Response应答上下文
     */
    @Autowired
    private MockHttpServletResponse responseMock;

    /**
     * 设置用户信息
     *
     * @param userCode 用户代码
     */
    protected void initUser(String userCode) {
        log.debug("初始化账号：" + userCode);
        UserInfoDO user = UserConstants.USER_MAP.getOrDefault(userCode, null);
        Assert.assertNotNull("初始化账号！", user);
        for (Map.Entry<String, String> item : user.toMap().entrySet()) {
            requstMock.addHeader(item.getKey(), item.getValue());
        }
    }

    /**
     * 按属性名获取Setter函数名
     *
     * @param fieldName 属性名
     * @return Setter函数名
     */
    private String getSetter(String fieldName) {
        return "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
    }

    /**
     * 按属性名获取Getter函数名
     *
     * @param fieldName 属性名
     * @return Getter函数名
     */
    private String getGetter(String fieldName) {
        return "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
    }

    /**
     * 按EP项目规则生成属性的默认值。
     *
     * @param field 字段属性
     * @return 默认值
     */
    private Object getDefaultValue(Field field) {
        Object ret;
        try {
            if (field.getType().isAssignableFrom(java.util.Date.class)) {
                ret = new Timestamp(System.currentTimeMillis());
            } else if (field.getType().isAssignableFrom(java.sql.Timestamp.class)) {
                ret = new Timestamp(System.currentTimeMillis());
            } else if (field.getType().isAssignableFrom(String.class)) {
                String vFieldName = field.getName();
                if (vFieldName.equalsIgnoreCase("id")) {
                    ret = RandomUtil.randomInt();
                } else if (vFieldName.equalsIgnoreCase("vstop") || vFieldName.toLowerCase().endsWith("flag")) {
                    // 标识类的默认为1
                    ret = "1";
                } else if (vFieldName.toLowerCase().endsWith("code")) {
                    // 代码类的默认为JUint开头后面追加8个大写字母
                    ret = "JUint" + RandomUtil.randomString(8);
                } else if (vFieldName.equalsIgnoreCase("name") || vFieldName.equalsIgnoreCase("text")) {
                    // 名称类的默认10个随机中文字
                    ret = RandomUtil.randomChinese();
                } else {
                    ret = RandomUtil.randomString(10);
                }
            } else if (field.getType().isAssignableFrom(Long.class)) {
                ret = (long) RandomUtil.randomInt(999999);
            } else if (field.getType().isAssignableFrom(Boolean.class)) {
                ret = Boolean.TRUE;
            } else if (field.getType().isAssignableFrom(long.class)) {
                ret = (long) RandomUtil.randomInt(999999);
            } else if (field.getType().isAssignableFrom(double.class)) {
                ret = (long) RandomUtil.randomInt(999999);
            } else if (field.getType().isAssignableFrom(int.class)) {
                ret = (long) RandomUtil.randomInt(999999);
            } else if (field.getType().isAssignableFrom(boolean.class)) {
                ret = true;
            } else {
                ret = null;
            }
        } catch (Exception e) {
            ret = null;
            log.info("---error--"+field.getName() + "生成默认值异常", e);
        }
        return ret;
    }

    /**
     * 获取一个所有属性都没有值的Module类。
     * 里面包含了对基类的处理。
     *
     * @param clazz module类
     * @param <M>
     * @return Module实例化对象
     */
    protected <M> M moduleEmpty(Class<M> clazz) {
        M obj;

        // 实例化对象
        try {
            obj = clazz.newInstance();
        } catch (Exception e) {
            obj = null;
            Assert.fail("moduleFill异常！" + e.getMessage());
        }

        // 设置标准Dto参数
        if (obj instanceof JsonParamDto) {
            JsonParamDto paraDto = (JsonParamDto) obj;
            paraDto.setCurrentPage(1);
            paraDto.setPageSize(20);
        }
        return obj;
    }

    /**
     * 获取一个所有属性都有值的Module类
     *
     * @param clazz module类
     * @param <M>
     * @return Module实例化对象
     */
    protected <M> M moduleFill(Class<M> clazz) {
        M obj;

        // 设置当前类的所有属性值
        try {
            obj = clazz.newInstance();
            Field[] fields = clazz.getDeclaredFields();
            Method[] methods = clazz.getMethods();
            for (Field field : fields) {
                String vMethodName = this.getSetter(field.getName());
                for (Method method : methods) {
                    if (method.getName().equals(vMethodName)) {
                        try {
                            method.invoke(obj, getDefaultValue(field));
                        } catch (Exception e) {
                            Assert.fail(method.getName() + " invoke 异常！" + e.getMessage());
                        }
                        break;
                    }
                }
            }
        } catch (Exception e) {
            obj = null;
            Assert.fail("moduleFill异常！" + e.getMessage());
        }

        // 设置标准Dto父类参数
        if (obj instanceof JsonParamDto) {
            JsonParamDto paraDto = (JsonParamDto) obj;
            paraDto.setCurrentPage(1);
            paraDto.setPageSize(20);
        }
        return obj;
    }

    /**
     * 覆盖module测试指标
     *
     * @param clazz module类
     * @param <M>
     * @deprecated 使用nl.jqno.equalsverifier.EqualsVerifier替换此函数
     */
    @Deprecated
    protected <M> void moduleTest(Class<M> clazz) {
        try {
            // 覆盖Module基础功能
            M beanEmpty = clazz.newInstance();
            M beanOne = this.moduleFill(clazz);
            M beanTwo = this.moduleFill(clazz);
            Object beanThree = new Object();
            Assert.assertNotNull(beanOne.toString());
            Assert.assertTrue(beanOne.hashCode() != 0);
            Assert.assertTrue(beanEmpty.hashCode() != 0);
            beanOne.equals(beanEmpty);
            beanOne.equals(beanOne);
            beanOne.equals(beanTwo);
            beanOne.equals(beanThree);
            beanOne.equals(null);
            beanThree.equals(beanOne);

            // 覆盖Module的equals函数
            M bean = clazz.newInstance();
            bean.equals(beanOne);
            bean.equals(beanEmpty);
            beanOne.equals(bean);
            beanEmpty.equals(bean);
            Field[] fields = clazz.getFields();
            for (Field field : fields) {
                Method methodSet = clazz.getMethod(this.getSetter(field.getName()), field.getType().getClass());
                Method methodGet = clazz.getMethod(this.getGetter(field.getName()), field.getType().getClass());
                if (methodSet != null && methodGet != null) {
                    methodSet.invoke(bean, methodGet.invoke(beanOne));
                    bean.equals(beanOne);
                    beanOne.equals(bean);
                    bean.equals(beanEmpty);
                    beanEmpty.equals(bean);
                }
            }
        } catch (Exception e) {
            Assert.fail(clazz.getName() + " moduleTest异常！" + e.getMessage());
        }
    }

    /**
     * JsonResultVo正确的返回值
     *
     * @return 正确的返回值
     */
    protected int getCodeOk() {
        return JsonResultVo.CODE_OK;
    }

    /**
     * 标准返回对象vo中是否为成功状态。
     *
     * @param vo 标准返回对象
     */
    protected void assertJsonResultVo(JsonResultVo<?> vo) {
        assertJsonResultVo(vo, 10000);
    }

    /**
     * 标准返回对象vo中是否为成功状态。
     * 如果返回记录数多余<code>limit</code>则是大数据量查询会拖累数据库性能，需要增加必要查询条件。
     *
     * @param vo    标准返回对象
     * @param limit 最大返回记录数限制
     */
    protected void assertJsonResultVo(JsonResultVo<?> vo, int limit) {
        Assert.assertTrue("返回结果是否为成功状态。", vo.isOk());
        try {
            if (vo.getData() instanceof QmPage) {
                QmPage<?> pageData = (QmPage<?>) vo.getData();
                if (pageData != null && pageData.getTotal() > limit) {
                    Assert.fail(String.format("QmPage总记录数[%d]大于阈值[%d]", limit, limit));
                }
            } else if (vo.getData() instanceof List) {
                List<?> dataList = (List<?>) vo.getData();
                if (CollectionUtils.isNotEmpty(dataList) && dataList.size() > limit) {
                    Assert.fail(String.format("Data返回结果[%d]大于阈值[%d]", limit, limit));
                }
            }
            if (CollectionUtils.isNotEmpty(vo.getDataList()) && vo.getDataList().size() > limit) {
                Assert.fail(String.format("DataList返回结果[%d]大于阈值[%d]", limit, limit));
            }
        } catch (Exception ex) {
            log.info("---error--"+"识别详细信息异常！" + ex.getMessage(), ex);
        }
    }

    /**
     * 标准返回对象vo中是否为成功状态
     *
     * @param vo 标准返回对象
     */
    protected void assertJsonResultVoFalse(JsonResultVo<?> vo) {
        Assert.assertFalse("返回结果是否为失败状态。", vo.isOk());
    }
}

package com.qm.ep.testapi.domain.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;

/**
 * 用户信息
 */
@NoArgsConstructor
@Data
public class UserInfoDO {

    private String userId;
    private String userCode;
    private String userName;
    private int companyId;
    private int custGroupId;
    private int tenantId;
    private String lang;
    private String appId;
    private String loginTimestamp;

    /**
     * 将用户信息转换为Map类型。
     *
     * @return Map类型数据
     */
    public HashMap<String, String> toMap() {
        HashMap<String, String> map = new HashMap<>();
        map.put("appId", this.getAppId());
        map.put("companyId", String.valueOf(this.getCompanyId()));
        map.put("custGroupId", String.valueOf(this.getCustGroupId()));
        map.put("lang", this.getLang());
        map.put("loginTimestamp", this.getLoginTimestamp());
        map.put("personCode", this.getUserCode());
        map.put("tenantId", String.valueOf(this.getTenantId()));
        map.put("userId", this.getUserId());
        map.put("userName", this.getUserName());
        return map;
    }

    /**
     * 创建一个EP pc端的用户信息
     *
     * @param userId   用户ID
     * @param userCode 用户代码
     * @param userName 用户名称
     * @return 用户信息
     */
    public static UserInfoDO createEpPcUser(String userId, String userCode, String userName) {
        return createEpPcUser(userId, userCode, userName, 6000);
    }

    /**
     * 创建一个EP pc端的用户信息
     *
     * @param userId    用户ID
     * @param userCode  用户代码
     * @param userName  用户名称
     * @param companyId 用户公司ID
     * @return 用户信息
     */
    public static UserInfoDO createEpPcUser(String userId, String userCode, String userName, int companyId) {
        UserInfoDO user = new UserInfoDO();
        user.userId = userId;
        user.userCode = userCode;
        user.userName = userName;
        user.companyId = companyId;
        user.custGroupId = 15;
        user.tenantId = 15;
        user.lang = "zh";
        user.appId = "JDMC";
        user.loginTimestamp = "-1";
        return user;
    }

}

#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 2049360 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=39628, tid=25688
#
# JRE version: OpenJDK Runtime Environment Zulu17.50+19-CA (17.0.11+9) (build 17.0.11+9-LTS)
# Java VM: OpenJDK 64-Bit Server VM Zulu17.50+19-CA (17.0.11+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Dclassworlds.conf=D:\Programming_Tools\apache-maven-3.8.4\bin\m2.conf -Dmaven.home=D:\Programming_Tools\apache-maven-3.8.4 -Dlibrary.jansi.path=D:\Programming_Tools\apache-maven-3.8.4\lib\jansi-native -Dmaven.multiModuleProjectDirectory=D:\Git\temp\sa-0201_base_tds_cloud\cloud-common org.codehaus.plexus.classworlds.launcher.Launcher clean deploy -Dmaven.test.skip=true -s D:\Programming_Tools\apache-maven-3.8.4\conf\gts-aliyun-global-settings-TDS.xml

Host: 12th Gen Intel(R) Core(TM) i7-1255U, 12 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5198)
Time: Fri Feb 14 09:31:16 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5198) elapsed time: 78.891450 seconds (0d 0h 1m 18s)

---------------  T H R E A D  ---------------

Current thread (0x000001f77fa68ff0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=25688, stack(0x0000001808400000,0x0000001808500000)]


Current CompileTask:
C2:  78891 14424       4       java.lang.invoke.MethodType::makeImpl (109 bytes)

Stack: [0x0000001808400000,0x0000001808500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x681759]
V  [jvm.dll+0x8391ea]
V  [jvm.dll+0x83acae]
V  [jvm.dll+0x83b313]
V  [jvm.dll+0x24b8bf]
V  [jvm.dll+0xac534]
V  [jvm.dll+0xacb7c]
V  [jvm.dll+0x36a8e7]
V  [jvm.dll+0x33462a]
V  [jvm.dll+0x333aca]
V  [jvm.dll+0x21a6b1]
V  [jvm.dll+0x219af1]
V  [jvm.dll+0x1a551d]
V  [jvm.dll+0x22981e]
V  [jvm.dll+0x2279dc]
V  [jvm.dll+0x7ee377]
V  [jvm.dll+0x7e875a]
V  [jvm.dll+0x680645]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001f75b737370, length=14, elements={
0x000001f76cbcf960, 0x000001f77fa44c80, 0x000001f77fa45a20, 0x000001f77fa61350,
0x000001f77fa62c40, 0x000001f77fa66a40, 0x000001f77fa68330, 0x000001f77fa68ff0,
0x000001f77fa69fd0, 0x000001f77fa70e20, 0x000001f7115ce070, 0x000001f7115d1640,
0x000001f761a864b0, 0x000001f761a844d0
}

Java Threads: ( => current thread )
  0x000001f76cbcf960 JavaThread "main" [_thread_in_native, id=24248, stack(0x0000001807300000,0x0000001807400000)]
  0x000001f77fa44c80 JavaThread "Reference Handler" daemon [_thread_blocked, id=18108, stack(0x0000001807e00000,0x0000001807f00000)]
  0x000001f77fa45a20 JavaThread "Finalizer" daemon [_thread_blocked, id=29892, stack(0x0000001807f00000,0x0000001808000000)]
  0x000001f77fa61350 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=22636, stack(0x0000001808000000,0x0000001808100000)]
  0x000001f77fa62c40 JavaThread "Attach Listener" daemon [_thread_blocked, id=25248, stack(0x0000001808100000,0x0000001808200000)]
  0x000001f77fa66a40 JavaThread "Service Thread" daemon [_thread_blocked, id=29444, stack(0x0000001808200000,0x0000001808300000)]
  0x000001f77fa68330 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=37584, stack(0x0000001808300000,0x0000001808400000)]
=>0x000001f77fa68ff0 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=25688, stack(0x0000001808400000,0x0000001808500000)]
  0x000001f77fa69fd0 JavaThread "C1 CompilerThread0" daemon [_thread_in_native, id=41456, stack(0x0000001808500000,0x0000001808600000)]
  0x000001f77fa70e20 JavaThread "Sweeper thread" daemon [_thread_blocked, id=21116, stack(0x0000001808600000,0x0000001808700000)]
  0x000001f7115ce070 JavaThread "Notification Thread" daemon [_thread_blocked, id=28572, stack(0x0000001808700000,0x0000001808800000)]
  0x000001f7115d1640 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=20416, stack(0x0000001808900000,0x0000001808a00000)]
  0x000001f761a864b0 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=26316, stack(0x0000001807600000,0x0000001807700000)]
  0x000001f761a844d0 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=41724, stack(0x0000001809500000,0x0000001809600000)]

Other Threads:
  0x000001f77fa41110 VMThread "VM Thread" [stack: 0x0000001807d00000,0x0000001807e00000] [id=40160]
  0x000001f7115d0570 WatcherThread [stack: 0x0000001808800000,0x0000001808900000] [id=35640]
  0x000001f76cc3a310 GCTaskThread "GC Thread#0" [stack: 0x0000001807800000,0x0000001807900000] [id=27768]
  0x000001f757b027a0 GCTaskThread "GC Thread#1" [stack: 0x0000001808c00000,0x0000001808d00000] [id=38876]
  0x000001f757b01c60 GCTaskThread "GC Thread#2" [stack: 0x0000001808d00000,0x0000001808e00000] [id=27340]
  0x000001f757b01f30 GCTaskThread "GC Thread#3" [stack: 0x0000001808e00000,0x0000001808f00000] [id=21736]
  0x000001f757b00e50 GCTaskThread "GC Thread#4" [stack: 0x0000001808f00000,0x0000001809000000] [id=28496]
  0x000001f757b024d0 GCTaskThread "GC Thread#5" [stack: 0x0000001809000000,0x0000001809100000] [id=29984]
  0x000001f757b01120 GCTaskThread "GC Thread#6" [stack: 0x0000001809100000,0x0000001809200000] [id=41832]
  0x000001f757b013f0 GCTaskThread "GC Thread#7" [stack: 0x0000001809200000,0x0000001809300000] [id=2020]
  0x000001f757b02200 GCTaskThread "GC Thread#8" [stack: 0x0000001809300000,0x0000001809400000] [id=7148]
  0x000001f757b00b80 GCTaskThread "GC Thread#9" [stack: 0x0000001809400000,0x0000001809500000] [id=23472]
  0x000001f76cc4ac00 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000001807900000,0x0000001807a00000] [id=11104]
  0x000001f76cc4b530 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000001807a00000,0x0000001807b00000] [id=10396]
  0x000001f7587ce890 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000001809c00000,0x0000001809d00000] [id=15748]
  0x000001f7587ce2f0 ConcurrentGCThread "G1 Conc#2" [stack: 0x0000001809d00000,0x0000001809e00000] [id=27656]
  0x000001f77f97cab0 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000001807b00000,0x0000001807c00000] [id=26480]
  0x000001f75a32dcb0 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000001808a00000,0x0000001808b00000] [id=324]
  0x000001f759d35a00 ConcurrentGCThread "G1 Refine#2" [stack: 0x0000001808b00000,0x0000001808c00000] [id=33320]
  0x000001f7585c8610 ConcurrentGCThread "G1 Refine#3" [stack: 0x0000001809600000,0x0000001809700000] [id=38688]
  0x000001f75a490d80 ConcurrentGCThread "G1 Refine#4" [stack: 0x0000001809700000,0x0000001809800000] [id=11888]
  0x000001f75a491080 ConcurrentGCThread "G1 Refine#5" [stack: 0x0000001809800000,0x0000001809900000] [id=37784]
  0x000001f75a491380 ConcurrentGCThread "G1 Refine#6" [stack: 0x0000001809900000,0x0000001809a00000] [id=41344]
  0x000001f758047820 ConcurrentGCThread "G1 Refine#7" [stack: 0x0000001809a00000,0x0000001809b00000] [id=23260]
  0x000001f77f97d400 ConcurrentGCThread "G1 Service" [stack: 0x0000001807c00000,0x0000001807d00000] [id=7164]

Threads with active compile tasks:
C2 CompilerThread0    79332 14424       4       java.lang.invoke.MethodType::makeImpl (109 bytes)
C1 CompilerThread0    79333 14522       3       lombok.javac.JavacNode::getStartPos (11 bytes)
C2 CompilerThread1    79333 14491   !   4       lombok.javac.JavacAST::drill (146 bytes)
C2 CompilerThread2    79333 14493       4       lombok.javac.JavacAST::buildStatementOrExpression (120 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001f76cbce3f0] Compile_lock - owner thread: 0x0000000000000000

Heap address: 0x0000000705800000, size: 4008 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001f712000000-0x000001f712bd0000-0x000001f712bd0000), size 12386304, SharedBaseAddress: 0x000001f712000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001f713000000-0x000001f753000000, reserved size: 1073741824
Narrow klass base: 0x000001f712000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 16024M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 252M
 Heap Max Capacity: 4008M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 593920K, used 361133K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 106 young (217088K), 12 survivors (24576K)
 Metaspace       used 48476K, committed 49024K, reserved 1114112K
  class space    used 5222K, committed 5440K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| O|  |TAMS 0x0000000705a00000, 0x0000000705800000| Untracked 
|   1|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| O|  |TAMS 0x0000000705c00000, 0x0000000705a00000| Untracked 
|   2|0x0000000705c00000, 0x0000000705e00000, 0x0000000705e00000|100%| O|  |TAMS 0x0000000705e00000, 0x0000000705c00000| Untracked 
|   3|0x0000000705e00000, 0x0000000706000000, 0x0000000706000000|100%| O|  |TAMS 0x0000000706000000, 0x0000000705e00000| Untracked 
|   4|0x0000000706000000, 0x0000000706200000, 0x0000000706200000|100%| O|  |TAMS 0x0000000706200000, 0x0000000706000000| Untracked 
|   5|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%| O|  |TAMS 0x0000000706400000, 0x0000000706200000| Untracked 
|   6|0x0000000706400000, 0x0000000706600000, 0x0000000706600000|100%| O|  |TAMS 0x0000000706600000, 0x0000000706400000| Untracked 
|   7|0x0000000706600000, 0x0000000706800000, 0x0000000706800000|100%| O|  |TAMS 0x0000000706800000, 0x0000000706600000| Untracked 
|   8|0x0000000706800000, 0x0000000706a00000, 0x0000000706a00000|100%| O|  |TAMS 0x0000000706a00000, 0x0000000706800000| Untracked 
|   9|0x0000000706a00000, 0x0000000706c00000, 0x0000000706c00000|100%| O|  |TAMS 0x0000000706c00000, 0x0000000706a00000| Untracked 
|  10|0x0000000706c00000, 0x0000000706e00000, 0x0000000706e00000|100%| O|  |TAMS 0x0000000706e00000, 0x0000000706c00000| Untracked 
|  11|0x0000000706e00000, 0x0000000707000000, 0x0000000707000000|100%| O|  |TAMS 0x0000000707000000, 0x0000000706e00000| Untracked 
|  12|0x0000000707000000, 0x0000000707200000, 0x0000000707200000|100%| O|  |TAMS 0x0000000707200000, 0x0000000707000000| Untracked 
|  13|0x0000000707200000, 0x0000000707400000, 0x0000000707400000|100%| O|  |TAMS 0x0000000707400000, 0x0000000707200000| Untracked 
|  14|0x0000000707400000, 0x0000000707600000, 0x0000000707600000|100%| O|  |TAMS 0x0000000707600000, 0x0000000707400000| Untracked 
|  15|0x0000000707600000, 0x0000000707800000, 0x0000000707800000|100%| O|  |TAMS 0x0000000707800000, 0x0000000707600000| Untracked 
|  16|0x0000000707800000, 0x0000000707a00000, 0x0000000707a00000|100%| O|  |TAMS 0x0000000707800000, 0x0000000707800000| Untracked 
|  17|0x0000000707a00000, 0x0000000707c00000, 0x0000000707c00000|100%| O|  |TAMS 0x0000000707c00000, 0x0000000707a00000| Untracked 
|  18|0x0000000707c00000, 0x0000000707e00000, 0x0000000707e00000|100%| O|  |TAMS 0x0000000707e00000, 0x0000000707c00000| Untracked 
|  19|0x0000000707e00000, 0x0000000708000000, 0x0000000708000000|100%| O|  |TAMS 0x0000000707e00000, 0x0000000707e00000| Untracked 
|  20|0x0000000708000000, 0x0000000708200000, 0x0000000708200000|100%| O|  |TAMS 0x0000000708200000, 0x0000000708000000| Untracked 
|  21|0x0000000708200000, 0x0000000708400000, 0x0000000708400000|100%| O|  |TAMS 0x0000000708200000, 0x0000000708200000| Untracked 
|  22|0x0000000708400000, 0x0000000708600000, 0x0000000708600000|100%| O|  |TAMS 0x0000000708400000, 0x0000000708400000| Untracked 
|  23|0x0000000708600000, 0x0000000708800000, 0x0000000708800000|100%| O|  |TAMS 0x0000000708800000, 0x0000000708600000| Untracked 
|  24|0x0000000708800000, 0x0000000708a00000, 0x0000000708a00000|100%| O|  |TAMS 0x0000000708a00000, 0x0000000708800000| Untracked 
|  25|0x0000000708a00000, 0x0000000708c00000, 0x0000000708c00000|100%| O|  |TAMS 0x0000000708c00000, 0x0000000708a00000| Untracked 
|  26|0x0000000708c00000, 0x0000000708e00000, 0x0000000708e00000|100%| O|  |TAMS 0x0000000708c00000, 0x0000000708c00000| Untracked 
|  27|0x0000000708e00000, 0x0000000709000000, 0x0000000709000000|100%| O|  |TAMS 0x0000000709000000, 0x0000000708e00000| Untracked 
|  28|0x0000000709000000, 0x0000000709200000, 0x0000000709200000|100%| O|  |TAMS 0x0000000709200000, 0x0000000709000000| Untracked 
|  29|0x0000000709200000, 0x0000000709400000, 0x0000000709400000|100%| O|  |TAMS 0x0000000709400000, 0x0000000709200000| Untracked 
|  30|0x0000000709400000, 0x0000000709600000, 0x0000000709600000|100%| O|  |TAMS 0x0000000709600000, 0x0000000709400000| Untracked 
|  31|0x0000000709600000, 0x0000000709800000, 0x0000000709800000|100%| O|  |TAMS 0x0000000709800000, 0x0000000709600000| Untracked 
|  32|0x0000000709800000, 0x0000000709a00000, 0x0000000709a00000|100%| O|  |TAMS 0x0000000709a00000, 0x0000000709800000| Untracked 
|  33|0x0000000709a00000, 0x0000000709c00000, 0x0000000709c00000|100%| O|  |TAMS 0x0000000709a00000, 0x0000000709a00000| Untracked 
|  34|0x0000000709c00000, 0x0000000709cff200, 0x0000000709e00000| 49%| O|  |TAMS 0x0000000709c00000, 0x0000000709c00000| Untracked 
|  35|0x0000000709e00000, 0x000000070a000000, 0x000000070a000000|100%|HS|  |TAMS 0x0000000709e00000, 0x0000000709e00000| Complete 
|  36|0x000000070a000000, 0x000000070a200000, 0x000000070a200000|100%| O|  |TAMS 0x000000070a200000, 0x000000070a000000| Untracked 
|  37|0x000000070a200000, 0x000000070a400000, 0x000000070a400000|100%|HS|  |TAMS 0x000000070a400000, 0x000000070a200000| Complete 
|  38|0x000000070a400000, 0x000000070a600000, 0x000000070a600000|100%|HC|  |TAMS 0x000000070a600000, 0x000000070a400000| Complete 
|  39|0x000000070a600000, 0x000000070a800000, 0x000000070a800000|100%| O|  |TAMS 0x000000070a600000, 0x000000070a600000| Untracked 
|  40|0x000000070a800000, 0x000000070aa00000, 0x000000070aa00000|100%| O|  |TAMS 0x000000070aa00000, 0x000000070a800000| Untracked 
|  41|0x000000070aa00000, 0x000000070ac00000, 0x000000070ac00000|100%| O|  |TAMS 0x000000070ac00000, 0x000000070aa00000| Untracked 
|  42|0x000000070ac00000, 0x000000070ae00000, 0x000000070ae00000|100%| O|  |TAMS 0x000000070ae00000, 0x000000070ac00000| Untracked 
|  43|0x000000070ae00000, 0x000000070b000000, 0x000000070b000000|100%| O|  |TAMS 0x000000070b000000, 0x000000070ae00000| Untracked 
|  44|0x000000070b000000, 0x000000070b200000, 0x000000070b200000|100%|HS|  |TAMS 0x000000070b200000, 0x000000070b000000| Complete 
|  45|0x000000070b200000, 0x000000070b400000, 0x000000070b400000|100%| O|  |TAMS 0x000000070b400000, 0x000000070b200000| Untracked 
|  46|0x000000070b400000, 0x000000070b600000, 0x000000070b600000|100%| O|  |TAMS 0x000000070b600000, 0x000000070b400000| Untracked 
|  47|0x000000070b600000, 0x000000070b800000, 0x000000070b800000|100%|HS|  |TAMS 0x000000070b600000, 0x000000070b600000| Complete 
|  48|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000, 0x000000070b800000| Untracked 
|  49|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000, 0x000000070ba00000| Untracked 
|  50|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000, 0x000000070bc00000| Untracked 
|  51|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000, 0x000000070be00000| Untracked 
|  52|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000, 0x000000070c000000| Untracked 
|  53|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000, 0x000000070c200000| Untracked 
|  54|0x000000070c400000, 0x000000070c600000, 0x000000070c600000|100%| O|  |TAMS 0x000000070c600000, 0x000000070c400000| Untracked 
|  55|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000, 0x000000070c600000| Untracked 
|  56|0x000000070c800000, 0x000000070ca00000, 0x000000070ca00000|100%| O|  |TAMS 0x000000070c800000, 0x000000070c800000| Untracked 
|  57|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000, 0x000000070ca00000| Untracked 
|  58|0x000000070cc00000, 0x000000070ce00000, 0x000000070ce00000|100%| O|  |TAMS 0x000000070cc00000, 0x000000070cc00000| Untracked 
|  59|0x000000070ce00000, 0x000000070d000000, 0x000000070d000000|100%| O|  |TAMS 0x000000070ce00000, 0x000000070ce00000| Untracked 
|  60|0x000000070d000000, 0x000000070d200000, 0x000000070d200000|100%| O|  |TAMS 0x000000070d000000, 0x000000070d000000| Untracked 
|  61|0x000000070d200000, 0x000000070d400000, 0x000000070d400000|100%| O|  |TAMS 0x000000070d200000, 0x000000070d200000| Untracked 
|  62|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000, 0x000000070d400000| Untracked 
|  63|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000, 0x000000070d600000| Untracked 
|  64|0x000000070d800000, 0x000000070da00000, 0x000000070da00000|100%| O|  |TAMS 0x000000070da00000, 0x000000070d800000| Untracked 
|  65|0x000000070da00000, 0x000000070dc00000, 0x000000070dc00000|100%| O|  |TAMS 0x000000070dc00000, 0x000000070da00000| Untracked 
|  66|0x000000070dc00000, 0x000000070de00000, 0x000000070de00000|100%| O|  |TAMS 0x000000070de00000, 0x000000070dc00000| Untracked 
|  67|0x000000070de00000, 0x000000070e000000, 0x000000070e000000|100%| O|  |TAMS 0x000000070de00000, 0x000000070de00000| Untracked 
|  68|0x000000070e000000, 0x000000070e200000, 0x000000070e200000|100%| O|  |TAMS 0x000000070e000000, 0x000000070e000000| Untracked 
|  69|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000, 0x000000070e200000| Untracked 
|  70|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000, 0x000000070e400000| Untracked 
|  71|0x000000070e600000, 0x000000070e800000, 0x000000070e800000|100%| O|  |TAMS 0x000000070e600000, 0x000000070e600000| Untracked 
|  72|0x000000070e800000, 0x000000070ea00000, 0x000000070ea00000|100%| O|  |TAMS 0x000000070ea00000, 0x000000070e800000| Untracked 
|  73|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000, 0x000000070ea00000| Untracked 
|  74|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000, 0x000000070ec00000| Untracked 
|  75|0x000000070ee00000, 0x000000070f000000, 0x000000070f000000|100%| O|  |TAMS 0x000000070f000000, 0x000000070ee00000| Untracked 
|  76|0x000000070f000000, 0x000000070f200000, 0x000000070f200000|100%| O|  |TAMS 0x000000070f200000, 0x000000070f000000| Untracked 
|  77|0x000000070f200000, 0x000000070f400000, 0x000000070f400000|100%| O|  |TAMS 0x000000070f2fb400, 0x000000070f200000| Untracked 
|  78|0x000000070f400000, 0x000000070f600000, 0x000000070f600000|100%| O|  |TAMS 0x000000070f400000, 0x000000070f400000| Untracked 
|  79|0x000000070f600000, 0x000000070f800000, 0x000000070f800000|100%|HS|  |TAMS 0x000000070f600000, 0x000000070f600000| Complete 
|  80|0x000000070f800000, 0x000000070fa00000, 0x000000070fa00000|100%| O|  |TAMS 0x000000070f800000, 0x000000070f800000| Untracked 
|  81|0x000000070fa00000, 0x000000070fa00000, 0x000000070fc00000|  0%| F|  |TAMS 0x000000070fa00000, 0x000000070fa00000| Untracked 
|  82|0x000000070fc00000, 0x000000070fc00000, 0x000000070fe00000|  0%| F|  |TAMS 0x000000070fc00000, 0x000000070fc00000| Untracked 
|  83|0x000000070fe00000, 0x0000000710000000, 0x0000000710000000|100%| O|  |TAMS 0x000000070fe00000, 0x000000070fe00000| Untracked 
|  84|0x0000000710000000, 0x0000000710200000, 0x0000000710200000|100%| O|  |TAMS 0x0000000710000000, 0x0000000710000000| Untracked 
|  85|0x0000000710200000, 0x0000000710400000, 0x0000000710400000|100%| O|  |TAMS 0x0000000710200000, 0x0000000710200000| Untracked 
|  86|0x0000000710400000, 0x0000000710600000, 0x0000000710600000|100%| O|  |TAMS 0x0000000710400000, 0x0000000710400000| Untracked 
|  87|0x0000000710600000, 0x0000000710800000, 0x0000000710800000|100%| O|  |TAMS 0x0000000710600000, 0x0000000710600000| Untracked 
|  88|0x0000000710800000, 0x0000000710800000, 0x0000000710a00000|  0%| F|  |TAMS 0x0000000710800000, 0x0000000710800000| Untracked 
|  89|0x0000000710a00000, 0x0000000710a00000, 0x0000000710c00000|  0%| F|  |TAMS 0x0000000710a00000, 0x0000000710a00000| Untracked 
|  90|0x0000000710c00000, 0x0000000710c00000, 0x0000000710e00000|  0%| F|  |TAMS 0x0000000710c00000, 0x0000000710c00000| Untracked 
|  91|0x0000000710e00000, 0x0000000710e00000, 0x0000000711000000|  0%| F|  |TAMS 0x0000000710e00000, 0x0000000710e00000| Untracked 
|  92|0x0000000711000000, 0x0000000711000000, 0x0000000711200000|  0%| F|  |TAMS 0x0000000711000000, 0x0000000711000000| Untracked 
|  93|0x0000000711200000, 0x0000000711200000, 0x0000000711400000|  0%| F|  |TAMS 0x0000000711200000, 0x0000000711200000| Untracked 
|  94|0x0000000711400000, 0x0000000711400000, 0x0000000711600000|  0%| F|  |TAMS 0x0000000711400000, 0x0000000711400000| Untracked 
|  95|0x0000000711600000, 0x0000000711600000, 0x0000000711800000|  0%| F|  |TAMS 0x0000000711600000, 0x0000000711600000| Untracked 
|  96|0x0000000711800000, 0x0000000711800000, 0x0000000711a00000|  0%| F|  |TAMS 0x0000000711800000, 0x0000000711800000| Untracked 
|  97|0x0000000711a00000, 0x0000000711a00000, 0x0000000711c00000|  0%| F|  |TAMS 0x0000000711a00000, 0x0000000711a00000| Untracked 
|  98|0x0000000711c00000, 0x0000000711c00000, 0x0000000711e00000|  0%| F|  |TAMS 0x0000000711c00000, 0x0000000711c00000| Untracked 
|  99|0x0000000711e00000, 0x0000000711e00000, 0x0000000712000000|  0%| F|  |TAMS 0x0000000711e00000, 0x0000000711e00000| Untracked 
| 100|0x0000000712000000, 0x0000000712000000, 0x0000000712200000|  0%| F|  |TAMS 0x0000000712000000, 0x0000000712000000| Untracked 
| 101|0x0000000712200000, 0x0000000712200000, 0x0000000712400000|  0%| F|  |TAMS 0x0000000712200000, 0x0000000712200000| Untracked 
| 102|0x0000000712400000, 0x0000000712400000, 0x0000000712600000|  0%| F|  |TAMS 0x0000000712400000, 0x0000000712400000| Untracked 
| 103|0x0000000712600000, 0x0000000712800000, 0x0000000712800000|100%| O|  |TAMS 0x0000000712800000, 0x0000000712600000| Untracked 
| 104|0x0000000712800000, 0x0000000712800000, 0x0000000712a00000|  0%| F|  |TAMS 0x0000000712800000, 0x0000000712800000| Untracked 
| 105|0x0000000712a00000, 0x0000000712a00000, 0x0000000712c00000|  0%| F|  |TAMS 0x0000000712a00000, 0x0000000712a00000| Untracked 
| 106|0x0000000712c00000, 0x0000000712c00000, 0x0000000712e00000|  0%| F|  |TAMS 0x0000000712c00000, 0x0000000712c00000| Untracked 
| 107|0x0000000712e00000, 0x0000000712e00000, 0x0000000713000000|  0%| F|  |TAMS 0x0000000712e00000, 0x0000000712e00000| Untracked 
| 108|0x0000000713000000, 0x0000000713000000, 0x0000000713200000|  0%| F|  |TAMS 0x0000000713000000, 0x0000000713000000| Untracked 
| 109|0x0000000713200000, 0x0000000713200000, 0x0000000713400000|  0%| F|  |TAMS 0x0000000713200000, 0x0000000713200000| Untracked 
| 110|0x0000000713400000, 0x0000000713400000, 0x0000000713600000|  0%| F|  |TAMS 0x0000000713400000, 0x0000000713400000| Untracked 
| 111|0x0000000713600000, 0x0000000713600000, 0x0000000713800000|  0%| F|  |TAMS 0x0000000713600000, 0x0000000713600000| Untracked 
| 112|0x0000000713800000, 0x0000000713800000, 0x0000000713a00000|  0%| F|  |TAMS 0x0000000713800000, 0x0000000713800000| Untracked 
| 113|0x0000000713a00000, 0x0000000713a00000, 0x0000000713c00000|  0%| F|  |TAMS 0x0000000713a00000, 0x0000000713a00000| Untracked 
| 114|0x0000000713c00000, 0x0000000713c00000, 0x0000000713e00000|  0%| F|  |TAMS 0x0000000713c00000, 0x0000000713c00000| Untracked 
| 115|0x0000000713e00000, 0x0000000713e00000, 0x0000000714000000|  0%| F|  |TAMS 0x0000000713e00000, 0x0000000713e00000| Untracked 
| 116|0x0000000714000000, 0x0000000714000000, 0x0000000714200000|  0%| F|  |TAMS 0x0000000714000000, 0x0000000714000000| Untracked 
| 117|0x0000000714200000, 0x0000000714200000, 0x0000000714400000|  0%| F|  |TAMS 0x0000000714200000, 0x0000000714200000| Untracked 
| 118|0x0000000714400000, 0x0000000714400000, 0x0000000714600000|  0%| F|  |TAMS 0x0000000714400000, 0x0000000714400000| Untracked 
| 119|0x0000000714600000, 0x0000000714600000, 0x0000000714800000|  0%| F|  |TAMS 0x0000000714600000, 0x0000000714600000| Untracked 
| 120|0x0000000714800000, 0x0000000714800000, 0x0000000714a00000|  0%| F|  |TAMS 0x0000000714800000, 0x0000000714800000| Untracked 
| 121|0x0000000714a00000, 0x0000000714a00000, 0x0000000714c00000|  0%| F|  |TAMS 0x0000000714a00000, 0x0000000714a00000| Untracked 
| 122|0x0000000714c00000, 0x0000000714c00000, 0x0000000714e00000|  0%| F|  |TAMS 0x0000000714c00000, 0x0000000714c00000| Untracked 
| 123|0x0000000714e00000, 0x0000000714e00000, 0x0000000715000000|  0%| F|  |TAMS 0x0000000714e00000, 0x0000000714e00000| Untracked 
| 124|0x0000000715000000, 0x0000000715000000, 0x0000000715200000|  0%| F|  |TAMS 0x0000000715000000, 0x0000000715000000| Untracked 
| 125|0x0000000715200000, 0x0000000715200000, 0x0000000715400000|  0%| F|  |TAMS 0x0000000715200000, 0x0000000715200000| Untracked 
| 126|0x0000000715400000, 0x0000000715400000, 0x0000000715600000|  0%| F|  |TAMS 0x0000000715400000, 0x0000000715400000| Untracked 
| 127|0x0000000715600000, 0x0000000715600000, 0x0000000715800000|  0%| F|  |TAMS 0x0000000715600000, 0x0000000715600000| Untracked 
| 128|0x0000000715800000, 0x0000000715800000, 0x0000000715a00000|  0%| F|  |TAMS 0x0000000715800000, 0x0000000715800000| Untracked 
| 129|0x0000000715a00000, 0x0000000715a00000, 0x0000000715c00000|  0%| F|  |TAMS 0x0000000715a00000, 0x0000000715a00000| Untracked 
| 130|0x0000000715c00000, 0x0000000715c00000, 0x0000000715e00000|  0%| F|  |TAMS 0x0000000715c00000, 0x0000000715c00000| Untracked 
| 131|0x0000000715e00000, 0x0000000715e00000, 0x0000000716000000|  0%| F|  |TAMS 0x0000000715e00000, 0x0000000715e00000| Untracked 
| 132|0x0000000716000000, 0x0000000716000000, 0x0000000716200000|  0%| F|  |TAMS 0x0000000716000000, 0x0000000716000000| Untracked 
| 133|0x0000000716200000, 0x0000000716200000, 0x0000000716400000|  0%| F|  |TAMS 0x0000000716200000, 0x0000000716200000| Untracked 
| 134|0x0000000716400000, 0x0000000716400000, 0x0000000716600000|  0%| F|  |TAMS 0x0000000716400000, 0x0000000716400000| Untracked 
| 135|0x0000000716600000, 0x0000000716600000, 0x0000000716800000|  0%| F|  |TAMS 0x0000000716600000, 0x0000000716600000| Untracked 
| 136|0x0000000716800000, 0x0000000716800000, 0x0000000716a00000|  0%| F|  |TAMS 0x0000000716800000, 0x0000000716800000| Untracked 
| 137|0x0000000716a00000, 0x0000000716a00000, 0x0000000716c00000|  0%| F|  |TAMS 0x0000000716a00000, 0x0000000716a00000| Untracked 
| 138|0x0000000716c00000, 0x0000000716c00000, 0x0000000716e00000|  0%| F|  |TAMS 0x0000000716c00000, 0x0000000716c00000| Untracked 
| 139|0x0000000716e00000, 0x0000000716e00000, 0x0000000717000000|  0%| F|  |TAMS 0x0000000716e00000, 0x0000000716e00000| Untracked 
| 140|0x0000000717000000, 0x0000000717000000, 0x0000000717200000|  0%| F|  |TAMS 0x0000000717000000, 0x0000000717000000| Untracked 
| 141|0x0000000717200000, 0x0000000717200000, 0x0000000717400000|  0%| F|  |TAMS 0x0000000717200000, 0x0000000717200000| Untracked 
| 142|0x0000000717400000, 0x0000000717400000, 0x0000000717600000|  0%| F|  |TAMS 0x0000000717400000, 0x0000000717400000| Untracked 
| 143|0x0000000717600000, 0x0000000717600000, 0x0000000717800000|  0%| F|  |TAMS 0x0000000717600000, 0x0000000717600000| Untracked 
| 144|0x0000000717800000, 0x0000000717800000, 0x0000000717a00000|  0%| F|  |TAMS 0x0000000717800000, 0x0000000717800000| Untracked 
| 145|0x0000000717a00000, 0x0000000717a00000, 0x0000000717c00000|  0%| F|  |TAMS 0x0000000717a00000, 0x0000000717a00000| Untracked 
| 146|0x0000000717c00000, 0x0000000717c00000, 0x0000000717e00000|  0%| F|  |TAMS 0x0000000717c00000, 0x0000000717c00000| Untracked 
| 147|0x0000000717e00000, 0x0000000717e00000, 0x0000000718000000|  0%| F|  |TAMS 0x0000000717e00000, 0x0000000717e00000| Untracked 
| 148|0x0000000718000000, 0x0000000718000000, 0x0000000718200000|  0%| F|  |TAMS 0x0000000718000000, 0x0000000718000000| Untracked 
| 149|0x0000000718200000, 0x0000000718200000, 0x0000000718400000|  0%| F|  |TAMS 0x0000000718200000, 0x0000000718200000| Untracked 
| 150|0x0000000718400000, 0x0000000718400000, 0x0000000718600000|  0%| F|  |TAMS 0x0000000718400000, 0x0000000718400000| Untracked 
| 151|0x0000000718600000, 0x00000007187ac240, 0x0000000718800000| 83%| S|CS|TAMS 0x0000000718600000, 0x0000000718600000| Complete 
| 152|0x0000000718800000, 0x0000000718a00000, 0x0000000718a00000|100%| S|CS|TAMS 0x0000000718800000, 0x0000000718800000| Complete 
| 153|0x0000000718a00000, 0x0000000718c00000, 0x0000000718c00000|100%| S|CS|TAMS 0x0000000718a00000, 0x0000000718a00000| Complete 
| 154|0x0000000718c00000, 0x0000000718e00000, 0x0000000718e00000|100%| S|CS|TAMS 0x0000000718c00000, 0x0000000718c00000| Complete 
| 155|0x0000000718e00000, 0x0000000719000000, 0x0000000719000000|100%| S|CS|TAMS 0x0000000718e00000, 0x0000000718e00000| Complete 
| 156|0x0000000719000000, 0x0000000719200000, 0x0000000719200000|100%| S|CS|TAMS 0x0000000719000000, 0x0000000719000000| Complete 
| 157|0x0000000719200000, 0x0000000719400000, 0x0000000719400000|100%| S|CS|TAMS 0x0000000719200000, 0x0000000719200000| Complete 
| 158|0x0000000719400000, 0x0000000719600000, 0x0000000719600000|100%| S|CS|TAMS 0x0000000719400000, 0x0000000719400000| Complete 
| 159|0x0000000719600000, 0x0000000719800000, 0x0000000719800000|100%| S|CS|TAMS 0x0000000719600000, 0x0000000719600000| Complete 
| 160|0x0000000719800000, 0x0000000719a00000, 0x0000000719a00000|100%| S|CS|TAMS 0x0000000719800000, 0x0000000719800000| Complete 
| 161|0x0000000719a00000, 0x0000000719c00000, 0x0000000719c00000|100%| S|CS|TAMS 0x0000000719a00000, 0x0000000719a00000| Complete 
| 162|0x0000000719c00000, 0x0000000719e00000, 0x0000000719e00000|100%| S|CS|TAMS 0x0000000719c00000, 0x0000000719c00000| Complete 
| 163|0x0000000719e00000, 0x0000000719e00000, 0x000000071a000000|  0%| F|  |TAMS 0x0000000719e00000, 0x0000000719e00000| Untracked 
| 164|0x000000071a000000, 0x000000071a000000, 0x000000071a200000|  0%| F|  |TAMS 0x000000071a000000, 0x000000071a000000| Untracked 
| 165|0x000000071a200000, 0x000000071a200000, 0x000000071a400000|  0%| F|  |TAMS 0x000000071a200000, 0x000000071a200000| Untracked 
| 166|0x000000071a400000, 0x000000071a400000, 0x000000071a600000|  0%| F|  |TAMS 0x000000071a400000, 0x000000071a400000| Untracked 
| 167|0x000000071a600000, 0x000000071a600000, 0x000000071a800000|  0%| F|  |TAMS 0x000000071a600000, 0x000000071a600000| Untracked 
| 168|0x000000071a800000, 0x000000071a800000, 0x000000071aa00000|  0%| F|  |TAMS 0x000000071a800000, 0x000000071a800000| Untracked 
| 169|0x000000071aa00000, 0x000000071aa00000, 0x000000071ac00000|  0%| F|  |TAMS 0x000000071aa00000, 0x000000071aa00000| Untracked 
| 170|0x000000071ac00000, 0x000000071ac00000, 0x000000071ae00000|  0%| F|  |TAMS 0x000000071ac00000, 0x000000071ac00000| Untracked 
| 171|0x000000071ae00000, 0x000000071ae00000, 0x000000071b000000|  0%| F|  |TAMS 0x000000071ae00000, 0x000000071ae00000| Untracked 
| 172|0x000000071b000000, 0x000000071b000000, 0x000000071b200000|  0%| F|  |TAMS 0x000000071b000000, 0x000000071b000000| Untracked 
| 173|0x000000071b200000, 0x000000071b200000, 0x000000071b400000|  0%| F|  |TAMS 0x000000071b200000, 0x000000071b200000| Untracked 
| 174|0x000000071b400000, 0x000000071b400000, 0x000000071b600000|  0%| F|  |TAMS 0x000000071b400000, 0x000000071b400000| Untracked 
| 175|0x000000071b600000, 0x000000071b600000, 0x000000071b800000|  0%| F|  |TAMS 0x000000071b600000, 0x000000071b600000| Untracked 
| 176|0x000000071b800000, 0x000000071b800000, 0x000000071ba00000|  0%| F|  |TAMS 0x000000071b800000, 0x000000071b800000| Untracked 
| 177|0x000000071ba00000, 0x000000071ba00000, 0x000000071bc00000|  0%| F|  |TAMS 0x000000071ba00000, 0x000000071ba00000| Untracked 
| 178|0x000000071bc00000, 0x000000071bc00000, 0x000000071be00000|  0%| F|  |TAMS 0x000000071bc00000, 0x000000071bc00000| Untracked 
| 179|0x000000071be00000, 0x000000071be00000, 0x000000071c000000|  0%| F|  |TAMS 0x000000071be00000, 0x000000071be00000| Untracked 
| 180|0x000000071c000000, 0x000000071c000000, 0x000000071c200000|  0%| F|  |TAMS 0x000000071c000000, 0x000000071c000000| Untracked 
| 181|0x000000071c200000, 0x000000071c200000, 0x000000071c400000|  0%| F|  |TAMS 0x000000071c200000, 0x000000071c200000| Untracked 
| 182|0x000000071c400000, 0x000000071c400000, 0x000000071c600000|  0%| F|  |TAMS 0x000000071c400000, 0x000000071c400000| Untracked 
| 183|0x000000071c600000, 0x000000071c600000, 0x000000071c800000|  0%| F|  |TAMS 0x000000071c600000, 0x000000071c600000| Untracked 
| 184|0x000000071c800000, 0x000000071c800000, 0x000000071ca00000|  0%| F|  |TAMS 0x000000071c800000, 0x000000071c800000| Untracked 
| 185|0x000000071ca00000, 0x000000071ca00000, 0x000000071cc00000|  0%| F|  |TAMS 0x000000071ca00000, 0x000000071ca00000| Untracked 
| 186|0x000000071cc00000, 0x000000071cc00000, 0x000000071ce00000|  0%| F|  |TAMS 0x000000071cc00000, 0x000000071cc00000| Untracked 
| 187|0x000000071ce00000, 0x000000071ce00000, 0x000000071d000000|  0%| F|  |TAMS 0x000000071ce00000, 0x000000071ce00000| Untracked 
| 188|0x000000071d000000, 0x000000071d000000, 0x000000071d200000|  0%| F|  |TAMS 0x000000071d000000, 0x000000071d000000| Untracked 
| 189|0x000000071d200000, 0x000000071d200000, 0x000000071d400000|  0%| F|  |TAMS 0x000000071d200000, 0x000000071d200000| Untracked 
| 190|0x000000071d400000, 0x000000071d400000, 0x000000071d600000|  0%| F|  |TAMS 0x000000071d400000, 0x000000071d400000| Untracked 
| 191|0x000000071d600000, 0x000000071d600000, 0x000000071d800000|  0%| F|  |TAMS 0x000000071d600000, 0x000000071d600000| Untracked 
| 192|0x000000071d800000, 0x000000071d800000, 0x000000071da00000|  0%| F|  |TAMS 0x000000071d800000, 0x000000071d800000| Untracked 
| 193|0x000000071da00000, 0x000000071da00000, 0x000000071dc00000|  0%| F|  |TAMS 0x000000071da00000, 0x000000071da00000| Untracked 
| 194|0x000000071dc00000, 0x000000071dc00000, 0x000000071de00000|  0%| F|  |TAMS 0x000000071dc00000, 0x000000071dc00000| Untracked 
| 195|0x000000071de00000, 0x000000071de00000, 0x000000071e000000|  0%| F|  |TAMS 0x000000071de00000, 0x000000071de00000| Untracked 
| 196|0x000000071e000000, 0x000000071e100800, 0x000000071e200000| 50%| E|  |TAMS 0x000000071e000000, 0x000000071e000000| Complete 
| 197|0x000000071e200000, 0x000000071e400000, 0x000000071e400000|100%| E|CS|TAMS 0x000000071e200000, 0x000000071e200000| Complete 
| 198|0x000000071e400000, 0x000000071e600000, 0x000000071e600000|100%| E|CS|TAMS 0x000000071e400000, 0x000000071e400000| Complete 
| 199|0x000000071e600000, 0x000000071e800000, 0x000000071e800000|100%| E|CS|TAMS 0x000000071e600000, 0x000000071e600000| Complete 
| 200|0x000000071e800000, 0x000000071ea00000, 0x000000071ea00000|100%| E|CS|TAMS 0x000000071e800000, 0x000000071e800000| Complete 
| 201|0x000000071ea00000, 0x000000071ec00000, 0x000000071ec00000|100%| E|CS|TAMS 0x000000071ea00000, 0x000000071ea00000| Complete 
| 202|0x000000071ec00000, 0x000000071ee00000, 0x000000071ee00000|100%| E|CS|TAMS 0x000000071ec00000, 0x000000071ec00000| Complete 
| 203|0x000000071ee00000, 0x000000071f000000, 0x000000071f000000|100%| E|CS|TAMS 0x000000071ee00000, 0x000000071ee00000| Complete 
| 204|0x000000071f000000, 0x000000071f200000, 0x000000071f200000|100%| E|CS|TAMS 0x000000071f000000, 0x000000071f000000| Complete 
| 205|0x000000071f200000, 0x000000071f400000, 0x000000071f400000|100%| E|CS|TAMS 0x000000071f200000, 0x000000071f200000| Complete 
| 206|0x000000071f400000, 0x000000071f600000, 0x000000071f600000|100%| E|CS|TAMS 0x000000071f400000, 0x000000071f400000| Complete 
| 207|0x000000071f600000, 0x000000071f800000, 0x000000071f800000|100%| E|CS|TAMS 0x000000071f600000, 0x000000071f600000| Complete 
| 208|0x000000071f800000, 0x000000071fa00000, 0x000000071fa00000|100%| E|CS|TAMS 0x000000071f800000, 0x000000071f800000| Complete 
| 209|0x000000071fa00000, 0x000000071fc00000, 0x000000071fc00000|100%| E|CS|TAMS 0x000000071fa00000, 0x000000071fa00000| Complete 
| 210|0x000000071fc00000, 0x000000071fe00000, 0x000000071fe00000|100%| E|CS|TAMS 0x000000071fc00000, 0x000000071fc00000| Complete 
| 211|0x000000071fe00000, 0x0000000720000000, 0x0000000720000000|100%| E|CS|TAMS 0x000000071fe00000, 0x000000071fe00000| Complete 
| 212|0x0000000720000000, 0x0000000720200000, 0x0000000720200000|100%| E|CS|TAMS 0x0000000720000000, 0x0000000720000000| Complete 
| 213|0x0000000720200000, 0x0000000720400000, 0x0000000720400000|100%| E|CS|TAMS 0x0000000720200000, 0x0000000720200000| Complete 
| 214|0x0000000720400000, 0x0000000720600000, 0x0000000720600000|100%| E|CS|TAMS 0x0000000720400000, 0x0000000720400000| Complete 
| 215|0x0000000720600000, 0x0000000720800000, 0x0000000720800000|100%| E|CS|TAMS 0x0000000720600000, 0x0000000720600000| Complete 
| 216|0x0000000720800000, 0x0000000720a00000, 0x0000000720a00000|100%| E|CS|TAMS 0x0000000720800000, 0x0000000720800000| Complete 
| 217|0x0000000720a00000, 0x0000000720c00000, 0x0000000720c00000|100%| E|CS|TAMS 0x0000000720a00000, 0x0000000720a00000| Complete 
| 218|0x0000000720c00000, 0x0000000720e00000, 0x0000000720e00000|100%| E|CS|TAMS 0x0000000720c00000, 0x0000000720c00000| Complete 
| 219|0x0000000720e00000, 0x0000000721000000, 0x0000000721000000|100%| E|CS|TAMS 0x0000000720e00000, 0x0000000720e00000| Complete 
| 220|0x0000000721000000, 0x0000000721200000, 0x0000000721200000|100%| E|CS|TAMS 0x0000000721000000, 0x0000000721000000| Complete 
| 221|0x0000000721200000, 0x0000000721400000, 0x0000000721400000|100%| E|CS|TAMS 0x0000000721200000, 0x0000000721200000| Complete 
| 222|0x0000000721400000, 0x0000000721600000, 0x0000000721600000|100%| E|CS|TAMS 0x0000000721400000, 0x0000000721400000| Complete 
| 223|0x0000000721600000, 0x0000000721800000, 0x0000000721800000|100%| E|CS|TAMS 0x0000000721600000, 0x0000000721600000| Complete 
| 224|0x0000000721800000, 0x0000000721a00000, 0x0000000721a00000|100%| E|CS|TAMS 0x0000000721800000, 0x0000000721800000| Complete 
| 225|0x0000000721a00000, 0x0000000721c00000, 0x0000000721c00000|100%| E|CS|TAMS 0x0000000721a00000, 0x0000000721a00000| Complete 
| 226|0x0000000721c00000, 0x0000000721e00000, 0x0000000721e00000|100%| E|CS|TAMS 0x0000000721c00000, 0x0000000721c00000| Complete 
| 227|0x0000000721e00000, 0x0000000722000000, 0x0000000722000000|100%| E|CS|TAMS 0x0000000721e00000, 0x0000000721e00000| Complete 
| 228|0x0000000722000000, 0x0000000722200000, 0x0000000722200000|100%| E|CS|TAMS 0x0000000722000000, 0x0000000722000000| Complete 
| 229|0x0000000722200000, 0x0000000722400000, 0x0000000722400000|100%| E|CS|TAMS 0x0000000722200000, 0x0000000722200000| Complete 
| 230|0x0000000722400000, 0x0000000722600000, 0x0000000722600000|100%| E|CS|TAMS 0x0000000722400000, 0x0000000722400000| Complete 
| 231|0x0000000722600000, 0x0000000722800000, 0x0000000722800000|100%| E|CS|TAMS 0x0000000722600000, 0x0000000722600000| Complete 
| 232|0x0000000722800000, 0x0000000722a00000, 0x0000000722a00000|100%| E|CS|TAMS 0x0000000722800000, 0x0000000722800000| Complete 
| 233|0x0000000722a00000, 0x0000000722c00000, 0x0000000722c00000|100%| E|CS|TAMS 0x0000000722a00000, 0x0000000722a00000| Complete 
| 234|0x0000000722c00000, 0x0000000722e00000, 0x0000000722e00000|100%| E|CS|TAMS 0x0000000722c00000, 0x0000000722c00000| Complete 
| 235|0x0000000722e00000, 0x0000000723000000, 0x0000000723000000|100%| E|CS|TAMS 0x0000000722e00000, 0x0000000722e00000| Complete 
| 236|0x0000000723000000, 0x0000000723200000, 0x0000000723200000|100%| E|CS|TAMS 0x0000000723000000, 0x0000000723000000| Complete 
| 237|0x0000000723200000, 0x0000000723400000, 0x0000000723400000|100%| E|CS|TAMS 0x0000000723200000, 0x0000000723200000| Complete 
| 238|0x0000000723400000, 0x0000000723600000, 0x0000000723600000|100%| E|CS|TAMS 0x0000000723400000, 0x0000000723400000| Complete 
| 239|0x0000000723600000, 0x0000000723800000, 0x0000000723800000|100%| E|CS|TAMS 0x0000000723600000, 0x0000000723600000| Complete 
| 240|0x0000000723800000, 0x0000000723a00000, 0x0000000723a00000|100%| E|CS|TAMS 0x0000000723800000, 0x0000000723800000| Complete 
| 241|0x0000000723a00000, 0x0000000723c00000, 0x0000000723c00000|100%| E|CS|TAMS 0x0000000723a00000, 0x0000000723a00000| Complete 
| 242|0x0000000723c00000, 0x0000000723e00000, 0x0000000723e00000|100%| E|CS|TAMS 0x0000000723c00000, 0x0000000723c00000| Complete 
| 243|0x0000000723e00000, 0x0000000724000000, 0x0000000724000000|100%| E|CS|TAMS 0x0000000723e00000, 0x0000000723e00000| Complete 
| 244|0x0000000724000000, 0x0000000724200000, 0x0000000724200000|100%| E|CS|TAMS 0x0000000724000000, 0x0000000724000000| Complete 
| 245|0x0000000724200000, 0x0000000724400000, 0x0000000724400000|100%| E|CS|TAMS 0x0000000724200000, 0x0000000724200000| Complete 
| 246|0x0000000724400000, 0x0000000724600000, 0x0000000724600000|100%| E|CS|TAMS 0x0000000724400000, 0x0000000724400000| Complete 
| 247|0x0000000724600000, 0x0000000724800000, 0x0000000724800000|100%| E|CS|TAMS 0x0000000724600000, 0x0000000724600000| Complete 
| 248|0x0000000724800000, 0x0000000724a00000, 0x0000000724a00000|100%| E|CS|TAMS 0x0000000724800000, 0x0000000724800000| Complete 
| 249|0x0000000724a00000, 0x0000000724c00000, 0x0000000724c00000|100%| E|CS|TAMS 0x0000000724a00000, 0x0000000724a00000| Complete 
| 250|0x0000000724c00000, 0x0000000724e00000, 0x0000000724e00000|100%| E|CS|TAMS 0x0000000724c00000, 0x0000000724c00000| Complete 
| 251|0x0000000724e00000, 0x0000000725000000, 0x0000000725000000|100%| E|CS|TAMS 0x0000000724e00000, 0x0000000724e00000| Complete 
| 252|0x0000000725000000, 0x0000000725200000, 0x0000000725200000|100%| E|CS|TAMS 0x0000000725000000, 0x0000000725000000| Complete 
| 253|0x0000000725200000, 0x0000000725400000, 0x0000000725400000|100%| E|CS|TAMS 0x0000000725200000, 0x0000000725200000| Complete 
| 254|0x0000000725400000, 0x0000000725600000, 0x0000000725600000|100%| E|CS|TAMS 0x0000000725400000, 0x0000000725400000| Complete 
| 255|0x0000000725600000, 0x0000000725800000, 0x0000000725800000|100%| E|CS|TAMS 0x0000000725600000, 0x0000000725600000| Complete 
| 256|0x0000000725800000, 0x0000000725a00000, 0x0000000725a00000|100%| E|CS|TAMS 0x0000000725800000, 0x0000000725800000| Complete 
| 257|0x0000000725a00000, 0x0000000725c00000, 0x0000000725c00000|100%| E|CS|TAMS 0x0000000725a00000, 0x0000000725a00000| Complete 
| 258|0x0000000725c00000, 0x0000000725e00000, 0x0000000725e00000|100%| E|CS|TAMS 0x0000000725c00000, 0x0000000725c00000| Complete 
| 259|0x0000000725e00000, 0x0000000726000000, 0x0000000726000000|100%| E|CS|TAMS 0x0000000725e00000, 0x0000000725e00000| Complete 
| 260|0x0000000726000000, 0x0000000726200000, 0x0000000726200000|100%| E|CS|TAMS 0x0000000726000000, 0x0000000726000000| Complete 
| 261|0x0000000726200000, 0x0000000726400000, 0x0000000726400000|100%| E|CS|TAMS 0x0000000726200000, 0x0000000726200000| Complete 
| 262|0x0000000726400000, 0x0000000726600000, 0x0000000726600000|100%| E|CS|TAMS 0x0000000726400000, 0x0000000726400000| Complete 
| 263|0x0000000726600000, 0x0000000726800000, 0x0000000726800000|100%| E|CS|TAMS 0x0000000726600000, 0x0000000726600000| Complete 
| 264|0x0000000726800000, 0x0000000726a00000, 0x0000000726a00000|100%| E|CS|TAMS 0x0000000726800000, 0x0000000726800000| Complete 
| 265|0x0000000726a00000, 0x0000000726c00000, 0x0000000726c00000|100%| E|CS|TAMS 0x0000000726a00000, 0x0000000726a00000| Complete 
| 266|0x0000000726c00000, 0x0000000726e00000, 0x0000000726e00000|100%| E|CS|TAMS 0x0000000726c00000, 0x0000000726c00000| Complete 
| 267|0x0000000726e00000, 0x0000000727000000, 0x0000000727000000|100%| E|CS|TAMS 0x0000000726e00000, 0x0000000726e00000| Complete 
| 268|0x0000000727000000, 0x0000000727200000, 0x0000000727200000|100%| E|CS|TAMS 0x0000000727000000, 0x0000000727000000| Complete 
| 269|0x0000000727200000, 0x0000000727400000, 0x0000000727400000|100%| E|CS|TAMS 0x0000000727200000, 0x0000000727200000| Complete 
| 270|0x0000000727400000, 0x0000000727600000, 0x0000000727600000|100%| E|CS|TAMS 0x0000000727400000, 0x0000000727400000| Complete 
| 271|0x0000000727600000, 0x0000000727800000, 0x0000000727800000|100%| E|CS|TAMS 0x0000000727600000, 0x0000000727600000| Complete 
| 272|0x0000000727800000, 0x0000000727a00000, 0x0000000727a00000|100%| E|CS|TAMS 0x0000000727800000, 0x0000000727800000| Complete 
| 273|0x0000000727a00000, 0x0000000727c00000, 0x0000000727c00000|100%| E|  |TAMS 0x0000000727a00000, 0x0000000727a00000| Complete 
| 274|0x0000000727c00000, 0x0000000727e00000, 0x0000000727e00000|100%| E|CS|TAMS 0x0000000727c00000, 0x0000000727c00000| Complete 
| 275|0x0000000727e00000, 0x0000000728000000, 0x0000000728000000|100%| E|CS|TAMS 0x0000000727e00000, 0x0000000727e00000| Complete 
| 276|0x0000000728000000, 0x0000000728200000, 0x0000000728200000|100%| E|CS|TAMS 0x0000000728000000, 0x0000000728000000| Complete 
| 277|0x0000000728200000, 0x0000000728400000, 0x0000000728400000|100%| E|CS|TAMS 0x0000000728200000, 0x0000000728200000| Complete 
| 278|0x0000000728400000, 0x0000000728600000, 0x0000000728600000|100%| E|CS|TAMS 0x0000000728400000, 0x0000000728400000| Complete 
| 279|0x0000000728600000, 0x0000000728800000, 0x0000000728800000|100%| E|CS|TAMS 0x0000000728600000, 0x0000000728600000| Complete 
| 280|0x0000000728800000, 0x0000000728a00000, 0x0000000728a00000|100%| E|CS|TAMS 0x0000000728800000, 0x0000000728800000| Complete 
| 281|0x0000000728a00000, 0x0000000728c00000, 0x0000000728c00000|100%| E|CS|TAMS 0x0000000728a00000, 0x0000000728a00000| Complete 
| 282|0x0000000728c00000, 0x0000000728e00000, 0x0000000728e00000|100%| E|CS|TAMS 0x0000000728c00000, 0x0000000728c00000| Complete 
| 283|0x0000000728e00000, 0x0000000729000000, 0x0000000729000000|100%| E|CS|TAMS 0x0000000728e00000, 0x0000000728e00000| Complete 
| 284|0x0000000729000000, 0x0000000729200000, 0x0000000729200000|100%| E|CS|TAMS 0x0000000729000000, 0x0000000729000000| Complete 
| 285|0x0000000729200000, 0x0000000729400000, 0x0000000729400000|100%| E|CS|TAMS 0x0000000729200000, 0x0000000729200000| Complete 
| 286|0x0000000729400000, 0x0000000729600000, 0x0000000729600000|100%| E|CS|TAMS 0x0000000729400000, 0x0000000729400000| Complete 
| 287|0x0000000729600000, 0x0000000729800000, 0x0000000729800000|100%| E|CS|TAMS 0x0000000729600000, 0x0000000729600000| Complete 
| 288|0x0000000729800000, 0x0000000729a00000, 0x0000000729a00000|100%| E|CS|TAMS 0x0000000729800000, 0x0000000729800000| Complete 
| 289|0x0000000729a00000, 0x0000000729c00000, 0x0000000729c00000|100%| E|CS|TAMS 0x0000000729a00000, 0x0000000729a00000| Complete 

Card table byte_map: [0x000001f776a90000,0x000001f777270000] _byte_map_base: 0x000001f773264000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001f76cc3a850, (CMBitMap*) 0x000001f76cc3a890
 Prev Bits: [0x000001f777a50000, 0x000001f77b8f0000)
 Next Bits: [0x000001f77b8f0000, 0x000001f77f790000)

Polling page: 0x000001f769b70000

Metaspace:

Usage:
  Non-class:     42.27 MB used.
      Class:      5.10 MB used.
       Both:     47.38 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      42.56 MB ( 67%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       5.38 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      47.94 MB (  4%) committed. 

Chunk freelists:
   Non-Class:  5.47 MB
       Class:  10.48 MB
        Both:  15.95 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 71.50 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 428.
num_arena_deaths: 24.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 827.
num_space_uncommitted: 60.
num_chunks_returned_to_freelist: 308.
num_chunks_taken_from_freelist: 2419.
num_chunk_merges: 82.
num_chunk_splits: 1595.
num_chunks_enlarged: 1189.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=11526Kb max_used=11603Kb free=108473Kb
 bounds [0x000001f707ad0000, 0x000001f708630000, 0x000001f70f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=25126Kb max_used=25260Kb free=94873Kb
 bounds [0x000001f700000000, 0x000001f7018c0000, 0x000001f707530000]
CodeHeap 'non-nmethods': size=5760Kb used=1294Kb max_used=1370Kb free=4465Kb
 bounds [0x000001f707530000, 0x000001f7077a0000, 0x000001f707ad0000]
 total_blobs=11613 nmethods=11003 adapters=521
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 78.582 Thread 0x000001f77fa69fd0 14415   !   3       lombok.launch.ShadowClassLoader::getResource_ (242 bytes)
Event: 78.585 Thread 0x000001f77fa69fd0 nmethod 14415 0x000001f70020c090 code [0x000001f70020c4a0, 0x000001f70020df48]
Event: 78.585 Thread 0x000001f77fa69fd0 14417   !   3       lombok.launch.ShadowClassLoader::getOrMakeJarListing (193 bytes)
Event: 78.586 Thread 0x000001f77fa69fd0 nmethod 14417 0x000001f70093b310 code [0x000001f70093b600, 0x000001f70093c8c8]
Event: 78.586 Thread 0x000001f77fa69fd0 14416   !   3       lombok.launch.ShadowClassLoader::getResourceFromLocation (243 bytes)
Event: 78.588 Thread 0x000001f77fa69fd0 nmethod 14416 0x000001f700eaa510 code [0x000001f700eaac60, 0x000001f700eae768]
Event: 78.598 Thread 0x000001f77fa68ff0 nmethod 14414 0x000001f707ce1410 code [0x000001f707ce1680, 0x000001f707ce2b30]
Event: 78.599 Thread 0x000001f77fa68ff0 14418       4       sun.net.www.protocol.jar.Handler::indexOfBangSlash (52 bytes)
Event: 78.603 Thread 0x000001f77fa68ff0 nmethod 14418 0x000001f707f9ef90 code [0x000001f707f9f120, 0x000001f707f9f2b8]
Event: 78.603 Thread 0x000001f77fa68ff0 14419       4       java.net.URLStreamHandler::setURL (159 bytes)
Event: 78.605 Thread 0x000001f77fa68ff0 nmethod 14419 0x000001f708397290 code [0x000001f708397420, 0x000001f708397688]
Event: 78.620 Thread 0x000001f77fa69fd0 14420       3       java.lang.CompoundEnumeration::hasMoreElements (5 bytes)
Event: 78.620 Thread 0x000001f77fa69fd0 nmethod 14420 0x000001f700795310 code [0x000001f7007954a0, 0x000001f7007955e8]
Event: 78.620 Thread 0x000001f77fa69fd0 14421       1       java.util.Collections$EmptyEnumeration::hasMoreElements (2 bytes)
Event: 78.621 Thread 0x000001f77fa69fd0 nmethod 14421 0x000001f7081b2390 code [0x000001f7081b2520, 0x000001f7081b25f8]
Event: 78.632 Thread 0x000001f77fa68ff0 14422       4       java.util.zip.ZipInputStream::getNextEntry (74 bytes)
Event: 78.683 Thread 0x000001f77fa68ff0 nmethod 14422 0x000001f7083d5710 code [0x000001f7083d5a60, 0x000001f7083d8188]
Event: 78.683 Thread 0x000001f77fa68ff0 14423   !   4       java.util.zip.ZipInputStream::readLOC (312 bytes)
Event: 78.708 Thread 0x000001f77fa68ff0 nmethod 14423 0x000001f707fed410 code [0x000001f707fed6a0, 0x000001f707feeea0]
Event: 78.802 Thread 0x000001f77fa68ff0 14424       4       java.lang.invoke.MethodType::makeImpl (109 bytes)

GC Heap History (20 events):
Event: 50.565 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 296960K, used 174438K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 1 survivors (2048K)
 Metaspace       used 42398K, committed 43904K, reserved 1114112K
  class space    used 4570K, committed 5056K, reserved 1048576K
}
Event: 50.570 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 296960K, used 158459K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 42398K, committed 43904K, reserved 1114112K
  class space    used 4570K, committed 5056K, reserved 1048576K
}
Event: 51.300 GC heap before
{Heap before GC invocations=45 (full 0):
 garbage-first heap   total 296960K, used 217851K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 30 young (61440K), 1 survivors (2048K)
 Metaspace       used 43337K, committed 44032K, reserved 1114112K
  class space    used 4682K, committed 5056K, reserved 1048576K
}
Event: 51.304 GC heap after
{Heap after GC invocations=46 (full 0):
 garbage-first heap   total 296960K, used 168530K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 43337K, committed 44032K, reserved 1114112K
  class space    used 4682K, committed 5056K, reserved 1048576K
}
Event: 51.924 GC heap before
{Heap before GC invocations=46 (full 0):
 garbage-first heap   total 296960K, used 225874K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 32 young (65536K), 4 survivors (8192K)
 Metaspace       used 44144K, committed 44736K, reserved 1114112K
  class space    used 4774K, committed 5056K, reserved 1048576K
}
Event: 51.928 GC heap after
{Heap after GC invocations=47 (full 0):
 garbage-first heap   total 296960K, used 170762K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 44144K, committed 44736K, reserved 1114112K
  class space    used 4774K, committed 5056K, reserved 1048576K
}
Event: 53.220 GC heap before
{Heap before GC invocations=47 (full 0):
 garbage-first heap   total 296960K, used 228106K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 30 young (61440K), 1 survivors (2048K)
 Metaspace       used 44444K, committed 44992K, reserved 1114112K
  class space    used 4793K, committed 5056K, reserved 1048576K
}
Event: 53.226 GC heap after
{Heap after GC invocations=48 (full 0):
 garbage-first heap   total 296960K, used 174499K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 44444K, committed 44992K, reserved 1114112K
  class space    used 4793K, committed 5056K, reserved 1048576K
}
Event: 58.710 GC heap before
{Heap before GC invocations=49 (full 0):
 garbage-first heap   total 325632K, used 231843K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 32 young (65536K), 3 survivors (6144K)
 Metaspace       used 42858K, committed 43520K, reserved 1114112K
  class space    used 4586K, committed 4928K, reserved 1048576K
}
Event: 58.716 GC heap after
{Heap after GC invocations=50 (full 0):
 garbage-first heap   total 325632K, used 173987K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 42858K, committed 43520K, reserved 1114112K
  class space    used 4586K, committed 4928K, reserved 1048576K
}
Event: 58.841 GC heap before
{Heap before GC invocations=50 (full 0):
 garbage-first heap   total 325632K, used 184227K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 2 survivors (4096K)
 Metaspace       used 42860K, committed 43520K, reserved 1114112K
  class space    used 4586K, committed 4928K, reserved 1048576K
}
Event: 58.850 GC heap after
{Heap after GC invocations=51 (full 0):
 garbage-first heap   total 325632K, used 155189K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 42860K, committed 43520K, reserved 1114112K
  class space    used 4586K, committed 4928K, reserved 1048576K
}
Event: 59.044 GC heap before
{Heap before GC invocations=51 (full 0):
 garbage-first heap   total 325632K, used 165429K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 1 survivors (2048K)
 Metaspace       used 42870K, committed 43520K, reserved 1114112K
  class space    used 4587K, committed 4928K, reserved 1048576K
}
Event: 59.054 GC heap after
{Heap after GC invocations=52 (full 0):
 garbage-first heap   total 593920K, used 144063K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 42870K, committed 43520K, reserved 1114112K
  class space    used 4587K, committed 4928K, reserved 1048576K
}
Event: 59.408 GC heap before
{Heap before GC invocations=52 (full 0):
 garbage-first heap   total 593920K, used 170687K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 14 young (28672K), 1 survivors (2048K)
 Metaspace       used 42876K, committed 43584K, reserved 1114112K
  class space    used 4587K, committed 4928K, reserved 1048576K
}
Event: 59.415 GC heap after
{Heap after GC invocations=53 (full 0):
 garbage-first heap   total 593920K, used 148476K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 42876K, committed 43584K, reserved 1114112K
  class space    used 4587K, committed 4928K, reserved 1048576K
}
Event: 68.086 GC heap before
{Heap before GC invocations=53 (full 0):
 garbage-first heap   total 593920K, used 375804K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 114 young (233472K), 2 survivors (4096K)
 Metaspace       used 44784K, committed 45312K, reserved 1114112K
  class space    used 4798K, committed 5056K, reserved 1048576K
}
Event: 68.093 GC heap after
{Heap after GC invocations=54 (full 0):
 garbage-first heap   total 593920K, used 160795K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 9 young (18432K), 9 survivors (18432K)
 Metaspace       used 44784K, committed 45312K, reserved 1114112K
  class space    used 4798K, committed 5056K, reserved 1048576K
}
Event: 74.185 GC heap before
{Heap before GC invocations=54 (full 0):
 garbage-first heap   total 593920K, used 402459K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 127 young (260096K), 9 survivors (18432K)
 Metaspace       used 46881K, committed 47424K, reserved 1114112K
  class space    used 5030K, committed 5248K, reserved 1048576K
}
Event: 74.194 GC heap after
{Heap after GC invocations=55 (full 0):
 garbage-first heap   total 593920K, used 170669K [0x0000000705800000, 0x0000000800000000)
  region size 2048K, 12 young (24576K), 12 survivors (24576K)
 Metaspace       used 46881K, committed 47424K, reserved 1114112K
  class space    used 5030K, committed 5248K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.010 Loaded shared library C:\Users\<USER>\.jdks\azul-17.0.11\bin\java.dll
Event: 0.025 Loaded shared library C:\Users\<USER>\.jdks\azul-17.0.11\bin\jsvml.dll
Event: 0.055 Loaded shared library C:\Users\<USER>\.jdks\azul-17.0.11\bin\net.dll
Event: 0.058 Loaded shared library C:\Users\<USER>\.jdks\azul-17.0.11\bin\nio.dll
Event: 0.077 Loaded shared library C:\Users\<USER>\.jdks\azul-17.0.11\bin\zip.dll
Event: 0.631 Loaded shared library C:\Users\<USER>\.jdks\azul-17.0.11\bin\jimage.dll
Event: 1.871 Loaded shared library D:\Programming_Tools\apache-maven-3.8.4\lib\jansi-native\Windows\x86_64\jansi.dll
Event: 1.884 Loaded shared library C:\Users\<USER>\.jdks\azul-17.0.11\bin\verify.dll
Event: 20.960 Loaded shared library C:\Users\<USER>\.jdks\azul-17.0.11\bin\sunmscapi.dll
Event: 21.697 Loaded shared library C:\Users\<USER>\.jdks\azul-17.0.11\bin\extnet.dll

Deoptimization events (20 events):
Event: 71.967 Thread 0x000001f76cbcf960 Uncommon trap: trap_request=0xffffff66 fr.pc=0x000001f707ee6478 relative=0x0000000000000138
Event: 71.967 Thread 0x000001f76cbcf960 Uncommon trap: reason=speculate_class_check action=maybe_recompile pc=0x000001f707ee6478 method=jdk.internal.reflect.UnsafeObjectFieldAccessorImpl.get(Ljava/lang/Object;)Ljava/lang/Object; @ 13 c2
Event: 71.967 Thread 0x000001f76cbcf960 DEOPT PACKING pc=0x000001f707ee6478 sp=0x00000018073fbdb0
Event: 71.967 Thread 0x000001f76cbcf960 DEOPT UNPACKING pc=0x000001f7075869a3 sp=0x00000018073fbce8 mode 2
Event: 71.967 Thread 0x000001f76cbcf960 Uncommon trap: trap_request=0xffffff66 fr.pc=0x000001f707ee6478 relative=0x0000000000000138
Event: 71.967 Thread 0x000001f76cbcf960 Uncommon trap: reason=speculate_class_check action=maybe_recompile pc=0x000001f707ee6478 method=jdk.internal.reflect.UnsafeObjectFieldAccessorImpl.get(Ljava/lang/Object;)Ljava/lang/Object; @ 13 c2
Event: 71.967 Thread 0x000001f76cbcf960 DEOPT PACKING pc=0x000001f707ee6478 sp=0x00000018073fbdb0
Event: 71.967 Thread 0x000001f76cbcf960 DEOPT UNPACKING pc=0x000001f7075869a3 sp=0x00000018073fbce8 mode 2
Event: 71.968 Thread 0x000001f76cbcf960 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001f707cd29f0 relative=0x0000000000000550
Event: 71.968 Thread 0x000001f76cbcf960 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001f707cd29f0 method=java.util.Hashtable.get(Ljava/lang/Object;)Ljava/lang/Object; @ 6 c2
Event: 71.968 Thread 0x000001f76cbcf960 DEOPT PACKING pc=0x000001f707cd29f0 sp=0x00000018073fc030
Event: 71.968 Thread 0x000001f76cbcf960 DEOPT UNPACKING pc=0x000001f7075869a3 sp=0x00000018073fbfe0 mode 2
Event: 72.483 Thread 0x000001f76cbcf960 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001f707cf3238 relative=0x00000000000004d8
Event: 72.483 Thread 0x000001f76cbcf960 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001f707cf3238 method=org.apache.maven.model.merge.ModelMerger.mergePlugin_Version(Lorg/apache/maven/model/Plugin;Lorg/apache/maven/model/Plugin;ZLjava/util/Map;)V @ 12 c2
Event: 72.483 Thread 0x000001f76cbcf960 DEOPT PACKING pc=0x000001f707cf3238 sp=0x00000018073fc7b0
Event: 72.483 Thread 0x000001f76cbcf960 DEOPT UNPACKING pc=0x000001f7075869a3 sp=0x00000018073fc750 mode 2
Event: 78.334 Thread 0x000001f76cbcf960 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001f7081f5dc0 relative=0x0000000000000780
Event: 78.335 Thread 0x000001f76cbcf960 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001f7081f5dc0 method=com.sun.tools.javac.comp.Resolve.findType(Lcom/sun/tools/javac/comp/Env;Lcom/sun/tools/javac/util/Name;)Lcom/sun/tools/javac/code/Symbol; @ 196 c2
Event: 78.335 Thread 0x000001f76cbcf960 DEOPT PACKING pc=0x000001f7081f5dc0 sp=0x00000018073fca00
Event: 78.335 Thread 0x000001f76cbcf960 DEOPT UNPACKING pc=0x000001f7075869a3 sp=0x00000018073fc9f0 mode 2

Classes unloaded (20 events):
Event: 53.298 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a4000 'lombok/javac/apt/MessagerDiagnosticsReceiver'
Event: 53.298 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a3b40 'lombok/javac/apt/InterceptingJavaFileManager'
Event: 53.298 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a3930 'lombok/permit/dummy/Parent'
Event: 53.298 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a3710 'lombok/core/CleanupRegistry'
Event: 53.298 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a3500 'lombok/permit/Permit$Fake'
Event: 53.298 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a32f8 'lombok/permit/Permit'
Event: 53.298 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a2db8 'lombok/core/DiagnosticsReceiver'
Event: 53.298 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a3000 'lombok/javac/apt/LombokProcessor$1'
Event: 53.299 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a2b18 'lombok/javac/apt/LombokProcessor'
Event: 53.299 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a28e0 'lombok/core/AnnotationProcessor$EcjDescriptor'
Event: 53.299 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a26a0 'lombok/core/AnnotationProcessor$JavacDescriptor'
Event: 53.299 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a2288 'lombok/core/AnnotationProcessor$ProcessorDescriptor'
Event: 53.299 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a2000 'lombok/core/AnnotationProcessor'
Event: 53.299 Thread 0x000001f77fa41110 Unloading class 0x000001f713514800 'lombok/launch/AnnotationProcessorHider$ClaimingProcessor'
Event: 53.299 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a1c68 'lombok/launch/AnnotationProcessorHider$AstModificationNotifierData'
Event: 53.299 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a1950 'lombok/launch/ShadowClassLoader'
Event: 53.299 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a1748 'lombok/launch/Main'
Event: 53.299 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a14c0 'lombok/launch/AnnotationProcessorHider$AnnotationProcessor'
Event: 53.299 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a1288 'org/apache/logging/log4j/core/config/plugins/processor/PluginCache'
Event: 53.299 Thread 0x000001f77fa41110 Unloading class 0x000001f7134a1000 'org/apache/logging/log4j/core/config/plugins/processor/PluginProcessor'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 78.320 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007209fa278}> (0x00000007209fa278) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.320 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007209fa460}> (0x00000007209fa460) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.321 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007209fb978}> (0x00000007209fb978) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.321 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007209fbba8}> (0x00000007209fbba8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.323 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007209fdbb0}> (0x00000007209fdbb0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.323 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007209fddb0}> (0x00000007209fddb0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.325 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x0000000720622768}> (0x0000000720622768) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.325 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x0000000720622918}> (0x0000000720622918) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.327 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x000000072067ccd8}> (0x000000072067ccd8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.327 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x000000072067ced0}> (0x000000072067ced0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.328 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x000000072067deb0}> (0x000000072067deb0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.328 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x000000072067e0f0}> (0x000000072067e0f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.341 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007207a6d70}> (0x00000007207a6d70) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.342 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007207a6f98}> (0x00000007207a6f98) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.343 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007207b64f8}> (0x00000007207b64f8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.343 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007207b66c8}> (0x00000007207b66c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.345 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007207dc150}> (0x00000007207dc150) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.346 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007207dc378}> (0x00000007207dc378) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.347 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007207dd3a8}> (0x00000007207dd3a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 78.347 Thread 0x000001f76cbcf960 Exception <a 'sun/nio/fs/WindowsException'{0x00000007207dd608}> (0x00000007207dd608) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]

VM Operations (20 events):
Event: 70.112 Executing VM operation: Cleanup
Event: 70.112 Executing VM operation: Cleanup done
Event: 71.115 Executing VM operation: Cleanup
Event: 71.115 Executing VM operation: Cleanup done
Event: 72.128 Executing VM operation: Cleanup
Event: 72.128 Executing VM operation: Cleanup done
Event: 73.129 Executing VM operation: Cleanup
Event: 73.129 Executing VM operation: Cleanup done
Event: 74.132 Executing VM operation: Cleanup
Event: 74.132 Executing VM operation: Cleanup done
Event: 74.185 Executing VM operation: G1CollectForAllocation
Event: 74.194 Executing VM operation: G1CollectForAllocation done
Event: 75.197 Executing VM operation: Cleanup
Event: 75.198 Executing VM operation: Cleanup done
Event: 76.211 Executing VM operation: Cleanup
Event: 76.211 Executing VM operation: Cleanup done
Event: 78.236 Executing VM operation: Cleanup
Event: 78.236 Executing VM operation: Cleanup done
Event: 78.338 Executing VM operation: HandshakeAllThreads
Event: 78.338 Executing VM operation: HandshakeAllThreads done

Events (20 events):
Event: 78.352 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f701196910
Event: 78.352 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f7011baf10
Event: 78.352 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f7011c8490
Event: 78.352 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f7011cf310
Event: 78.352 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f7011e3b10
Event: 78.352 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f7011f4f90
Event: 78.352 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f701200590
Event: 78.352 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f70120b210
Event: 78.353 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f701280890
Event: 78.353 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f701281c10
Event: 78.353 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f701282a90
Event: 78.353 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f7012ba410
Event: 78.353 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f7012ffd90
Event: 78.354 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f7013b8d90
Event: 78.354 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f701428510
Event: 78.355 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f7014e6c10
Event: 78.355 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f70150d310
Event: 78.356 Thread 0x000001f77fa70e20 flushing nmethod 0x000001f7015cb210
Event: 78.390 loading class jdk/internal/module/IllegalAccessLogger
Event: 78.390 loading class jdk/internal/module/IllegalAccessLogger done


Dynamic libraries:
0x00007ff66ffb0000 - 0x00007ff66ffbe000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\java.exe
0x00007ffd2be50000 - 0x00007ffd2c048000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffd2afa0000 - 0x00007ffd2b062000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffd29970000 - 0x00007ffd29c6e000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffd29610000 - 0x00007ffd29710000 	C:\Windows\System32\ucrtbase.dll
0x00007ffd22ef0000 - 0x00007ffd22f09000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\jli.dll
0x00007ffd27580000 - 0x00007ffd2759b000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\VCRUNTIME140.dll
0x00007ffd2a780000 - 0x00007ffd2a91d000 	C:\Windows\System32\USER32.dll
0x00007ffd298d0000 - 0x00007ffd298f2000 	C:\Windows\System32\win32u.dll
0x00007ffd2a6e0000 - 0x00007ffd2a70b000 	C:\Windows\System32\GDI32.dll
0x00007ffd294f0000 - 0x00007ffd29607000 	C:\Windows\System32\gdi32full.dll
0x00007ffd29dd0000 - 0x00007ffd29e6d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffd288b0000 - 0x00007ffd28b4a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16\COMCTL32.dll
0x00007ffd2a510000 - 0x00007ffd2a5ae000 	C:\Windows\System32\msvcrt.dll
0x00007ffd2af70000 - 0x00007ffd2af9f000 	C:\Windows\System32\IMM32.DLL
0x00007ffcf4240000 - 0x00007ffcf433d000 	C:\PROGRAM FILES (X86)\AGILE DGS\BIN\FF64.DLL
0x00007ffd2bdb0000 - 0x00007ffd2be05000 	C:\Windows\System32\SHLWAPI.dll
0x00007ffd2af40000 - 0x00007ffd2af48000 	C:\Windows\System32\PSAPI.DLL
0x00007ffd044a0000 - 0x00007ffd044bd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ffd25cc0000 - 0x00007ffd25cca000 	C:\Windows\SYSTEM32\Version.dll
0x00007ffd259e0000 - 0x00007ffd25a02000 	C:\PROGRAM FILES (X86)\AGILE DGS\BIN\DGStrategy64.dll
0x00007ffc7deb0000 - 0x00007ffc7e0b2000 	C:\Windows\system32\tmumh\20019\AddOn\8.53.0.1073\TmUmEvt64.dll
0x00007ffd2a3f0000 - 0x00007ffd2a4a1000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffd2a640000 - 0x00007ffd2a6df000 	C:\Windows\System32\sechost.dll
0x00007ffd2ada0000 - 0x00007ffd2aec3000 	C:\Windows\System32\RPCRT4.dll
0x00007ffd29710000 - 0x00007ffd29737000 	C:\Windows\System32\bcrypt.dll
0x0000000061b40000 - 0x0000000061cae000 	C:\Windows\system32\tmumh\20019\TmMon\2.8.0.1049\tmmon64.dll
0x00007ffd23620000 - 0x00007ffd236f1000 	C:\Program Files (x86)\Agile DGS\bin\Krn64.dll
0x00007ffd27760000 - 0x00007ffd2776c000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\vcruntime140_1.dll
0x00007ffd076a0000 - 0x00007ffd0772d000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\msvcp140.dll
0x00007ffc5f900000 - 0x00007ffc60569000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\server\jvm.dll
0x00007ffd29350000 - 0x00007ffd2939b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffd0af30000 - 0x00007ffd0af39000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffd2a710000 - 0x00007ffd2a77b000 	C:\Windows\System32\WS2_32.dll
0x00007ffd1c840000 - 0x00007ffd1c867000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffd29330000 - 0x00007ffd29342000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffd26d50000 - 0x00007ffd26d62000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffd27660000 - 0x00007ffd2766a000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\jimage.dll
0x00007ffd28e50000 - 0x00007ffd29034000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffd28c60000 - 0x00007ffd28c94000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffd29740000 - 0x00007ffd297c2000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffd1bd30000 - 0x00007ffd1bd55000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\java.dll
0x00007ffcf3c90000 - 0x00007ffcf3d67000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\jsvml.dll
0x00007ffd2b3a0000 - 0x00007ffd2bb0d000 	C:\Windows\System32\SHELL32.dll
0x00007ffd26450000 - 0x00007ffd26bf4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffd2aa40000 - 0x00007ffd2ad93000 	C:\Windows\System32\combase.dll
0x00007ffd287b0000 - 0x00007ffd287de000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffd2b070000 - 0x00007ffd2b13d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffd2a340000 - 0x00007ffd2a3ed000 	C:\Windows\System32\SHCORE.dll
0x00007ffd29420000 - 0x00007ffd29444000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffd0d870000 - 0x00007ffd0d889000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\net.dll
0x00007ffd234f0000 - 0x00007ffd235fd000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffd28490000 - 0x00007ffd284fa000 	C:\Windows\system32\mswsock.dll
0x00007ffd0d6b0000 - 0x00007ffd0d6c6000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\nio.dll
0x00007ffd0cd60000 - 0x00007ffd0cd78000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\zip.dll
0x0000000069ac0000 - 0x0000000069ae4000 	D:\Programming_Tools\apache-maven-3.8.4\lib\jansi-native\Windows\x86_64\jansi.dll
0x00007ffd276c0000 - 0x00007ffd276d0000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\verify.dll
0x00007ffd11d60000 - 0x00007ffd11d6e000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\sunmscapi.dll
0x00007ffd29c70000 - 0x00007ffd29dcc000 	C:\Windows\System32\CRYPT32.dll
0x00007ffd28880000 - 0x00007ffd288a7000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffd287e0000 - 0x00007ffd2881b000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffd291f0000 - 0x00007ffd29208000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffd27e90000 - 0x00007ffd27ec4000 	C:\Windows\system32\rsaenh.dll
0x00007ffd293e0000 - 0x00007ffd2940e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffd286c0000 - 0x00007ffd286cc000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffd281f0000 - 0x00007ffd2822b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffd2bb10000 - 0x00007ffd2bb18000 	C:\Windows\System32\NSI.dll
0x00007ffd22d20000 - 0x00007ffd22d37000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffd22d00000 - 0x00007ffd22d1d000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffd28230000 - 0x00007ffd282fa000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffd21e30000 - 0x00007ffd21e3a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffd23020000 - 0x00007ffd230a0000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffd20980000 - 0x00007ffd20989000 	C:\Users\<USER>\.jdks\azul-17.0.11\bin\extnet.dll
0x00007ffd291b0000 - 0x00007ffd291e3000 	C:\Windows\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\azul-17.0.11\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16;C:\PROGRAM FILES (X86)\AGILE DGS\BIN;C:\Windows\system32\tmumh\20019\AddOn\8.53.0.1073;C:\Windows\system32\tmumh\20019\TmMon\2.8.0.1049;C:\Users\<USER>\.jdks\azul-17.0.11\bin\server;D:\Programming_Tools\apache-maven-3.8.4\lib\jansi-native\Windows\x86_64

VM Arguments:
jvm_args: -Dclassworlds.conf=D:\Programming_Tools\apache-maven-3.8.4\bin\m2.conf -Dmaven.home=D:\Programming_Tools\apache-maven-3.8.4 -Dlibrary.jansi.path=D:\Programming_Tools\apache-maven-3.8.4\lib\jansi-native -Dmaven.multiModuleProjectDirectory=D:\Git\temp\sa-0201_base_tds_cloud\cloud-common 
java_command: org.codehaus.plexus.classworlds.launcher.Launcher clean deploy -Dmaven.test.skip=true -s D:\Programming_Tools\apache-maven-3.8.4\conf\gts-aliyun-global-settings-TDS.xml
java_class_path (initial): D:\Programming_Tools\apache-maven-3.8.4\boot\plexus-classworlds-2.6.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4202692608                                {product} {ergonomic}
   size_t MaxNewSize                               = 2520776704                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4202692608                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Users\<USER>\.jdks\azul-17.0.11
CLASSPATH=.;C:\Users\<USER>\.jdks\azul-17.0.11\lib;C:\Users\<USER>\.jdks\azul-17.0.11\lib\tools.jar;C:\Users\<USER>\.jdks\azul-17.0.11\jre\lib\rt.jar;C:\Users\<USER>\.jdks\azul-17.0.11\lib\dt.jar
PATH=D:\Programming_Tools\Conda\condabin;D:\Programming_Tools\VM16\bin\;C:\Program Files\Zulu\zulu-17\bin\;C:\Users\<USER>\.jdks\azul-17.0.11\bin;D:\Programming_Tools\jdk1.8.0_321;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Install_Location\Bandizip\;C:\Users\<USER>\.jdks\azul-17.0.11\jre\bin;D:\Programming_Tools\mysql-8.0.23-winx64\bin;D:\Programming_Tools\Erlang OTP\bin;D:\Programming_Tools\apache-maven-3.8.4\bin;D:\Programming_Tools\python;C:\Program Files\AsiaInfo Security\AisEsmAgent;D:\Programming_Tools\Git_2.47.0\cmd;D:\Programming_Tools\NODEJS\;D:\Programming_Tools\yarn\bin\;D:\Programming_Tools\Conda;D:\Programming_Tools\Conda\Scripts;D:\Programming_Tools\Conda\Library\mingw-w64\bin;D:\Programming_Tools\Conda\Library\bin;D:\Programming_Tools\python\Scripts\;D:\Programming_Tools\python;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;D:\Programming_Tools\PyCharm 2024.2.1\bin;;D:\Programming_Tools\IntelliJ IDEA 2024.2.1\bin;;D:\Programming_Tools\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Yarn\bin
USERNAME=wangxin213
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 154 Stepping 4, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5198)
OS uptime: 2 days 0:52 hours

CPU: total 12 (initial active 12) (12 cores per cpu, 1 threads per core) family 6 model 154 stepping 4 microcode 0x432, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb
Processor Information for processor 0
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1190
Processor Information for processor 1
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1190
Processor Information for processor 2
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 3
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 4
  Max Mhz: 1700, Current Mhz: 1200, Mhz Limit: 1190
Processor Information for processor 5
  Max Mhz: 1700, Current Mhz: 1200, Mhz Limit: 1190
Processor Information for processor 6
  Max Mhz: 1700, Current Mhz: 1200, Mhz Limit: 1190
Processor Information for processor 7
  Max Mhz: 1700, Current Mhz: 1200, Mhz Limit: 1190
Processor Information for processor 8
  Max Mhz: 1700, Current Mhz: 1200, Mhz Limit: 1190
Processor Information for processor 9
  Max Mhz: 1700, Current Mhz: 1200, Mhz Limit: 1190
Processor Information for processor 10
  Max Mhz: 1700, Current Mhz: 1200, Mhz Limit: 1190
Processor Information for processor 11
  Max Mhz: 1700, Current Mhz: 1200, Mhz Limit: 1190

Memory: 4k page, system-wide physical 16024M (4839M free)
TotalPageFile size 41624M (AvailPageFile size 3M)
current process WorkingSet (physical memory assigned to process): 721M, peak: 754M
current process commit charge ("private bytes"): 1211M, peak: 1213M

vm_info: OpenJDK 64-Bit Server VM (17.0.11+9-LTS) for windows-amd64 JRE (17.0.11+9-LTS) (Zulu17.50+19-CA), built on Apr  9 2024 07:23:02 by "zulu_re" with MS VC++ 16.10 / 16.11 (VS2019)

END.

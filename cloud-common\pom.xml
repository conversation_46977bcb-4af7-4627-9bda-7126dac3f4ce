<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ep.nacos</groupId>
        <artifactId>spring-cloud-nacos-dependencies-bastnew</artifactId>
        <version>7.0.0-rebate-SNAPSHOT</version>
    </parent>

    <groupId>com.qm.tds</groupId>
    <artifactId>tds-base-service-parent</artifactId>
    <version>7.1.0-rebate-SNAPSHOT</version>


    <description>TDS基础包父模块-</description>
    <packaging>pom</packaging>

    <modules>
        <module>ep-test-base-common</module>
        <module>tds-base-common</module>
        <module>tds-base-dynamic-starter</module>
<!--        <module>tds-base-lang-mysql</module>-->
<!--        <module>tds-base-lang-oracle</module>-->
        <module>tds-base-lock</module>
        <module>tds-base-mq</module>
        <module>tds-security-common</module>
        <module>tds-base-tds-file</module>
        <module>common-uiep-jar</module>
    </modules>

    <properties>
        <tds-base.version>7.0.0-rebate-SNAPSHOT</tds-base.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--启明私包 基础模块-->
            <dependency>
                <groupId>com.qm</groupId>
                <artifactId>common-uiep-jar</artifactId>
                <version>${tds-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qm.tds</groupId>
                <artifactId>ep-test-base-common</artifactId>
                <version>${tds-base.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.qm.tds</groupId>
                <artifactId>tds-base-common</artifactId>
                <version>${tds-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qm.tds</groupId>
                <artifactId>tds-base-dynamic-starter</artifactId>
                <version>${tds-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qm.tds</groupId>
                <artifactId>tds-base-lang-mysql</artifactId>
                <version>${tds-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qm.tds</groupId>
                <artifactId>tds-base-lang-oracle</artifactId>
                <version>${tds-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qm.tds</groupId>
                <artifactId>tds-base-lock</artifactId>
                <version>${tds-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qm.tds</groupId>
                <artifactId>tds-base-mq</artifactId>
                <version>${tds-base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qm.tds</groupId>
                <artifactId>tds-base-tds-file</artifactId>
                <version>${tds-base.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--swagger-->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <!--mybatis-plus-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <!-- 阿里fast JSON -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <!--start:视频获取某一帧的图片 -->
        <dependency>
            <groupId>org.jcodec</groupId>
            <artifactId>jcodec</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jcodec</groupId>
            <artifactId>jcodec-javase</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections4</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-compress</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>
        <!-- Nacos注册中心 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Nacos配置中心 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Spring Cloud Feign Starter -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!--lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!--redis-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.mysql</groupId>-->
<!--            <artifactId>mysql-connector-j</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>cn.com.kingbase</groupId>
            <artifactId>kingbase8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <!--新版本依赖调整-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-hystrix</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.netflix.archaius</groupId>
            <artifactId>archaius-core</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <target>17</target>
                    <source>17</source>
                    <encoding>UTF-8</encoding>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 上传jar包至私有maven仓库-->
    <distributionManagement>
        <repository>
            <id>dayu-maven-releases</id>
            <url>https://devops-nexus.faw.cn/repository/bp4655969726-releases/</url>
        </repository>
        <snapshotRepository>
            <id>dayu-maven-snapshots</id>
            <url>https://devops-nexus.faw.cn/repository/bp4655969726-snapshots/</url>
            <uniqueVersion>true</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

</project>

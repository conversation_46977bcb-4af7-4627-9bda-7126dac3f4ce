# tds-base-common  基础模块
## 版本1.0.0-SNAPSHOT 

### 2020-05-28 
1. 声明式事务配置文件添加  
2. 自定义乐观锁处理机制
### 2020-05-29 
1. 改为header中取基本信息 
2. 修改多语言保存bug
### 2020-06-02 
1. 获取map分页信息
### 2020-06-02 
1. 移除多语言
### 2020-06-11 
1. 增加stream分页工具 
2. 修改异常提醒 
3. 修改拦截header数据 
4. 修改list底层查询 
5. 处理线程共享
### 2020-06-19
1. 增加时间序列化
### 2020-06-22 
1. 加密工具类 实现aes加密、解密
### 2020-06-24 
1. 正则表达式工具类 
2. feign header传参
### 2020-07-02 
1. 修改声明式事务
### 2020-07-03 
1. 增加默认多语言
### 2020-07-08 
1. 增加swagger配置
2. 增加table工具类
### 2020-08-07 
1. LoginKey中增加operatorname与logintimestamp属性
2. BootAppUtil.getLoginKey()函数中增加对operatorname和logintimestamp字段处理
3. 完善BootAppUtil.getBizNo()函数
4. 调整ReadMe文件排版
### 2020-08-20
1. LoginKey中operatorname字段增加urldecode解码，防止中文乱码问题。
### 2020-11-26
1. DateUtils中增加addDays、addMonths函数，方便获取几天前、几月前的时间。
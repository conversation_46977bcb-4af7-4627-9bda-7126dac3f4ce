package com.qm.tds.api.aspect;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.impl.SizeLimitExceededException;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.dao.*;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.transaction.CannotCreateTransactionException;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.net.InetAddress.getLocalHost;

/**
 * AOP处理异步异常的公用切片代码，当出现服务器内部的时候，转换成标准的返回对象+异常信息。
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since 2020/05/28 08:32
 * @since 2021/03/30 10:47
 */
@Slf4j
@RestControllerAdvice
public class ControllerAspect {
    @Autowired
    private I18nUtil i18nUtil;
    private static final String TRACE_ID = "traceId";

    /**
     * 向响应信息存放traceId
     *
     * @param result 异常响应信息
     */
    private void setTraceId(JsonResultVo<Object> result) {
        //这里获取TraceId
        if (!BootAppUtil.isNullOrEmpty(MDC.get(TRACE_ID))) {
            result.setTraceId(MDC.get(TRACE_ID));
        }
    }

    /**
     * RecoverableDataAccessException
     * 如果应用程序执行某些恢复步骤并重试整个事务，或者在分布式事务的情况下，事务分支执行之前失败的操作可能成功，则引发数据访问异常。
     * 恢复操作至少必须包括关闭当前连接和获取新连接。
     *
     * @param ex RecoverableDataAccessException
     * @return 异常响应信息
     */
    @ExceptionHandler(RecoverableDataAccessException.class)
    @Order(3)
    public JsonResultVo<Object> recoverableDataAccessExceptionHandler(RuntimeException ex) {
        JsonResultVo<Object> result = new JsonResultVo<>();
        String message = i18nUtil.getMessage("ERR.basecommon.ControllerAspect.databaseInterrupt");
        QmException exSelf = new QmException(message, ex);
        result.setMsgErr(exSelf.getMessage(), exSelf);
        this.setTraceId(result);

        log.info("---error--"+ex.getMessage(), ex);
        return result;
    }

    /**
     * DataAccessResourceFailureException
     * 资源完全失败时引发的数据访问异常：
     * 例如，如果我们不能使用JDBC连接到数据库。
     *
     * @param ex DataAccessResourceFailureException
     * @return 异常响应信息
     */
    @ExceptionHandler(DataAccessResourceFailureException.class)
    @Order(3)
    public JsonResultVo<Object> dataAccessResourceFailureExceptionHandler(RuntimeException ex) {
        JsonResultVo<Object> result = new JsonResultVo<>();
        String message = i18nUtil.getMessage("ERR.basecommon.ControllerAspect.databaseCannotConn");
        QmException exSelf = new QmException(message, ex);
        result.setMsgErr(exSelf.getMessage(), exSelf);
        this.setTraceId(result);
        log.info("---error--"+ex.getMessage(), ex);
        return result;
    }

    /**
     * 尝试插入或更新数据导致违反主键或唯一约束时引发异常。
     *
     * @param ex DuplicateKeyException
     * @return 异常响应信息
     */
    @ExceptionHandler(DuplicateKeyException.class)
    @Order(3)
    public JsonResultVo<Object> duplicateKeyExceptionHandler(RuntimeException ex) {
        JsonResultVo<Object> result = new JsonResultVo<>();
        // Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '1' for key 'sysc030.sysc030_uk'
        String message = i18nUtil.getMessage("ERR.basecommon.ControllerAspect.saveFailUniqueExist");
        QmException exSelf = new QmException(message, ex);
        result.setMsgErr(exSelf.getMessage(), exSelf);
        log.info("---error--"+ex.getMessage(), ex);
        return result;
    }

    /**
     * 转义数据校验失败类型异常信息
     *
     * @param ex 数据校验失败类型异常
     * @return 异常响应信息
     */
     @ExceptionHandler(DataIntegrityViolationException.class)
    @Order(3)
    public JsonResultVo<Object> dataIntegrityViolationExceptionHandler(RuntimeException ex) {
        // 数据校验失败。字段超长、唯一键冲突等等。
        JsonResultVo<Object> result = new JsonResultVo<>();
        QmException exSelf;
        String errorMsg = ex.getCause().getMessage();
        if (errorMsg.startsWith("Data truncation: Data too long for column")) {
            // Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'VPERSONCODE' at row 1
            String[] errorMsgArray = errorMsg.split("'");
            if (errorMsgArray.length > 2) {
                String message = i18nUtil.getMessage("ERR.basecommon.ControllerAspect.columnLengthOverPrompt",errorMsgArray[1]);
                exSelf = new QmException(message, ex);
                //exSelf = new QmException("字段[" + errorMsgArray[1] + "]长度超过最大长度限制，请重新输入！", ex);
            } else {
                String message = i18nUtil.getMessage("ERR.basecommon.ControllerAspect.columnLengthOver");
                exSelf = new QmException(message, ex);
            }
            result.setMsgErr(exSelf.getMessage(), exSelf);
        } else if (errorMsg.endsWith("doesn't have a default value")) {
            // Cause: java.sql.SQLException: Field 'VUSERNAME' doesn't have a default value
            String[] errorMsgArray = errorMsg.split("'");
            if (errorMsgArray.length > 2) {
                String message = i18nUtil.getMessage("ERR.basecommon.ControllerAspect.columnNullPrompt",errorMsgArray[1]);
                exSelf = new QmException(message, ex);
                //exSelf = new QmException("字段[" + errorMsgArray[1] + "]不可以为空，请重新输入！", ex);
            } else {
                String message = i18nUtil.getMessage("ERR.basecommon.ControllerAspect.columnNull");
                exSelf = new QmException(message, ex);
            }
            result.setMsgErr(exSelf.getMessage(), exSelf);
        } else {
            result.setMsgErr(ex.getMessage(), ex);
        }
        this.setTraceId(result);
        log.info("--error--"+ex.getMessage(), ex);
        return result;
    }

    /**
     * 转义SQL类型异常信息
     *
     * @param ex SQL类型异常
     * @return 异常响应信息
     */
    @SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
    @ExceptionHandler(UncategorizedSQLException.class)
    @Order(3)
    public JsonResultVo<Object> uncategorizedSQLExceptionHandler(RuntimeException ex) {
        // 数据校验失败。字段超长、唯一键冲突等等。
        JsonResultVo<Object> result = new JsonResultVo<>();
        QmException exSelf;
        String errorMsg = ex.getCause().getMessage();
        if (errorMsg.startsWith("sql injection violation, syntax error:")) {
            // Cause: java.sql.SQLException: sql injection violation, syntax error: ERROR. pos 1732, line 67, column 244, token RPAREN :
            String message = i18nUtil.getMessage("ERR.basecommon.ControllerAspect.sqlFormatError");
            exSelf = new QmException(message, ex);
            result.setMsgErr(exSelf.getMessage(), exSelf);
        } else {
            result.setMsgErr(ex.getMessage(), ex);
        }
        this.setTraceId(result);
        log.info("---error--"+ex.getMessage(), ex);
        return result;
    }

    /**
     * 转义SQL类型异常信息,当无法使用底层事务API（如JTA）创建事务时引发异常。
     *
     * @param ex SQL类型异常
     * @return 异常响应信息
     */
    @ExceptionHandler(CannotCreateTransactionException.class)
    @Order(3)
    public JsonResultVo<Object> cannotCreateTransactionExceptionHandler(RuntimeException ex) {
        // 数据校验失败。字段超长、唯一键冲突等等。
        JsonResultVo<Object> result = new JsonResultVo<>();
        String message = i18nUtil.getMessage("ERR.basecommon.ControllerAspect.createTrancException");
        QmException exSelf = new QmException(message + ex.getMessage(), ex);
        result.setMsgErr(exSelf.getMessage(), exSelf);
        this.setTraceId(result);
        log.info("---error--"+ex.getMessage(), ex);
        return result;
    }

    /**
     * UndeclaredThrowableException异常处理器
     *
     * @param ex SQL类型异常
     * @return 异常响应信息
     */
    @ExceptionHandler({org.springframework.cglib.proxy.UndeclaredThrowableException.class,
            java.lang.reflect.UndeclaredThrowableException.class})
    @Order(3)
    public JsonResultVo<Object> cglibUndeclaredThrowableExceptionHandler(RuntimeException ex) {
        // 数据校验失败。字段超长、唯一键冲突等等。
        JsonResultVo<Object> result = new JsonResultVo<>();
        String message = i18nUtil.getMessage("ERR.basecommon.ControllerAspect.remoteServerInnerException");
        QmException exSelf = new QmException(message + ex.getMessage(), ex);
        result.setMsgErr(exSelf.getMessage(), exSelf);
        this.setTraceId(result);
        log.info("---error--"+ex.getMessage(), ex);
        return result;
    }

    /**
     * 附件大小限制异常
     * MultipartException subclass thrown when an upload exceeds the
     * maximum upload size allowed.
     *
     * @param ex MaxUploadSizeExceededException,SizeLimitExceededException
     * @return 异常响应信息
     */
    @ExceptionHandler({MaxUploadSizeExceededException.class, SizeLimitExceededException.class})
    @Order(3)
    public JsonResultVo<Object> maxUploadSizeExceededExceptionHandler(RuntimeException ex) {
        JsonResultVo<Object> result = new JsonResultVo<>();
        String msg = ex.getCause().getMessage();
        String limit = msg.substring(msg.indexOf("maximum") + 9, msg.length() - 1);
        String actual = msg.substring(msg.indexOf("size (") + 6, msg.indexOf(") exceeds"));
        String limitMB = (Long.parseLong(limit) / 1048576L) + "MB";
        String actualMB = (Long.parseLong(actual) / 1048576L) + "MB";
        String errorMsg = "附件大小限制异常！限制大小：" + limitMB + ",上传的文件大小" + actualMB + ";";
        result.setMsgErr(errorMsg, null);
        log.info("---error--"+"[-ControllerAspect-].maxUploadSizeExceededException");
        this.setTraceId(result);
        log.info("---error--"+ex.getMessage(), ex);
        return result;
    }

    /**
     * 查询时连接超时异常
     *
     * @param ex QueryTimeoutException
     * @return 异常响应信息
     */
    @ExceptionHandler({QueryTimeoutException.class})
    @Order(3)
    public JsonResultVo<Object> queryTimeoutExceptionHandler(QueryTimeoutException ex) {
        JsonResultVo<Object> result = new JsonResultVo<>();
        String msg;
        if (Objects.requireNonNull(ex.getMessage()).contains("Redis command timed out")) {
            String message = i18nUtil.getMessage("ERR.basecommon.ControllerAspect.redisConnOvertime");
            msg = message;
            try {
                InetAddress localHost = getLocalHost();
                log.info("---error--"+"[-ControllerAspect-].queryTimeoutException:redis连接超时，本地ip为={}", localHost.getHostAddress());
            } catch (UnknownHostException e) {
                log.info("---error--"+"[-ControllerAspect-].queryTimeoutException:redis连接超时并且无法获取本地ip", e);
            }
        } else {
            msg = ex.getMessage();
        }
        result.setMsgErr(msg, null);
        this.setTraceId(result);
        log.info("---error--"+ex.getMessage(), ex);
        return result;
    }

    /**
     * HTTP请求入参读取异常
     *
     * @param ex HttpMessageNotReadableException
     * @return 异常响应信息
     */
    @ExceptionHandler({HttpMessageNotReadableException.class})
    @Order(3)
    public JsonResultVo<Object> httpMessageNotReadableExceptionHandler(HttpMessageNotReadableException ex) {
        JsonResultVo<Object> result = new JsonResultVo<>();
        String message = i18nUtil.getMessage("ERR.basecommon.ControllerAspect.httpRequestParamReadException");
        result.setMsgErr(message, ex);
        this.setTraceId(result);
        log.info("---error--"+ex.getMessage(), ex);
        return result;
    }

    /**
     * 最终异常处理器，优先级比上述优先级低，当上述异常处理器没有拦截到的异常，将由此异常处理器处理异常
     *
     * @param ex RuntimeException
     * @return 异常响应信息
     */
    @ExceptionHandler(RuntimeException.class)
    @Order(4)
    public JsonResultVo<Object> runtimeExceptionHandler(RuntimeException ex) {
        JsonResultVo<Object> result = new JsonResultVo<>();
        QmException exSelf;
        //多动态数据源错误特殊处理
        if ((ex.getMessage() != null && ex.getMessage().contains("dynamic-datasource could not find a datasource named"))) {
            String message = i18nUtil.getMessage("ERR.basecommon.ControllerAspect.datasourceConfigWrong");
            exSelf = new QmException(message + ex.getMessage(), ex);
            result.setMsgErr(exSelf.getMessage(), exSelf);
        } else {
            result.setMsgErr(ex.getMessage(), ex);
        }
        this.setTraceId(result);
        log.info("---error--"+ex.getMessage(), ex);
        return result;
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @Order(3)
    public JsonResultVo<Object> methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException ex) {
        JsonResultVo<Object> result = new JsonResultVo<>();

        List<String> msgList = ex.getBindingResult().getAllErrors()
                .parallelStream()
                .map(t -> t.getDefaultMessage())
                .collect(Collectors.toList());

        String vMsg = "";
        if (CollectionUtils.isEmpty(msgList)) {
            vMsg = ex.getMessage();
        } else {
            if (msgList.size() > 1) {
                vMsg = "共" + msgList.size() + "项信息：\n";
            }
            vMsg += String.join("\n", msgList);
        }

        result.setMsgErr(vMsg);
        this.setTraceId(result);
        log.info("---error--"+ex.getMessage(), ex);
        return result;
    }
}

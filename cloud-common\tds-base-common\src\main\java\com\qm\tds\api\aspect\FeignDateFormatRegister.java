package com.qm.tds.api.aspect;

import com.qm.tds.util.DateUtils;
import org.springframework.cloud.openfeign.FeignFormatterRegistrar;
import org.springframework.core.convert.converter.Converter;
import org.springframework.format.FormatterRegistry;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <p>FeignDateFormatRegister</p>
 * <p>
 * Feign调用时对参数进行格式化处理：
 * <ul>
 *     <li>Date型转换为String型，防止日期精度有问题</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> wjq
 * @date 2021/5/23
 */
@Component
public class FeignDateFormatRegister implements FeignFormatterRegistrar {

    @Override
    public void registerFormatters(FormatterRegistry registry) {
        registry.addConverter(Date.class, String.class, new Date2StringConverter());
    }

    private class Date2StringConverter implements Converter<Date, String> {
        @Override
        public String convert(Date source) {
            return DateUtils.format(source, DateUtils.DATE_TIME_PATTERN);
        }
    }
}

package com.qm.tds.api.aspect;

import com.qm.tds.api.hystrix.QmHystrix;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Collection;
import java.util.Enumeration;

/**
 * Feign远程调用请求拦截器。
 * 目的是传递request中的通用请求参数。
 * <p>
 * 同理要更改yml文件中hystrix.command.default.execution.isolation.strategy属性的值为SEMAPHORE，否则在RequestInterceptor 中无法转发request请求。
 */
@Component
@Slf4j
public class FeignRequestInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        boolean appendToken = needAppendToken(requestTemplate);
        log.debug("feign调用url:" + requestTemplate.url());
        log.debug("feign调用request url:" + requestTemplate.request().url());
        // 传递 parameter 和 header
        if (appendToken) {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

            if (attributes == null) {
                log.warn("获取不到ServletRequestAttributes对象，feign无法传递参数！");
            } else {
                HttpServletRequest request = attributes.getRequest();
                //传递Request Parameter中通用字段
                this.appendRequestParameter(requestTemplate, request);

                //传递Request Header中通用字段
                this.appendRequestHeader(requestTemplate, request);
            }
        } else {
            log.debug("feign不需要传递 parameter 和 header");
        }
    }

    /**
     * 判断是否需要附加传递 parameter 和 header
     *
     * @param requestTemplate Fegin对象
     * @return true：需要附加； false：不需要附加
     */
    private boolean needAppendToken(RequestTemplate requestTemplate) {
        boolean appendToken = true;
        try {
            Collection<String> values = requestTemplate.queries().get(QmHystrix.KEY_CLEAR_TOKEN);
            if (values != null) {
                for (String value : values) {
                    if ("true".equalsIgnoreCase(value) || "1".equals(value)) {
                        appendToken = false;
                    }
                }
            }
        } catch (Exception ex) {
            log.info("---error--"+"判断是否需要附带token异常！", ex);
            appendToken = true;
        }
        return appendToken;
    }

    /**
     * 传递RequestParameter中通用字段
     *
     * @param requestTemplate Fegin对象
     * @param request         需要传递的request对象
     */
    private void appendRequestParameter(RequestTemplate requestTemplate, HttpServletRequest request) {
        StringBuilder sb = new StringBuilder();
        if (request != null) {
            Enumeration<String> paraNames = request.getParameterNames();
            while (paraNames != null && paraNames.hasMoreElements()) {
                String name = paraNames.nextElement();
                String value = request.getParameter(name);
                requestTemplate.query(name, value);
                // 拼装日志
                sb.append("[").append(name).append(":").append(value).append("]");
            }
        }
        log.debug("feign传递parameter:" + sb.toString());
    }

    /**
     * 该函数用于给请求模板添加请求头。
     * 它会遍历一个指定的请求头列表（SPECIFIED_HEADERS），从HttpServletRequest中获取对应的请求头值，如果该请求头在请求模板中不存在，则将其添加到请求模板中。最后，该函数还会打印出请求模板中的所有请求头。
     * 遍历指定的请求头列表
     * 获取请求头值并判断是否已存在于请求模板中
     * 若不存在，则将请求头添加到请求模板中
     * 打印请求模板中的所有请求头
     *
     * @param requestTemplate Fegin对象
     * @param request         需要传递的request对象
     */
    private void appendRequestHeader(RequestTemplate requestTemplate, HttpServletRequest request) {
        StringBuilder sb = new StringBuilder();
        if (request != null) {
            Enumeration<String> headerNames = request.getHeaderNames();
            if (headerNames != null) {
                while (headerNames.hasMoreElements()) {
                    String name = headerNames.nextElement();
                    String values = request.getHeader(name);
                    // 如果RequestTemplate中包含相同的Header则使用RequestTemplate 中的为主
                    if (!requestTemplate.headers().containsKey(name)) {
                        requestTemplate.header(name, values);
                    }

                    // 拼装日志
                    sb.append("[").append(name).append(":").append(values).append("]");
                }
            }
        }
        log.debug("feign传递header:" + sb.toString());
    }
}

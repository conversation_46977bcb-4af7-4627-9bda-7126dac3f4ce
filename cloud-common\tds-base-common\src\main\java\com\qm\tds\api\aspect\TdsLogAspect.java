package com.qm.tds.api.aspect;

import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 自定义日志字段
 */
@Aspect
@Component
@Slf4j
@Order(1)
public class TdsLogAspect {

    @Pointcut("execution( * com.qm.*.*.controller..*.*(..))")
    private void tdsLogPointCut() {
        // 针对controller层的代码做拦截。通过控制台输出请求入出参。
    }

    @Before("tdsLogPointCut()")
    private void doBefore(JoinPoint joinPoint) {
        //用户信息
        LoginKeyDO loginKeyDO = BootAppUtil.getLoginKey();
        // 日志中记录当前操作员信息
        MDC.put("ep-opr-id", loginKeyDO.getOperatorId());
        MDC.put("ep-opr-code", loginKeyDO.getPersonCode());
        MDC.put("ep-opr-name", loginKeyDO.getOperatorName());
        MDC.put("ep-logintimestamp", loginKeyDO.getLoginTimestamp());
        MDC.put("ep-tenant-id", loginKeyDO.getTenantId());
        MDC.put("ep-com-id", loginKeyDO.getCompanyId());
    }

    @AfterReturning(pointcut = "tdsLogPointCut()")
    private void doAfterReturning() {
        // 清理MDC信息防止堆栈溢出
        MDC.clear();
    }
}

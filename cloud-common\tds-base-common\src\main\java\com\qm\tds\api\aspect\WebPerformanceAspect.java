package com.qm.tds.api.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.JSONUtils;
import com.qm.tds.util.RedisUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description: AOP切片: 记录所有web请求性能情况
 * @date 2020/5/27 14:40
 */
@Aspect
@Component
@Slf4j
@Order(2)
public class WebPerformanceAspect {

    private static final int LIMIT_LOG_DATA_SIZE = 150;

    @Pointcut("execution( * com.qm.*.*.controller..*.*(..))")
    private void webPerformancePointCut() {
        // 针对controller层的代码做拦截。通过控制台输出请求入出参。
    }

    @Autowired
    private LoginKeyDO loginKeyDO;

    @Autowired
    private RedisUtils redisUtils;

    @Before("webPerformancePointCut()")
    private void doBefore(JoinPoint joinPoint) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null != attributes) {
            HttpServletRequest request = attributes.getRequest();
            //用户信息
            loginKeyDO = BootAppUtil.getLoginKey();
            log.debug("WebPerformanceAspect--> HTTP METHOD : " + request.getMethod());
            log.debug("WebPerformanceAspect--> CLASS_METHOD : " + joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName());
            log.debug("WebPerformanceAspect--> HTTP IN PARAMETER JSON STRING : " + Arrays.toString(joinPoint.getArgs()));
        }
    }

    @AfterReturning(returning = "returnJsonObj", pointcut = "webPerformancePointCut()")
    private void doAfterReturning(Object returnJsonObj) {
        boolean printCommonLog = true;
        try {
            if (returnJsonObj instanceof JsonResultVo) {
                JsonResultVo ret = (JsonResultVo) returnJsonObj;
                if (ret.getData() instanceof QmPage) {
                    QmPage<?> pageData = (QmPage<?>) ret.getData();
                    if (pageData != null && !CollectionUtils.isEmpty(pageData.getItems()) && pageData.getItems().size() > LIMIT_LOG_DATA_SIZE) {
                        log.debug("WebPerformanceAspect--> HTTP RESPONSE VALUE : Data-Page共{}条，超过{}条不输出日志Page！",
                                pageData.getItems().size(), LIMIT_LOG_DATA_SIZE);
                        printCommonLog = false;
                    } else {
                        JSONObject resObj = JSON.parseObject(JSONUtils.beanToJson(returnJsonObj));
                        //分页数据解析
                        if (BootAppUtil.isNullOrEmpty(resObj.getJSONObject("data"))) {
                            return;
                        }
                        JSONObject data = resObj.getJSONObject("data");
                        if (data.get("items") == null) {
                            return;
                        }
                        JSONArray itemsList = JSON.parseArray(data.get("items").toString());
                        List repList = authTableCustom(itemsList);
                        if (!repList.isEmpty()) {
                            pageData.setItems(repList);
                            ret.setData(pageData);
                        }
                    }
                } else if (ret.getData() instanceof List) {
                    List<?> dataList = (List<?>) ret.getData();
                    if (!CollectionUtils.isEmpty(dataList) && dataList.size() > LIMIT_LOG_DATA_SIZE) {
                        log.debug("WebPerformanceAspect--> HTTP RESPONSE VALUE : Data-List共{}条，超过{}条不输出日志List！",
                                dataList.size(), LIMIT_LOG_DATA_SIZE);
                        printCommonLog = false;
                    }
                } else if (ret.getData() instanceof Map) {
                    Map<?, ?> dataMap = (Map<?, ?>) ret.getData();
                    if (!CollectionUtils.isEmpty(dataMap) && dataMap.size() > LIMIT_LOG_DATA_SIZE) {
                        log.debug("WebPerformanceAspect--> HTTP RESPONSE VALUE : Data-Map共{}条Key，超过{}条不输出日志Map！",
                                dataMap.size(), LIMIT_LOG_DATA_SIZE);
                        printCommonLog = false;
                    }
                } else if (!CollectionUtils.isEmpty(ret.getDataList()) && ret.getDataList().size() > LIMIT_LOG_DATA_SIZE) {
                    log.debug("WebPerformanceAspect--> HTTP RESPONSE VALUE : DataList共{}条，超过{}条不输出日志3！",
                            ret.getDataList().size(), LIMIT_LOG_DATA_SIZE);
                    printCommonLog = false;
                }
            }
        } catch (Exception ex) {
            log.info("---error--"+"WebPerformanceAspect--> HTTP RESPONSE VALUE : 打印日志报错！" + ex.getMessage(), ex);
            printCommonLog = false;
        } finally {
            if (printCommonLog) {
                if (returnJsonObj == null) {
                    log.debug("WebPerformanceAspect--> HTTP RESPONSE VALUE : ");
                } else {
                    log.debug("WebPerformanceAspect--> HTTP RESPONSE VALUE : {}",
                            JSONUtils.beanToJson(returnJsonObj));
                }
            }
        }
    }

    @Around("webPerformancePointCut()")
    private Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        Object ob = null;
        long startTime = System.currentTimeMillis();
        ob = pjp.proceed();
        long timeCost = System.currentTimeMillis() - startTime;
        log.debug("WebPerformanceAspect--> TIME CONSUMING : " + timeCost + " ms");
        return ob;
    }


    public List authTableCustom(List itemsList) {
        List responseList = new ArrayList();
        log.info("-----items-------" + itemsList);

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null != attributes) {
            HttpServletRequest request = attributes.getRequest();
            String uri = request.getRequestURI();
            String operatorId = BootAppUtil.getLoginKey().getOperatorId();

            String uiKey = this.getRediesKey();//查询固定redis的key
            Object obj = redisUtils.get(uiKey);
            log.info("缓存中获取全部权限信息:::" + obj);

            if(!BootAppUtil.isNullOrEmpty(obj)){
                JSONArray repArr = JSON.parseArray(obj.toString());
                log.info("------repList-----" + repArr);
                String columninfo = "";
                String reverse = "";
                for (int i = 0; i < repArr.size(); i++) {
                    JSONObject jsonObj = repArr.getJSONObject(i);
                    String dataurl = jsonObj.get("dataurl").toString();
                    String actionid = jsonObj.get("actionid").toString();
                    if (uri.equals(dataurl) && actionid.equals(operatorId)) {
                        //找到权限信息
                        columninfo = jsonObj.get("columninfo").toString();
                        reverse = jsonObj.get("reverse").toString();
                    }
                }

                if (!"".equals(columninfo)) {
                    JSONArray columnArr = JSON.parseArray(columninfo);

                    for (Object value : itemsList) {
                        JSONObject tempObj = JSON.parseObject(value.toString());
                        JSONObject reObj = new JSONObject();
                        if ("0".equals(reverse)) {//0为正向，1为反向
                            columnArr.stream().map(o -> JSON.parseObject(o.toString()))
                                    .map(jsonObject -> jsonObject.getString("name"))
                                    .filter(tempObj::containsKey)
                                    .forEach(s -> {
                                        tempObj.remove(s);
                                        log.info("---移除字段---column-----" + s);
                                    });
                            responseList.add(tempObj);
                        } else if ("1".equals(reverse)) {//0为正向，1为反向
                            columnArr.stream().map(o -> JSON.parseObject(o.toString()))
                                    .map(jsonObject -> jsonObject.getString("name"))
                                    .filter(tempObj::containsKey)
                                    .forEach(s -> {
                                        String val = tempObj.getString(s);
                                        reObj.put(s, val);
                                    });
                            responseList.add(reObj);
                        }
                    }
                }
            }
        }
        return responseList;
    }

    private String getRediesKey() {
        return redisUtils.keyBuilder("ui", "TableColumnCustom", "key", "authcustom", "module");

    }
}

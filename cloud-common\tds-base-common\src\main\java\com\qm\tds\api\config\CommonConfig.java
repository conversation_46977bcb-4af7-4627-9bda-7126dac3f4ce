package com.qm.tds.api.config;

import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CommonConfig {
    @Bean
    TomcatConnectorCustomizer disableFacadeDiscard() {
        return (connector) -> connector.setDiscardFacades(false);
    }
}

package com.qm.tds.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.context.MessageSourceProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.time.Duration;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 多语言多配置配置器
 *
 * <AUTHOR>
 * @since 2022/1/7 16:10
 */
@Configuration
@Slf4j
public class I18nProperties {
    @Autowired
    private ApplicationContext applicationContext;

    @Bean
    public MessageSourceProperties messageSourceProperties() throws IOException {
        String property = applicationContext.getEnvironment().getProperty("spring.messages.basename");
        if (!StringUtils.isEmpty(property)) {
            throw new RuntimeException("基于框架要求，spring.messages.basename此配置会导致多语言配置混乱，" +
                    "请使用其他配置名称或联系开发人员修改源码");
        }
        ClassLoader classLoader = applicationContext.getClassLoader();
        Resource[] resources = new PathMatchingResourcePatternResolver(classLoader)
                .getResources("classpath*:*.properties");
        Set<String> basenames = Stream.of(resources).map(resource -> {
            String propertiesName = resource.getFilename();
            log.info("-------------------------classpath*:*.properties:" + propertiesName + "------------------------------");
            assert propertiesName != null;
            if (!propertiesName.contains("messages")) {
                return null;
            }
            String[] messages = propertiesName.split("messages");
            return messages[0] + "messages";
        }).filter(Objects::nonNull).collect(Collectors.toSet());
        MessageSourceProperties messageSourceProperties = new MessageSourceProperties();
        messageSourceProperties.setBasename(String.join(",", basenames));
        return messageSourceProperties;
    }

    @Bean
    public MessageSource messageSource(MessageSourceProperties properties) {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        if (StringUtils.hasText(properties.getBasename())) {
            messageSource.setBasenames(StringUtils.commaDelimitedListToStringArray(
                    StringUtils.trimAllWhitespace(properties.getBasename())));
        }
        if (properties.getEncoding() != null) {
            messageSource.setDefaultEncoding(properties.getEncoding().name());
        }
        messageSource.setFallbackToSystemLocale(properties.isFallbackToSystemLocale());
        Duration cacheDuration = properties.getCacheDuration();
        if (cacheDuration != null) {
            messageSource.setCacheMillis(cacheDuration.toMillis());
        }
        messageSource.setAlwaysUseMessageFormat(properties.isAlwaysUseMessageFormat());
        messageSource.setUseCodeAsDefaultMessage(properties.isUseCodeAsDefaultMessage());
        return messageSource;
    }
}

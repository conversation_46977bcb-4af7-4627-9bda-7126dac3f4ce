package com.qm.tds.api.config;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.baomidou.mybatisplus.extension.incrementer.OracleKeyGenerator;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;

import java.sql.Timestamp;
import java.util.Date;


/**
 * @description: mybatisPlus 配置类
 * @author: Cyl
 * @time: 2020/5/27 14:52
 */

@Configuration
public class MybatisPlusConfig {


/**
     * 乐观锁*/


@Bean
public MybatisPlusInterceptor mybatisPlusInterceptor() {
    MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

    // 乐观锁插件（保持原配置）
    interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

    // 新版分页插件配置（核心改动点）
    PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor();
    paginationInterceptor.setMaxLimit(-1L);
    paginationInterceptor.setOverflow(false); // 替代旧版 setOverflow 语义

    // 新版已内置 COUNT 优化，无需单独设置 JsqlExParserCountOptimize
    interceptor.addInnerInterceptor(paginationInterceptor);

    return interceptor;
}


    /**
     * oracle 自定义主键*/


    @Bean
    public OracleKeyGenerator oracleKeyGenerator() {
        return new OracleKeyGenerator();
    }


    //@Bean
    public HttpMessageConverters fastJsonHttpMessageConverters() {
        FastJsonHttpMessageConverter fastJsonHttpMessageConverter = new FastJsonHttpMessageConverter();
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setSerializerFeatures(SerializerFeature.PrettyFormat,
                SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullStringAsEmpty, SerializerFeature.WriteNullListAsEmpty
        );
        fastJsonConfig.setSerializeFilters((ValueFilter) (o, s, source) -> {
            if (source == null) {
                return null;
            }
            if (source instanceof Timestamp) {
                return source;
            }
            if (source instanceof Date) {
                return DateFormatUtils.format((Date) source, "yyyy-MM-dd HH:mm:ss");
            }
            return source;
        });
        fastJsonHttpMessageConverter.setFastJsonConfig(fastJsonConfig);
        HttpMessageConverter<?> converter = fastJsonHttpMessageConverter;
        return new HttpMessageConverters(converter);
    }
}

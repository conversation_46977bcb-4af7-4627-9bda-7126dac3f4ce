package com.qm.tds.api.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @description: 自动补充插入或更新时的值  提高性能加判断
 * @author: Cyl
 * @time: 2020/5/27 14:30
 */
@Component
public class MybatisPlusMetaObjectHandler implements MetaObjectHandler {

    private String updateOn = "updateOn";
    private String updateBy = "updateBy";
    private String dtstamp = "dtstamp";

    @Override
    public void insertFill(MetaObject metaObject) {
        Date now = new Date();
        if (metaObject.hasSetter("createOn")) {
            setFieldValByName("createOn", now, metaObject);
        }

        if (metaObject.hasSetter("createBy")) {
            setFieldValByName("createBy", BootAppUtil.getLoginKey().getOperatorId(), metaObject);
        }

        if (metaObject.hasSetter(updateOn)) {
            setFieldValByName(updateOn, now, metaObject);
        }

        if (metaObject.hasSetter(updateBy)) {
            setFieldValByName(updateBy, BootAppUtil.getLoginKey().getOperatorId(), metaObject);
        }

        if (metaObject.hasSetter(dtstamp) && BootAppUtil.isNullOrEmpty(getFieldValByName(dtstamp, metaObject))) {
            setFieldValByName(dtstamp, DateUtils.getSysTimestamp(), metaObject);
        }


    }

    @Override
    public void updateFill(MetaObject metaObject) {

        if (metaObject.hasSetter(updateOn)) {
            setFieldValByName(updateOn, new Date(), metaObject);
        }

        if (metaObject.hasSetter(updateBy)) {
            setFieldValByName(updateBy, BootAppUtil.getLoginKey().getOperatorId(), metaObject);
        }
    }

}

package com.qm.tds.api.config;

import com.qm.tds.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.support.GenericConversionService;
import org.springframework.web.bind.support.ConfigurableWebBindingInitializer;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;

import javax.annotation.PostConstruct;
import java.util.Date;

/**
 * <p>RequestParaTypeConvertConfig</p>
 * <p>
 * 补充对Request参数类型转换实现类
 * </p>
 *
 * <AUTHOR> wjq
 * @date 2021/5/23
 */
@Configuration
public class RequestParaTypeConvertConfig {

    @Autowired
    private RequestMappingHandlerAdapter handlerAdapter;

    /**
     * 增加字符串转日期的功能
     */
    @PostConstruct
    public void initEditableValidation() {
        ConfigurableWebBindingInitializer initializer = (ConfigurableWebBindingInitializer) handlerAdapter.getWebBindingInitializer();
        if (initializer.getConversionService() != null) {
            GenericConversionService genericConversionService = (GenericConversionService) initializer.getConversionService();
            genericConversionService.addConverter(String.class, Date.class, new String2DateConverter());
        }
    }

    class String2DateConverter implements Converter<String, Date> {
        @Override
        public Date convert(String source) {
            //TODO 这里应该对日期格式自动识别，而不是应该写死的一个
            return DateUtils.parse(source, DateUtils.DATE_TIME_PATTERN);
        }
    }
}

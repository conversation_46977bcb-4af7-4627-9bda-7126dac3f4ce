package com.qm.tds.api.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.License;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
@ConditionalOnProperty(prefix = "tds.swagger-ui", value = {"enabled"}, havingValue = "true",matchIfMissing = true)
public class Swagger3Config {

    @Bean
    public OpenAPI customOpenAPI(@Value("${spring.application.name}") String applicationName) {
        return new OpenAPI()
                .info(new io.swagger.v3.oas.models.info.Info()
                        .title(applicationName)
                        .version("1.0")
                        .description(applicationName)
                        .license(new License().name("Apache 2.0").url("http://www.apache.org/licenses/LICENSE-2.0"))
                        .contact(new io.swagger.v3.oas.models.info.Contact().name("QM").url("https://www.qm.cn"))
                );
    }
}

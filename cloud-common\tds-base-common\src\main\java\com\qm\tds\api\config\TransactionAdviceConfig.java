package com.qm.tds.api.config;

import org.aspectj.lang.annotation.Aspect;
import org.springframework.aop.Advisor;
import org.springframework.aop.aspectj.AspectJExpressionPointcut;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionManager;
import org.springframework.transaction.interceptor.DefaultTransactionAttribute;
import org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource;
import org.springframework.transaction.interceptor.TransactionInterceptor;

/**
 * @description: 声明式事务  Exception 可检查异常需要手动处理
 * @author: Cyl
 * @time: 2020/5/28 18:28
 */
@Aspect
@Configuration
public class TransactionAdviceConfig {
    // @Autowired
    // @Lazy
    // private TransactionManager transactionManager;

    private static final String AOP_POINTCUT_EXPRESSION_CONTROLLER = "execution(* com.qm.ep.*.controller..*(..))|| execution(* com.qm.tds.*.controller..*(..))";

    @Autowired
    private TransactionManager transactionManager;

    // @Bean
    // public PlatformTransactionManager txManager(DataSource dataSource) {
    //     return new DataSourceTransactionManager(dataSource);
    // }
    @Bean
    public TransactionInterceptor txAdviceController() {
        DefaultTransactionAttribute txAttrRequired = new DefaultTransactionAttribute();
        txAttrRequired.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        NameMatchTransactionAttributeSource source = new NameMatchTransactionAttributeSource();
        source.addTransactionalMethod("*", txAttrRequired);
        return new TransactionInterceptor(transactionManager, source);
    }

    @Bean
    public Advisor txAdviceAdvisorController() {
        AspectJExpressionPointcut pointcut = new AspectJExpressionPointcut();
        pointcut.setExpression(AOP_POINTCUT_EXPRESSION_CONTROLLER);
        return new DefaultPointcutAdvisor(pointcut, txAdviceController());
    }
}

package com.qm.tds.api.controller;

import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller基类
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
@Slf4j
public class BaseController {

    public BaseController() {
        // Do something
    }

    protected LoginKeyDO getUserInfo() {
        return BootAppUtil.getLoginKey();
    }

    private static final String GRID_SORT_BY = "tsortby";
    private static final String GRID_FILTER = "theadFilter";
    private static final String GRID_PAGE_CURRENT = "currentPage";
    private static final String GRID_PAGE_SIZE = "pageSize";

    /**
     * 入参map中获取JsonParamDto信息。以便在service的table函数中使用。
     *
     * @param paraMap 入参信息
     * @return JsonParamDto信息
     */
    protected JsonParamDto getParaFromMap(Map paraMap) {
        JsonParamDto dto = new JsonParamDto();
        this.appendParaSortBy(paraMap, dto);
        this.appendParaPage(paraMap, dto);
        this.appendParaFilter(paraMap, dto);
        return dto;
    }

    /**
     * 将ParameterMap中的变量填充到DTO中。排序信息部分。
     *
     * @param paraMap 请求参数
     * @param dto     DTO
     * @return 填充了信息的DTO
     */
    private JsonParamDto appendParaSortBy(Map paraMap, JsonParamDto dto) {
        if (!CollectionUtils.isEmpty(paraMap)
                && paraMap.get(GRID_SORT_BY) != null
                && StringUtils.isNotBlank(paraMap.get(GRID_SORT_BY).toString())) {
            dto.setTsortby(paraMap.get(GRID_SORT_BY).toString());
        }
        return dto;
    }

    /**
     * 将ParameterMap中的变量填充到DTO中。分页信息部分。
     *
     * @param paraMap 请求参数
     * @param dto     DTO
     * @return 填充了信息的DTO
     */
    private JsonParamDto appendParaPage(Map paraMap, JsonParamDto dto) {
        if (!CollectionUtils.isEmpty(paraMap)) {
            if (paraMap.get(GRID_PAGE_CURRENT) != null
                    && StringUtils.isNotBlank(paraMap.get(GRID_PAGE_CURRENT).toString())) {
                try {
                    dto.setCurrentPage(Long.parseLong(paraMap.get(GRID_PAGE_CURRENT).toString()));
                } catch (Exception ex) {
                    // 设置默认值
                    dto.setCurrentPage(1);
                    log.warn("从入参中获取currentPage参数失败！", ex);
                }
            }
            if (paraMap.get(GRID_PAGE_SIZE) != null
                    && StringUtils.isNotBlank(paraMap.get(GRID_PAGE_SIZE).toString())) {
                try {
                    dto.setPageSize(Long.parseLong(paraMap.get(GRID_PAGE_SIZE).toString()));
                } catch (Exception ex) {
                    // 设置默认值
                    dto.setPageSize(10);
                    log.warn("从入参中获取pageSize参数失败！", ex);
                }
            }
        }
        return dto;
    }

    /**
     * 将ParameterMap中的变量填充到DTO中。过滤部分。
     *
     * @param paraMap 请求参数
     * @param dto     DTO
     * @return 填充了信息的DTO
     */
    private JsonParamDto appendParaFilter(Map paraMap, JsonParamDto dto) {
        if (!CollectionUtils.isEmpty(paraMap) && paraMap.get(GRID_FILTER) != null) {
            try {
                Map<String, Object> map = (Map) paraMap.get(GRID_FILTER);
                HashMap<String, String> hashMap = new HashMap<>();
                map.forEach((key, value) -> hashMap.put(key, value.toString()));
                dto.setTheadFilter(hashMap);
            } catch (Exception ex) {
                // 设置默认值
                dto.setTheadFilter(new HashMap<>(0));
                log.warn("从入参中获取pageSize参数失败！", ex);
            }
        }
        return dto;
    }
}

package com.qm.tds.api.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.sf.jsqlparser.statement.select.SelectItem;

import java.util.HashMap;
import java.util.List;

/**
 * TDS项目入参基类。
 * <p>
 * 示例数据：
 * {
 * "theadFilter": {
 * "name|text": "姓名",
 * "createOn|range-date": "2020-05-11#2020-05-20"
 * },
 * "tsortby": "name|ascend,createOn|descend",
 * "twhere": "dstop > '2020-07-15' and dstop < '2020-07-16' and vsex in ['1','2'] and vrealName like ''a'a' or vpersoncode like '11' and ( vrealName like 'uu' or vstop in ['1'] )"
 * "tsummary": "count|vSpa,sum|nSalPrice",
 * "tgroupby": "vMasterNO,vSupplierSpa"
 * "currentPage": 1,
 * "pageSize": 20,
 * "vpersoncode": "aaa",
 * "vpersonname": "bbbb"
 * }
 */
//@ApiModel("入参基础信息")
@Data
@Schema(description = "请求参数基础类")
public class JsonParamDto {

    /**
     * 表头筛选条件
     */
    @Schema(description = "表头筛选条件")
    private HashMap<String, String> theadFilter;

    /**
     * 排序条件
     */
    @Schema(description = "排序条件")
    private String tsortby;

    /**
     * 查询条件
     */
    @Schema(description = "查询条件")
    private String twhere;

    /**
     * 汇总统计配置
     */
    @Schema(description = "汇总统计配置")
    private String tsummary;

    /**
     * 分组字段
     */
    @Schema(description = "分组字段")
    private String tgroupby;

    /**
     * 当前页码
     */
    @Schema(description = "当前页码")
    private long currentPage;

    /**
     * 每页记录数
     */
    @Schema(description = "每页记录数")
    private long pageSize;

    /**
     * 是否统计总记录数
     */
    @Schema(description = "是否统计总记录数")
    private boolean isSearchCount = true;

    /**
     * 附加聚合函数
     */
    @Schema(description = "附加聚合函数")
    private List<SelectItem> aggregateItems;
}

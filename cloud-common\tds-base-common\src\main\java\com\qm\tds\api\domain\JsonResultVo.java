package com.qm.tds.api.domain;

import com.qm.tds.api.exception.QmException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * TDS项目出参基类。
 * <p>
 * 示例数据：
 * {
 * status:'S/E',
 * code:'200/400/自定义码',
 * msg:'',
 * data:{},
 * datalist:[]
 * }
 */
@Schema(description = "响应结果基础类")
@Slf4j
@NoArgsConstructor
public class JsonResultVo<T> {
    /**
     * 成功结果值
     */
    @Schema(description = "成功状态标识")
    public static final String STATUS_OK = "S";
    /**
     * 失败结果值
     */
    @Schema(description = "失败状态标识")
    public static final String STATUS_ERR = "E";

    @Schema(description = "成功状态码")
    public static final int CODE_OK = 200;
    @Schema(description = "错误状态码")
    public static final int CODE_ERR = 500;
    @Schema(description = "静默错误码")
    public static final int CODE_ERR_NO_PROMPT = 1007;
    @Schema(description = "响应状态")
    private String status = STATUS_OK;

    @Getter
    @Setter
    @Schema(description = "响应码")
    private int code = CODE_OK;

    @Getter
    @Setter
    @Schema(description = "请求追踪标识")
    private String traceId;

    @Getter
    @Setter
    @Schema(description = "响应消息")
    private String msg;

    @Getter
    @Setter
    @Schema(description = "响应数据")
    private T data;

    @Getter
    @Setter
    @Schema(description = "响应数据列表")
    private List<T> dataList;

    public String getStatus() {
        if (CODE_OK == code) {
            return STATUS_OK;
        } else {
            return STATUS_ERR;
        }
    }

    /**
     * 当前结果是否为正常
     *
     * @return true结果正常；false结果失败
     */
    @Schema(description="当前结果是否为正常")
    public boolean isOk() {
        return CODE_OK == code;
    }

    /**
     * 设置失败异常信息。
     * 自动设置code值为CODE_ERR。
     *
     * @param msg 失败情况下的异常信息
     */
    public void setMsgErr(String msg) {
        this.msg = msg;
        code = CODE_ERR;
    }

    /**
     * 设置失败异常信息。
     * 自动设置code值为CODE_ERR，同时将ex信息拼接到msg尾部。
     *
     * @param msg 失败情况下的异常信息
     * @param ex  引起失败的异常对象
     */
    public void setMsgErr(String msg, Throwable ex) {
        if (ex == null) {
            setMsgErr(msg);
            log.warn(msg);
        } else if (ex instanceof QmException) {
            // 自定义内部异常
            setMsgErr(msg);
            if (ex.getCause() == null) {
                log.warn(msg, ex);
            } else {
                log.info("---error--"+msg, ex);
            }
        } else {
            setMsgErr(msg + "[" + ex.getClass().getName() + "]" + ex.getMessage());
            log.info("---error--"+msg, ex);
        }
    }

}

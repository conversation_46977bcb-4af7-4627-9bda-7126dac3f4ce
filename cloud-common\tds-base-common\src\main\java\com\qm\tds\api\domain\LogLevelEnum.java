package com.qm.tds.api.domain;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @description 日志输出级别枚举类
 * @date 2020/7/29 15:29
 */
@Schema(description = "日志级别枚举")
public enum LogLevelEnum {
    @Schema(description = "信息")
    INFO(1),
    @Schema(description = "调试")
    DEBUG(2),
    @Schema(description = "警告")
    WARING(3),
    @Schema(description = "错误")
    ERROR(4);

    @Schema(description = "级别编码")
    private Integer code;

    LogLevelEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

}

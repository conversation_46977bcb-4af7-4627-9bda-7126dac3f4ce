package com.qm.tds.api.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * 通用异常类
 */
public class QmException extends RuntimeException {

    private static final long serialVersionUID = 7810584227602028484L;

    @Getter
    @Setter
    private Object data;

    /**
     * 自定义异常信息
     */
    public QmException() {
        super();
    }

    /**
     * 自定义异常信息
     *
     * @param message 异常消息内容
     */
    public QmException(String message) {
        super(message);
    }

    /**
     * 自定义异常信息
     *
     * @param message 异常消息内容
     * @param cause   引起消息的内部异常
     */
    public QmException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 自定义异常信息
     *
     * @param cause 引起消息的内部异常
     */
    public QmException(Throwable cause) {
        super(cause);
    }

    /**
     * 自定义异常信息
     *
     * @param nCode     异常信息错误编码
     * @param vParaList 异常参数
     */
    public QmException(long nCode, String... vParaList) {
        super("自定义异常信息[" + nCode + "]");
        //TODO 日后扩展自定义消息编码
    }

    public static QmException error(String message) {
        return new QmException(message);
    }

    public static QmException error(String message, Throwable cause) {
        return new QmException(message, cause);
    }
}

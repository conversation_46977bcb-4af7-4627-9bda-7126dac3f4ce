package com.qm.tds.api.hystrix;

/**
 * 熔断的公用参数和常量等。
 * by wjq on 20201111
 */
public class QmHystrix {
    private QmHystrix() {
    }

    /**
     * 清除附带的parameter和header参数。
     * 需要在fegin接口中增加一个 @RequestParam(name = QmHystrix.KEY_CLEAR_TOKEN) boolean clearTokean 参数。
     * <p>
     * 使用场景：
     * 1）调用三方接口
     * 2）不需要附加当前请求中的Parameter与header参数
     */
    public static final String KEY_CLEAR_TOKEN = "qm-interface-clear-token";
}

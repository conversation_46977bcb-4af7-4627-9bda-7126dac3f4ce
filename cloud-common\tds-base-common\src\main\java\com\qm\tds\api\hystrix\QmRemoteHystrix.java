package com.qm.tds.api.hystrix;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.util.SpringContextHolder;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;

@Slf4j
@Data
@NoArgsConstructor
public abstract class QmRemoteHystrix<T> {
    /**
     * 异常信息对象
     */
    private Throwable hystrixEx;

    private static I18nUtil i18nUtil;

    private static I18nUtil getI18nUtil() {
        if (i18nUtil == null) {
            i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
        }
        return i18nUtil;
    }

    public QmRemoteHystrix(Throwable e) {
        log.info("---error--"+"远程调用异常！", e);
        hystrixEx = e;
    }

    /**
     * 获取标准异常返回信息
     *
     * @param <T> 数据接口
     * @return 异常返回信息
     */
    public <T> JsonResultVo<T> getResult() {
        JsonResultVo<T> resultObj = new JsonResultVo<>();
        String message = getI18nUtil().getMessage("ERR.basecommon.QmRemoteHystrix.invokeException");
        if (getHystrixEx() == null) {
            resultObj.setMsgErr(message);
        } else {
            resultObj.setMsgErr(message, getHystrixEx());
        }
        return resultObj;
    }

    /**
     * 返回对应的Fegin接口
     *
     * @return Fegin接口
     */
    protected T getFeginRemote() {
        String message = getI18nUtil().getMessage("ERR.basecommon.QmRemoteHystrix.undoneMethod");
        throw new NotImplementedException(message);
    }
}

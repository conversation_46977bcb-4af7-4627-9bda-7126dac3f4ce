package com.qm.tds.api.mp.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 自定义Mybatis Plus的Mapper基类
 *
 * @param <T> 对应数据实体
 */
public interface QmBaseMapper<T> extends BaseMapper<T> {

    T selectByIdNew(@Param("id") Serializable id, @Param("ew") Wrapper<T> queryWrapper);

    List<T> selectBatchIdsNew(@Param("coll") Collection<? extends Serializable> idList, @Param("ew") Wrapper<T> queryWrapper);

    List<T> selectByMapNew(@Param("cm") Map<String, Object> columnMap, @Param("ew") Wrapper<T> queryWrapper);
}

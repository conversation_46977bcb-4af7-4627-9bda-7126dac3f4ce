package com.qm.tds.api.mp.pagination;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.common.uiep.mp.pagination.UiepPage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 自定义分页信息
 *
 * @param <T> 查询的实体类
 */
@Schema(description = "自定义分页信息")
@Data
@NoArgsConstructor
public class QmPage<T> implements Serializable {

    /**
     * 数据
     */
    @Schema(description="数据")
    private List<T> items;
    /**
     * 附加其他数据，例如合计行等。
     * add by wjq on 20210415
     */
    @Schema(description="附加其他数据，例如合计行等")
    private Object extension;
    /**
     * 数据总数
     */
    @Schema(description="数据总数")
    private long total;
    /**
     * 当前页码
     */
    @Schema(description= "当前页码")
    private long currentPage;
    /**
     * 每页的记录数
     */
    @Schema(description=  "每页的记录数")
    private long pageSize;
    /**
     * 页码总数
     */
    @Schema(description="页码总数")
    private long totalPages;

    /**
     * 聚合函数结果
     */
    @Schema(description="聚合函数结果")
    private Map aggregateResult;

    /**
     * 将MybatisPlus标准的分页信息转换为前台表格组件能识别的分页信息
     *
     * @param pageInfo MybatisPlus标准的分页信息
     * @return 分页信息对象
     */
    public static <T> QmPage<T> convertFromMpPage(IPage<T> pageInfo) {
        QmPage<T> pageInfoQm = new QmPage<>();
        pageInfoQm.setPageInfo(pageInfo);
        return pageInfoQm;
    }

    /**
     * 设置分页信息
     *
     * @param pageInfo 分页信息
     * @return 分页信息对象
     */
    public QmPage<T> setPageInfo(IPage<T> pageInfo) {
        setCurrentPage(pageInfo.getCurrent());
        setTotalPages(pageInfo.getPages());
        setItems(pageInfo.getRecords());
        setPageSize(pageInfo.getSize());
        setTotal(pageInfo.getTotal());
        if (pageInfo instanceof UiepPage) {
            setAggregateResult(((UiepPage) pageInfo).getAggregateResult());
        }
        return this;
    }

    /**
     * 设置ES分页信息
     *
     * @param pageInfo 分页信息
     * @return 分页信息对象
     */
    public QmPage<T> setEsPageInfo(Page<T> pageInfo) {
        setCurrentPage(pageInfo.getNumber());
        setTotalPages(pageInfo.getTotalPages());
        setItems(pageInfo.getContent());
        setPageSize(pageInfo.getSize());
        setTotal(pageInfo.getTotalElements());
        return this;
    }
}

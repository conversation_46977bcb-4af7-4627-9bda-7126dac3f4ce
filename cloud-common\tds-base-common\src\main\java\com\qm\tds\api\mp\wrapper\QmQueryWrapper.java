package com.qm.tds.api.mp.wrapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import lombok.Data;

/**
 * QM的自动通用查询
 *
 * @param <T>
 */
@Data
public class QmQueryWrapper<T> extends QueryWrapper<T> {

    /**
     * 公司ID
     */
    private String ncompanyid;
    /**
     * 语种代码
     */
    private String vlanguagecode;
    /**
     * 客户组ID
     */
    private String ncustgroupid;
    /**
     * 操作员ID
     */
    private String noperatorid;

    public QmQueryWrapper() {
        LoginKeyDO loginKey = BootAppUtil.getLoginKey();
        this.ncompanyid = loginKey.getCompanyId();
        this.vlanguagecode = loginKey.getLanguageCode();
        this.ncustgroupid = loginKey.getCustGroupid();
        this.noperatorid = loginKey.getOperatorId();
    }

}

package com.qm.tds.api.receiver;

import com.qm.tds.api.constant.GlobalConstants;
import com.qm.tds.api.listener.EpRedisListerer;
import com.qm.tds.api.util.SpringContextHolder;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Data
public class RedisReceiver {


    /**
     * 接受消息并调用业务逻辑处理器
     *
     * @param params
     */
    public void onMessage(Map params) {
        Object handlerName = params.get(GlobalConstants.HANDLER_NAME);
        EpRedisListerer messageListener = SpringContextHolder.getHandler(handlerName.toString(), EpRedisListerer.class);
        if (null != messageListener) {
            messageListener.onMessage(params);
        }
    }
}

package com.qm.tds.api.ser;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.introspect.AnnotationMap;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.qm.tds.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;
import java.util.Date;

/**
 * @description: 时间序列化
 * @author: Cyl
 * @time: 2020/6/18 13:34
 */
public class QmDateSerialize extends JsonSerializer<Date> implements ContextualSerializer {

    @Value("${spring.jackson.time-zone}")
    private String timeZone;

    @Value("${spring.jackson.date-format}")
    private String dateFormat;


    private String pattern = DateUtils.DATE_TIME_PATTERN;
    private String defultTimeZone = "GMT+8";

    @Override
    public void serialize(Date date, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (null != date) {
            if (date.compareTo(DateUtils.getMinDate()) <= 0) {
                jsonGenerator.writeObject(null);
            } else {
                jsonGenerator.writeObject(DateUtils.format(date, pattern, defultTimeZone));
            }
        } else {
            jsonGenerator.writeObject(date);
        }
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) throws JsonMappingException {
        AnnotationMap annotated = property.getMember().getAllAnnotations();
        JsonFormat jsonFormat = annotated.get(JsonFormat.class);
        if (jsonFormat != null && jsonFormat.pattern() != null) {
            pattern = jsonFormat.pattern();
        } else {
            if (StringUtils.isNotBlank(dateFormat)) {
                pattern = dateFormat;
            }
            if (StringUtils.isNotBlank(timeZone)) {
                defultTimeZone = timeZone;
            }
        }
        return this;
    }

}

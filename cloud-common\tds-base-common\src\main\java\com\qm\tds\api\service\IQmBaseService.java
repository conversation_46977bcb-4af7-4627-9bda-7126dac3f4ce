package com.qm.tds.api.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 自定义Mybatis Plus的Service基类
 *
 * @param <T>
 */
public interface IQmBaseService<T> extends IService<T> {

    /**
     * Service对应主Mapper基类
     *
     * @return 主Mapper基类
     */
    QmBaseMapper<T> getQmBaseMapper();

    /**
     * 自定义查询功能。
     *
     * @param queryWrapper 查询条件
     * @param tableParam   ui组件的过滤、排序功能、分页信息
     * @return 查询结果数据
     */
    QmPage<T> table(QueryWrapper<T> queryWrapper, JsonParamDto tableParam);

    /**
     * @description 编辑保存时调用更改日志
     * <AUTHOR>
     * @date 2020/7/9 14:06
     */
    boolean saveOrUpdateWithLog(T entity);

    /**
     * @description 删除时调用更改日志
     * <AUTHOR>
     * @date 2020/12/ 16:27
     */
    boolean deleteWithLog(T entity);

    boolean updateByIdWithLog(T entity);

    @Override
    boolean saveOrUpdate(T entity);

    @Transactional(rollbackFor = {Exception.class})
    @Override
    default boolean saveOrUpdateBatch(Collection<T> entityList) {
        return saveOrUpdateBatch(entityList, 1000);
    }

    @Override
    boolean saveOrUpdateBatch(Collection<T> entityList, int batchSize);

    @Override
    boolean updateById(T entity);

    @Override
    boolean update(T entity, Wrapper<T> updateWrapper);

    @Override
    default boolean update(Wrapper<T> updateWrapper) {
        return update((T) null, updateWrapper);
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    default boolean updateBatchById(Collection<T> entityList) {
        return updateBatchById(entityList, 1000);
    }

    @Override
    boolean updateBatchById(Collection<T> entityList, int batchSize);

    @Override
    default boolean saveOrUpdate(T entity, Wrapper<T> updateWrapper) {
        return update(entity, updateWrapper) || saveOrUpdate(entity);
    }

    @Override
    default List<T> list() {
        return list(new QmQueryWrapper<>());
    }


    /**
     * @description 保存并更新
     * <AUTHOR>
     * @date 2020/7/9 14:06
     */
    T saveOrUpdateMulti(T entity);

    /**
     * 调用更新日志接口
     * @param entity
     * @param oldEntity
     * @return
     */
    boolean saveChangeLog(T entity,T oldEntity);
}

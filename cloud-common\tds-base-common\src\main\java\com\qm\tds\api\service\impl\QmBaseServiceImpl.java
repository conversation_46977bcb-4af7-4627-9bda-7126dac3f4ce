package com.qm.tds.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.*;
import com.qm.common.uiep.mp.pagination.UiepPage;
import com.qm.common.uiep.mp.service.impl.UiepBaseServiceImpl;
import com.qm.common.uiep.table.domain.TableInfo;
import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.IQmBaseService;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.base.domain.bean.UpdateLogDO;
import com.qm.tds.base.service.UpdateLogService;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.ReflectUtil;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自定义Mybatis Plus的ServiceImpl基类
 *
 * @param <M> 对应数据层Mapper
 * @param <T> 对应业务实体
 */
public class QmBaseServiceImpl<M extends QmBaseMapper<T>, T> extends UiepBaseServiceImpl<M, T> implements IQmBaseService<T> {

    private static final String ID = "id";
    @Autowired
    UpdateLogService updateLogService;
    @Autowired
    private I18nUtil i18nUtil;

    @Override
    public M getQmBaseMapper() {
        return getBaseMapper();
    }

    @Override
    public T getById(Serializable id) {
        QmQueryWrapper<T> queryWrapper = new QmQueryWrapper<>();
        return getBaseMapper().selectByIdNew(id, queryWrapper);
    }

    private T getByIdNoCache(Serializable id) {
        QmQueryWrapper<T> queryWrapper = new QmQueryWrapper<>();
        queryWrapper.apply("{0} = {0}", UUID.randomUUID().toString());
        queryWrapper.eq(ID, id);
        return getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public List<T> listByIds(Collection<? extends Serializable> idList) {
        QmQueryWrapper<T> queryWrapper = new QmQueryWrapper<>();
        return getBaseMapper().selectBatchIdsNew(idList, queryWrapper);
    }

    @Override
    public List<T> listByMap(Map<String, Object> columnMap) {
        QmQueryWrapper<T> queryWrapper = new QmQueryWrapper<>();
        return baseMapper.selectByMapNew(columnMap, queryWrapper);
    }

    @Override
    public QmPage<T> table(QueryWrapper<T> queryWrapper, JsonParamDto tableParam) {
        TableInfo tableInfo = new TableInfo();

        if (tableParam != null) {
            tableInfo.setPageIndex(tableParam.getCurrentPage());
            tableInfo.setPageSize(tableParam.getPageSize());
            tableInfo.setSort(tableParam.getTsortby());
            tableInfo.setFilter(tableParam.getTheadFilter());
            tableInfo.setWhere(tableParam.getTwhere());
            tableInfo.setSummary(tableParam.getTsummary());
            tableInfo.setGroup(tableParam.getTgroupby());
            tableInfo.setSearchCount(tableParam.isSearchCount());
            tableInfo.setAggregateItems(tableParam.getAggregateItems());
        }

        //查询
        UiepPage<T> pageData = table(queryWrapper, tableInfo);

        //查询结果转换
        QmPage<T> retData = new QmPage<>();
        retData.setPageInfo(pageData);
        return retData;
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public boolean saveOrUpdateWithLog(T entity) {
        if (null == entity) {
            return false;
        }
        boolean saveFlag = false;
        Class<?> cls = entity.getClass();
        com.baomidou.mybatisplus.core.metadata.TableInfo tableInfo = TableInfoHelper.getTableInfo(cls);
        Assert.notNull(tableInfo, "error1: can not execute. because can not find cache of TableInfo for entity!");
        String keyProperty = tableInfo.getKeyProperty();
        Assert.notEmpty(keyProperty, "error2: can not execute. because can not find column for id from entity!");
        Object idVal = ReflectionKit.getFieldValue(entity, tableInfo.getKeyProperty());
        if (!StringUtils.checkValNotNull(idVal)) {
            return this.save(entity);
        }
        // 查询修改前数据
        T oldEntity = this.getByIdNoCache((Serializable) idVal);
        // 判断数据是否为空，为空则需要调保存方法
        if (Objects.isNull(oldEntity)) {
            return this.save(entity);
        }
        /*
         * 原有逻辑：
         * 处理新旧数据间的差别，并组装修改日志数据
         * List<UpdateLogDO> logs = handleCompareData(oldEntity, entity, tableInfo, idVal.toString()); //NOSONAR
         */
        saveFlag = this.updateById(entity);

        /*
         * 处理新旧数据间的差别，并组装修改日志数据。
         * 因为业务上要查看所有字段的对比，所以不能用入参的实体作为最新的数据源，必须用更新后的数据作为对比数据。
         * TODO 这里性能低下，日后需要优化。
         */
        T newEntity = this.getByIdNoCache((Serializable) idVal);
        List<UpdateLogDO> logs = handleCompareData2(oldEntity, newEntity, tableInfo, idVal.toString());
        // 修改日志不为空，保存修改日志
        if (saveFlag && !logs.isEmpty()) {
            updateLogService.saveUpdateLogBatch(logs);
        }
        return saveFlag;
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public boolean deleteWithLog(T entity) {
        boolean deleteFlag = false;
        if (null == entity) {
            return false;
        }
        Class<?> cls = entity.getClass();
        com.baomidou.mybatisplus.core.metadata.TableInfo tableInfo = TableInfoHelper.getTableInfo(cls);
        Assert.notNull(tableInfo, "error1: can not execute. because can not find cache of TableInfo for entity!");
        String keyProperty = tableInfo.getKeyProperty();
        Assert.notEmpty(keyProperty, "error2: can not execute. because can not find column for id from entity!");
        Object idVal = ReflectionKit.getFieldValue(entity, tableInfo.getKeyProperty());
        if (StringUtils.checkValNotNull(idVal)) {
            // 查询修改前数据
            T oldEntity = getByIdNoCache((Serializable) idVal);
            // 判断数据是否为空，为空则需要抛异常
            if (!Objects.isNull(oldEntity)) {
                int num = baseMapper.deleteById(idVal.toString());
                deleteFlag = num > 0;
                // 获取日志
                List<UpdateLogDO> logs = handleCompareData2(oldEntity, null, tableInfo, idVal.toString());
                // 修改日志不为空，保存修改日志
                if (deleteFlag && CollectionUtils.isNotEmpty(logs)) {
                    updateLogService.saveUpdateLogBatch(logs);
                }
            } else {
                String message = i18nUtil.getMessage("ERR.basecommon.QmBaseServiceImpl.deleteDataNonexist");
                throw new QmException(message);
            }
        }
        return deleteFlag;
    }

    /**
     * @description 处理新旧数据间的差别，并组装修改日志数据
     * <AUTHOR>
     * @date 2020/7/9 14:25
     *
     * private List<UpdateLogDO> handleCompareData(T oldEntity, T entity, com.baomidou.mybatisplus.core.metadata.TableInfo tableInfo, String dataId) {
     *     List<UpdateLogDO> logs = new ArrayList<>();
     *     // 当前登录用户，获取操作人
     *     LoginKeyDO loginKey = BootAppUtil.getLoginKey();
     *     String operId = loginKey.getOperatorId();
     *     // 操作时间
     *     Date operDate = DateUtils.getSysdateTime();
     *     // 数据库表名称
     *     String tableName = tableInfo.getTableName().toUpperCase();
     *     // 获取类属性
     *     List<TableFieldInfo> filedList = tableInfo.getFieldList();
     *     if (!filedList.isEmpty()) {
     *         for (TableFieldInfo filed : filedList) {
     *             Class<?> newCls = entity.getClass();
     *             if ("serialVersionUID".equals(filed.getProperty())) {
     *                 continue;
     *             }
     *             // 原有的值
     *             Object oldValue = ReflectionKit.getMethodValue(oldEntity.getClass(), oldEntity, filed.getProperty());
     *             // 本次要编辑的值
     *             Object newValue = ReflectionKit.getMethodValue(newCls, entity, filed.getProperty());
     *             // 获取字段的中文注释
     *             String label = null;
     *             String fill = "";
     *             try {
     *                 Field f = newCls.getDeclaredField(filed.getProperty());
     *                 // 判断不等于空
     *                 if (!BootAppUtil.isNullOrEmpty(f.getDeclaredAnnotation(ApiModelProperty.class))) {
     *                     label = f.getDeclaredAnnotation(ApiModelProperty.class).value();
     *                 }
     *                 // 获取字段填充策略注解,如果为UPDATE则需要存储记录
     *                 fill = f.getDeclaredAnnotation(TableField.class).fill().toString();
     *             } catch (NoSuchFieldException e) {
     *                 throw ExceptionUtils.mpe("Error: NoSuchField in %s.  Cause:", e, filed.getProperty());
     *             }
     *             **
     *              * 调整!BootAppUtil.isNullOrEmpty(newValue) && !BootAppUtil.isNullOrEmpty(oldValue) || "UPDATE".equals(fill) || "INSERT_UPDATE".equals(fill)判断逻辑。
     *              * 调整逻辑为只判断是否为null，不需要判断空字符串、0等其他初始值。
     *              *
     *             if (!BootAppUtil.isNullOrEmpty(label) && newValue != null && oldValue != null) {
     *                 if (!oldValue.toString().equals(newValue.toString())) {
     *                     UpdateLogDO update = new UpdateLogDO();
     *                     update.setVtablename(tableName);
     *                     update.setVcolname(filed.getProperty().toUpperCase());
     *                     update.setVcoltext(label);
     *                     update.setNdataid(dataId);
     *                     // 日期类型转换yyyy-mm-dd hh:mm:ss
     *                     if ("java.sql.Timestamp".equals(filed.getPropertyType().getName())) {
     *                         update.setVoldvalue(DateUtils.format((Date) oldValue, DateUtils.TIMESTAMP_PATTERN));
     *                         update.setVnewvalue(DateUtils.format((Date) newValue, DateUtils.TIMESTAMP_PATTERN));
     *                     } else if ("java.util.Date".equals(filed.getPropertyType().getName())) {
     *                         update.setVoldvalue(DateUtils.format((Date) oldValue, DateUtils.DATE_TIME_PATTERN));
     *                         update.setVnewvalue(DateUtils.format((Date) newValue, DateUtils.DATE_TIME_PATTERN));
     *                     } else {
     *                         update.setVoldvalue(oldValue.toString());
     *                         update.setVnewvalue(newValue.toString());
     *                     }
     *                     update.setNopr(operId);
     *                     update.setDopr(operDate);
     *                     logs.add(update);
     *                 }
     *             }
     *         }
     *     }
     *     return logs;
     * }
     */

    /**
     * @description 处理新旧数据间的差别，并组装修改日志数据
     * <AUTHOR>
     * @date 2020/7/9 14:25
     */
    private List<UpdateLogDO> handleCompareData2(T oldEntity, T entity, com.baomidou.mybatisplus.core.metadata.TableInfo tableInfo, String dataId) {
        List<UpdateLogDO> logs = new ArrayList<>();
        // 当前登录用户，获取操作人
        LoginKeyDO loginKey = BootAppUtil.getLoginKey();
        String operId = loginKey.getOperatorId();
        // 操作时间
        Date operDate = DateUtils.getSysdateTime();
        // 数据库表名称
        String tableName = tableInfo.getTableName().toUpperCase();
        // 获取类属性
        Field[] fields = oldEntity.getClass().getDeclaredFields();
        if (ArrayUtils.isNotEmpty(fields)) {
            logs = Arrays.stream(fields)
                    .filter(field -> !"serialVersionUID".equals(field.getName()))
                    .map(field -> {
                        // 如果entity为空，则代表是删除，不需要对比，直接返回所有旧的值的集合
                        UpdateLogDO updateLogDO;
                        if (BootAppUtil.isNullOrEmpty(entity)) {
                            updateLogDO = createUpdateLogDO(oldEntity, field, tableName, dataId, operId, operDate);
                        } else {
                            updateLogDO = createUpdateLogDO(oldEntity, entity, field, tableName, dataId, operId, operDate);
                        }
                        return updateLogDO;
                    })
                    .filter(Objects::nonNull).collect(Collectors.toList());
        }
        return logs;
    }

    /**
     * 获取实体的字段字符串值，如果为null，则返回空
     *
     * @param entityClass 对象类
     * @param entity      对象实体
     * @param filed       字段属性
     * @return 字段值的字符串
     */
    private String getMethodValueStr(Class<?> entityClass, T entity, Field filed) {
        String valueStr;
        Object valueObj = ReflectionKit.getFieldValue(entity, filed.getName());
        if (valueObj == null) {
            valueStr = "";
        } else if (filed.getType().isAssignableFrom(java.sql.Timestamp.class)) {
            valueStr = DateUtils.format((Date) valueObj, DateUtils.TIMESTAMP_PATTERN);
        } else if (filed.getType().isAssignableFrom(Date.class)) {
            valueStr = DateUtils.format((Date) valueObj, DateUtils.DATE_TIME_PATTERN);
        } else {
            valueStr = valueObj.toString();
        }
        return valueStr;
    }

    private UpdateLogDO createUpdateLogDO(T oldEntity, T entity, Field filed, String tableName, String dataId, String operId, Date operDate) {
        UpdateLogDO update = null;
        Class<?> newCls = entity.getClass();
        // 原有的值
        String oldValue = getMethodValueStr(oldEntity.getClass(), oldEntity, filed);
        // 本次要编辑的值
        String newValue = getMethodValueStr(newCls, entity, filed);
        // 获取字段的中文注释
        String label = ReflectUtil.getFieldLabel(newCls, filed);
        if (!BootAppUtil.isNullOrEmpty(label) && !oldValue.equals(newValue)) {
            update = new UpdateLogDO();
            update.setVtablename(tableName);
            update.setVcolname(filed.getName().toUpperCase());
            update.setVcoltext(label);
            update.setNdataid(dataId);
            update.setVoldvalue(oldValue);
            update.setVnewvalue(newValue);
            update.setNopr(operId);
            update.setDopr(operDate);
        }
        return update;
    }

    private UpdateLogDO createUpdateLogDO(T oldEntity, Field filed, String tableName, String dataId, String operId, Date operDate) {
        UpdateLogDO update = null;
        Class<?> newCls = oldEntity.getClass();
        // 原有的值
        String oldValue = getMethodValueStr(oldEntity.getClass(), oldEntity, filed);
        // 获取字段的中文注释
        String label = ReflectUtil.getFieldLabel(newCls, filed);
        if (!BootAppUtil.isNullOrEmpty(label) && oldValue != null) {
            update = new UpdateLogDO();
            update.setVtablename(tableName);
            update.setVcolname(filed.getName().toUpperCase());
            update.setVcoltext(label);
            update.setNdataid(dataId);
            update.setVoldvalue(oldValue);
            update.setVnewvalue(null);
            update.setNopr(operId);
            update.setDopr(operDate);
        }
        return update;
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public boolean saveOrUpdate(T entity) {
        if (null == entity) {
            return false;
        } else {
            Class<?> cls = entity.getClass();
            com.baomidou.mybatisplus.core.metadata.TableInfo tableInfo = TableInfoHelper.getTableInfo(cls);
            Assert.notNull(tableInfo, "error: can not execute. because can not find cache of TableInfo for entity!");
            String keyProperty = tableInfo.getKeyProperty();
            Assert.notEmpty(keyProperty, "error: can not execute. because can not find column for id from entity!");
            Object idVal = ReflectionKit.getFieldValue( entity, tableInfo.getKeyProperty());
            return StringUtils.checkValNotNull(idVal)
                    && ObjectUtils.isNotEmpty(this.getById((Serializable) idVal))
                    ? this.updateById(entity) : this.save(entity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveOrUpdateBatch(Collection<T> entityList, int batchSize) {
        /*
         * 重写基类saveOrUpdateBatch函数，增加EP特有的时间戳判断
         */
        Assert.notEmpty(entityList, "error: entityList must not be empty");
        Class<?> cls = currentModelClass();
        com.baomidou.mybatisplus.core.metadata.TableInfo tableInfo = TableInfoHelper.getTableInfo(cls);
        Assert.notNull(tableInfo, "error: can not execute. because can not find cache of TableInfo for entity!");
        String keyProperty = tableInfo.getKeyProperty();
        Assert.notEmpty(keyProperty, "error: can not execute. because can not find column for id from entity!");
        try (SqlSession batchSqlSession = sqlSessionBatch()) {
            int i = 0;
            for (T entity : entityList) {
                Object idVal = ReflectionKit.getFieldValue(entity, keyProperty);
                if (StringUtils.checkValNull(idVal) || Objects.isNull(getById((Serializable) idVal))) {
                    batchSqlSession.insert(sqlStatement(SqlMethod.INSERT_ONE), entity);
                } else {
                    // 增加EP项目的特有判断 by wjq
                    if (ReflectUtil.judgeTimestamp(entity)) {
                        MapperMethod.ParamMap<T> param = new MapperMethod.ParamMap<>();
                        param.put(Constants.ENTITY, entity);
                        batchSqlSession.update(sqlStatement(SqlMethod.UPDATE_BY_ID), param);
                    } else {
                        return false;
                    }
                }
                // 不知道以后会不会有人说更新失败了还要执行插入 😂😂😂
                if (i >= 1 && i % batchSize == 0) {
                    validTimestamp(batchSqlSession.flushStatements());
                }
                i++;
            }
            validTimestamp(batchSqlSession.flushStatements());
        }
        return true;
    }

    @Override
    public boolean updateById(T entity) {
        boolean resultFlag = ReflectUtil.judgeTimestamp(entity) && retBool(baseMapper.updateById(entity));
        if (!resultFlag) {
            String message = i18nUtil.getMessage("ERR.basecommon.QmBaseServiceImpl.infoChangedReflush");
            throw new QmException(message);
        }
        return true;
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public boolean updateByIdWithLog(T entity) {
        if (null == entity) {
            return false;
        }
        boolean updateFlag = false;
        Class<?> cls = entity.getClass();
        com.baomidou.mybatisplus.core.metadata.TableInfo tableInfo = TableInfoHelper.getTableInfo(cls);
        Assert.notNull(tableInfo, "error1: can not execute. because can not find cache of TableInfo for entity!");
        String keyProperty = tableInfo.getKeyProperty();
        Assert.notEmpty(keyProperty, "error2: can not execute. because can not find column for id from entity!");
        Object idVal = ReflectionKit.getFieldValue( entity, tableInfo.getKeyProperty());
        if (StringUtils.checkValNotNull(idVal)) {
            // 查询修改前数据
            T oldEntity = getByIdNoCache((Serializable) idVal);
            // 判断数据是否为空，为空则需要抛异常
            if (!Objects.isNull(oldEntity)) {
                int num = baseMapper.updateById(entity);
                updateFlag = num > 0;
                if (!updateFlag) {
                    String message = i18nUtil.getMessage("ERR.basecommon.QmBaseServiceImpl.infoChangedReflush");
                    throw new QmException(message);
                }
                T newEntity = this.getById((Serializable) idVal);
                // 获取日志
                List<UpdateLogDO> logs = handleCompareData2(oldEntity, newEntity, tableInfo, idVal.toString());
                // 修改日志不为空，保存修改日志
                if (updateFlag && CollectionUtils.isNotEmpty(logs)) {
                    updateLogService.saveUpdateLogBatch(logs);
                }
            } else {
                String message = i18nUtil.getMessage("ERR.basecommon.QmBaseServiceImpl.infoChangedReflush");
                throw new QmException(message);
            }
        }
        return updateFlag;
    }

    @Override
    public boolean update(T entity, Wrapper<T> updateWrapper) {
        boolean resultFlag = ReflectUtil.judgeTimestamp(entity) && retBool(baseMapper.update(entity, updateWrapper));
        if (resultFlag) {
            String message = i18nUtil.getMessage("ERR.basecommon.QmBaseServiceImpl.infoChangedReflush");
            throw new QmException(message);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateBatchById(Collection<T> entityList, int batchSize) {
        /*
         * 重写基类saveOrUpdateBatch函数，增加EP特有的时间戳判断
         */
        Assert.notEmpty(entityList, "error: entityList must not be empty");
        String sqlStatement = sqlStatement(SqlMethod.UPDATE_BY_ID);
        try (SqlSession batchSqlSession = sqlSessionBatch()) {
            int i = 0;
            for (T anEntityList : entityList) {
                // 增加EP项目的特有判断 by wjq
                if (ReflectUtil.judgeTimestamp(anEntityList)) {
                    MapperMethod.ParamMap<T> param = new MapperMethod.ParamMap<>();
                    param.put(Constants.ENTITY, anEntityList);
                    batchSqlSession.update(sqlStatement, param);
                    if (i >= 1 && i % batchSize == 0) {
                        validTimestamp(batchSqlSession.flushStatements());
                    }
                    i++;
                } else {
                    return false;
                }
            }
            validTimestamp(batchSqlSession.flushStatements());
        }
        return true;
    }

    private void validTimestamp(List<BatchResult> batchResults) {
        for (BatchResult result : batchResults) {
            int[] counts = result.getUpdateCounts();
            List<Integer> countsList = Arrays.stream(counts).boxed().collect(Collectors.toList());
            boolean flag = countsList.stream().allMatch(element -> element == 0);
            if (flag) {
                String message = i18nUtil.getMessage("ERR.basecommon.QmBaseServiceImpl.infoChangedReflush");
                if (result.getParameterObjects().isEmpty()) {
                    throw new QmException(message);
                }
                Object paramObject = result.getParameterObjects().get(0);
                if (paramObject instanceof Map) {
                    boolean flagId = ReflectUtil.containsKey(((Map<?, ?>) paramObject).get("et"), ID);
                    if (!flagId) {
                        throw new QmException(message);
                    }
                    Object valId = ReflectUtil.getValueByName(((Map<?, ?>) paramObject).get("et"), ID);
                    if (!StringUtils.checkValNull(valId) && !Objects.isNull(valId)) {
                        throw new QmException(ID + ":" + valId + message);
                    } else {
                        throw new QmException(message);
                    }
                } else {
                    throw new QmException(message);
                }
            }
        }
    }

    /**
     * saveOrUpdate
     * 更新系统日志信息
     * 返回查询修改前数据，用于对比更新
     * @param entity
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    @Override
    public T saveOrUpdateMulti(T entity) {
        if (null == entity) {
            //log.info("---error--"+"请求参数为空！");
        }
        boolean saveFlag = false;
        Class<?> cls = entity.getClass();
        com.baomidou.mybatisplus.core.metadata.TableInfo tableInfo = TableInfoHelper.getTableInfo(cls);
        Assert.notNull(tableInfo, "error1: can not execute. because can not find cache of TableInfo for entity!");
        String keyProperty = tableInfo.getKeyProperty();
        Assert.notEmpty(keyProperty, "error2: can not execute. because can not find column for id from entity!");
        Object idVal = ReflectionKit.getFieldValue(entity, tableInfo.getKeyProperty());
        if (!StringUtils.checkValNotNull(idVal)) {
            this.save(entity);
        }
        // 查询修改前数据
        T oldEntity = this.getByIdNoCache((Serializable) idVal);
        // 判断数据是否为空，为空则需要调保存方法
        if (Objects.isNull(oldEntity)) {
            this.save(entity);
        }
        /*
         * 原有逻辑：
         * 处理新旧数据间的差别，并组装修改日志数据
         * List<UpdateLogDO> logs = handleCompareData(oldEntity, entity, tableInfo, idVal.toString()); //NOSONAR
         */
        saveFlag = this.updateById(entity);

        /*
         * 处理新旧数据间的差别，并组装修改日志数据。
         * 因为业务上要查看所有字段的对比，所以不能用入参的实体作为最新的数据源，必须用更新后的数据作为对比数据。
         * TODO 这里性能低下，日后需要优化。
         */
        //T newEntity = this.getByIdNoCache((Serializable) idVal);
        //List<UpdateLogDO> logs = handleCompareData2(oldEntity, newEntity, tableInfo, idVal.toString());
        // 修改日志不为空，保存修改日志

        return oldEntity;
    }


    @Transactional(rollbackFor = {Exception.class})
    @Override
    public boolean saveChangeLog(T entity,T oldEntity) {
        if (null == entity) {
            return false;
        }
        boolean saveFlag = false;
        Class<?> cls = entity.getClass();
        com.baomidou.mybatisplus.core.metadata.TableInfo tableInfo = TableInfoHelper.getTableInfo(cls);
        Assert.notNull(tableInfo, "error1: can not execute. because can not find cache of TableInfo for entity!");
        String keyProperty = tableInfo.getKeyProperty();
        Assert.notEmpty(keyProperty, "error2: can not execute. because can not find column for id from entity!");
        Object idVal = ReflectionKit.getFieldValue( entity, tableInfo.getKeyProperty());
        /*if (!StringUtils.checkValNotNull(idVal)) {
            return this.save(entity);
        }
        // 查询修改前数据
        T oldEntity = this.getByIdNoCache((Serializable) idVal);
        // 判断数据是否为空，为空则需要调保存方法
        if (Objects.isNull(oldEntity)) {
            return this.save(entity);
        }*/
        /*
         * 原有逻辑：
         * 处理新旧数据间的差别，并组装修改日志数据
         * List<UpdateLogDO> logs = handleCompareData(oldEntity, entity, tableInfo, idVal.toString()); //NOSONAR
         */
        //saveFlag = this.updateById(entity);

        /*
         * 处理新旧数据间的差别，并组装修改日志数据。
         * 因为业务上要查看所有字段的对比，所以不能用入参的实体作为最新的数据源，必须用更新后的数据作为对比数据。
         * TODO 这里性能低下，日后需要优化。
         */
        T newEntity = this.getByIdNoCache((Serializable) idVal);
        List<UpdateLogDO> logs = handleCompareData2(oldEntity, newEntity, tableInfo, idVal.toString());
        // 修改日志不为空，保存修改日志
        if (saveFlag && !logs.isEmpty()) {
            updateLogService.saveUpdateLogBatch(logs);
        }
        return saveFlag;
    }
}

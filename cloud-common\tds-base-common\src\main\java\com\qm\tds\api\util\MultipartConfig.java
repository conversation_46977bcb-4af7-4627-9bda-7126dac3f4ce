package com.qm.tds.api.util;

import com.alibaba.druid.util.StringUtils;
import jakarta.servlet.MultipartConfigElement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.File;

/**
 * @Author: XUXIAOLIANG
 * @Date: 2022/02/12
 * @Description: 文件临时路径 配置类
 */
@Slf4j
@Configuration
public class MultipartConfig {

    @Value("${tupload.localTempPath:#{null}}")
    private String localTempPath;


    /**
     * 文件上传临时路径
     * 注：文件存储临时文件夹必须有，否则上传文件，可能会报错：
     * Could not parse multipart servlet request; nested exception is java.io.IOException: The temporary upload location
     */
    @Bean
    MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        String osName = System.getProperties().getProperty("os.name");
        log.info("当前运行系统环境："+osName);
        if(!StringUtils.isEmpty(osName) && !osName.contains("Windows")){
            log.info("开始创建临时访问文件夹路径："+localTempPath);
            if(StringUtils.isEmpty(localTempPath)){
                localTempPath="/app/data/upload_tmp";
            }

            //判断是否有文件夹，没有就创建
            File file = new File(localTempPath);
            if (!file.exists()) {
                file.mkdirs();
            }
            factory.setLocation(localTempPath);
            log.info("成功创建临时访问文件夹路径："+localTempPath);

        }
        return factory.createMultipartConfig();
    }
}
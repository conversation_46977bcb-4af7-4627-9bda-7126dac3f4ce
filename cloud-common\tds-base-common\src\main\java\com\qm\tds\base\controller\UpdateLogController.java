package com.qm.tds.base.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.bean.UpdateLogDO;
import com.qm.tds.base.domain.dto.UpdateLogDTO;
import com.qm.tds.base.service.UpdateLogService;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <p>
 * Controller
 * 修改记录（数据日志）
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
@Tag(name = "修改记录Controller （数据日志）", description = "修改记录Controller （数据日志）")
@RestController
@RequestMapping("/updateLog")
public class UpdateLogController extends BaseController {
    @Autowired
    private UpdateLogService updateLogService;
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 使用系统默认的保存/修改 方法
     */
    @Operation(summary = "使用系统默认的保存/修改 方法", description = "[author:10200571]")
    @PostMapping("/save")
    public JsonResultVo<UpdateLogDO> save(@RequestBody UpdateLogDO tempDO) {
        JsonResultVo<UpdateLogDO> resultObj = new JsonResultVo<>();
        boolean flag = updateLogService.saveOrUpdate(tempDO);
        if (flag) {
            resultObj.setData(tempDO);
        } else {
            String message = i18nUtil.getMessage("ERR.basecommon.common.saveFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的id删除数据
     */
    @Operation(summary = "根据传入的id删除数据", description = "[author:10200571]")
    @PostMapping("/deleteById")
    public JsonResultVo<String> deleteById(@RequestBody UpdateLogDO tempDO) {
        JsonResultVo<String> resultObj = new JsonResultVo<>();
        boolean flag = updateLogService.removeById(tempDO.getId());
        if (flag) {
            String message = i18nUtil.getMessage("ERR.basecommon.common.delSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.basecommon.common.delFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的map删除信息
     */
    @Operation(summary = "根据传入的map删除信息", description = "[author:10200571]")
    @PostMapping("/deleteByMap")
    public JsonResultVo<String> deleteByMap(@RequestBody String requestJson) {
        JsonResultVo<String> resultObj = new JsonResultVo<>();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> map;
        try {
            map = objectMapper.readValue(requestJson, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            String message = i18nUtil.getMessage("ERR.basecommon.common.operateFail");
            resultObj.setMsgErr(message + ": " + e.getMessage());
            return resultObj;
        }
        boolean flag = updateLogService.removeByMap(map);
        if (flag) {
            String message = i18nUtil.getMessage("ERR.basecommon.common.operateSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.basecommon.common.operateFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @Operation(summary = "根据传入的实体信息进行查询", description = "[author:10200571]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<UpdateLogDO>> table(@RequestBody UpdateLogDTO tempDTO) {
        //定义查询构造器
        QmQueryWrapper<UpdateLogDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<UpdateLogDO> lambdaWrapper = queryWrapper.lambda();
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getId())) {
            lambdaWrapper.eq(UpdateLogDO::getId, tempDTO.getId());
        }
        //表名
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVtablename())) {
            lambdaWrapper.eq(UpdateLogDO::getVtablename, tempDTO.getVtablename());
        }
        //列名
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVcolname())) {
            lambdaWrapper.eq(UpdateLogDO::getVcolname, tempDTO.getVcolname());
        }
        //标签
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVcoltext())) {
            lambdaWrapper.like(UpdateLogDO::getVcoltext, tempDTO.getVcoltext());
        }
        //数据ID
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getNdataid())) {
            lambdaWrapper.eq(UpdateLogDO::getNdataid, tempDTO.getNdataid());
        }
        //变更前值
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVoldvalue())) {
            lambdaWrapper.eq(UpdateLogDO::getVoldvalue, tempDTO.getVoldvalue());
        }
        //变更后值
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVnewvalue())) {
            lambdaWrapper.eq(UpdateLogDO::getVnewvalue, tempDTO.getVnewvalue());
        }
        //操作员
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getNopr())) {
            lambdaWrapper.eq(UpdateLogDO::getNopr, tempDTO.getNopr());
        }
        //操作时间
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getDopr())) {
            lambdaWrapper.eq(UpdateLogDO::getDopr, tempDTO.getDopr());
        }

        //增加语言标识----20220311--
        String LanguageCode = BootAppUtil.getLoginKey().getLanguageCode();
        if (!BootAppUtil.isNullOrEmpty(LanguageCode)) {
            tempDTO.setVlanguagecode(LanguageCode);
        }
        //语言标识---20220317
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVlanguagecode())) {
            lambdaWrapper.eq(UpdateLogDO::getVlanguagecode, tempDTO.getVlanguagecode());
        }
        //操作时间倒序排序
        lambdaWrapper.orderByDesc(UpdateLogDO::getDopr);
        //查询数据，使用table函数。
        QmPage<UpdateLogDO> list = updateLogService.table(queryWrapper, tempDTO);
        JsonResultVo<QmPage<UpdateLogDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    /**
     * 查询数据，使用Map进行查找数据集合。
     */
    @Operation(summary = "查询数据，使用Map进行查找数据集合。", description = "[author:10200571]")
    @PostMapping("/tableByMap")
    public JsonResultVo<QmPage<UpdateLogDO>> tableByMap(@RequestBody String requestJson) {
        JsonResultVo<QmPage<UpdateLogDO>> resultObj = new JsonResultVo<>();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> map;
        try {
            map = objectMapper.readValue(requestJson, new TypeReference<Map<String, String>>() {});
        } catch (JsonProcessingException e) {
            String message = i18nUtil.getMessage("ERR.basecommon.common.operateFail");
            resultObj.setMsgErr(message + ": " + e.getMessage());
            return resultObj;
        }

        QmQueryWrapper<UpdateLogDO> qmQueryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<UpdateLogDO> lambdaWrapper = qmQueryWrapper.lambda();
        if (!BootAppUtil.isNullOrEmpty(map.get("ID"))) {
            lambdaWrapper.eq(UpdateLogDO::getId, map.get("ID"));
        }
        // 表名
        if (!BootAppUtil.isNullOrEmpty(map.get("VTABLENAME"))) {
            lambdaWrapper.eq(UpdateLogDO::getVtablename, map.get("VTABLENAME"));
        }
        // 列名
        if (!BootAppUtil.isNullOrEmpty(map.get("VCOLNAME"))) {
            lambdaWrapper.eq(UpdateLogDO::getVcolname, map.get("VCOLNAME"));
        }
        // 标签
        if (!BootAppUtil.isNullOrEmpty(map.get("VCOLTEXT"))) {
            lambdaWrapper.eq(UpdateLogDO::getVcoltext, map.get("VCOLTEXT"));
        }
        // 数据ID
        if (!BootAppUtil.isNullOrEmpty(map.get("NDATAID"))) {
            lambdaWrapper.eq(UpdateLogDO::getNdataid, map.get("NDATAID"));
        }
        // 变更前值
        if (!BootAppUtil.isNullOrEmpty(map.get("VOLDVALUE"))) {
            lambdaWrapper.eq(UpdateLogDO::getVoldvalue, map.get("VOLDVALUE"));
        }
        // 变更后值
        if (!BootAppUtil.isNullOrEmpty(map.get("VNEWVALUE"))) {
            lambdaWrapper.eq(UpdateLogDO::getVnewvalue, map.get("VNEWVALUE"));
        }
        // 操作员
        if (!BootAppUtil.isNullOrEmpty(map.get("NOPR"))) {
            lambdaWrapper.eq(UpdateLogDO::getNopr, map.get("NOPR"));
        }
        // 操作时间
        if (!BootAppUtil.isNullOrEmpty(map.get("DOPR"))) {
            lambdaWrapper.eq(UpdateLogDO::getDopr, map.get("DOPR"));
        }
        // 时间戳
        if (!BootAppUtil.isNullOrEmpty(map.get("DTSTAMP"))) {
            lambdaWrapper.eq(UpdateLogDO::getDtstamp, map.get("DTSTAMP"));
        }
        //获取分页信息
        JsonParamDto jsonParamDto = getParaFromMap(map);
        //查询数据，使用table函数。
        QmPage<UpdateLogDO> list = updateLogService.table(qmQueryWrapper, jsonParamDto);
        JsonResultVo<QmPage<UpdateLogDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }
}

package com.qm.tds.base.domain;

import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 当前用户信息
 */
@Schema(description = "当前用户信息")
@Slf4j
@Component
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class LoginKeyDO {
    /**
     * 公司ID
     */
    @Schema(description = "公司ID")
    private String companyId;

    /**
     * 操作员ID
     */
    @Schema(description = "操作员ID")
    private String operatorId;

    /**
     * 操作员名称
     */
    @Schema(description = "操作员名称")
    private String operatorName;

    /**
     * 语言代码
     */
    @Schema(description = "语言代码")
    private String languageCode;

    /**
     * 人员代码
     */
    @Schema(description = "人员代码")
    private String personCode;

    /**
     * 客户组id
     */
    @Schema(description = "客户组id")
    private String custGroupid;

    /**
     * 请求来源
     */
    @Schema(description = "请求来源")
    private String appId;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;

    /**
     * 登录唯一标识
     */
    @Schema(description = "登录唯一标识")
    private String loginTimestamp;

    /**
     * 获取数值型公司ID
     *
     * @return 数值型公司ID
     */
    public int getCompanyIdInt() {
        int nRet = 0;

        try {
            nRet = Integer.parseInt(companyId != null ? companyId : "0");
        } catch (Exception ex) {
            nRet = 0;
        }

        return nRet;
    }

    /**
     * 获取数值型公司ID
     *
     * @return 数值型公司ID
     */
    public long getCompanyIdLong() {
        long nRet = 0;

        try {
            nRet = Long.parseLong(companyId != null ? companyId : "0");
        } catch (Exception ex) {
            nRet = 0;
        }

        return nRet;
    }

    /**
     * 获取数值型操作员ID
     *
     * @return 数值型操作员ID
     */
    public int getOperatorIdInt() {
        int nRet = 0;

        try {
            nRet = Integer.parseInt(operatorId != null ? operatorId : "0");
        } catch (Exception ex) {
            nRet = 0;
        }

        return nRet;
    }

    /**
     * 获取数值型操作员ID
     *
     * @return 数值型操作员ID
     */
    public long getOperatorIdLong() {
        long nRet = 0;

        try {
            nRet = Long.parseLong(operatorId != null ? operatorId : "0");
        } catch (Exception ex) {
            nRet = 0;
        }

        return nRet;
    }

    public String getCompanyId() {
        if (BootAppUtil.isNullOrEmpty(companyId)) {
            return "";
        }
        return companyId;
    }

    public String getLanguageCode() {
        if (BootAppUtil.isNullOrEmpty(languageCode)) {
            return "zh";
        }
        return languageCode;
    }
}

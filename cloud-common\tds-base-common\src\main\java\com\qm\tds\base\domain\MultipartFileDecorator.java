package com.qm.tds.base.domain;

import com.qm.tds.util.BootAppUtil;
import lombok.NonNull;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;

/**
 * multipartFile装饰器
 *
 * <AUTHOR>
 */
public class MultipartFileDecorator implements MultipartFile {

    private MultipartFile mpf;
    private String name;
    private String originalFilename;
    private String contentType;

    @NonNull
    private byte[] bytes;

    public MultipartFileDecorator(MultipartFile mpf, byte[] newFileBytes) {
        this.mpf = mpf;
        this.bytes = newFileBytes;
    }

    public MultipartFileDecorator(byte[] newFileBytes
            , String name
            , String originalFilename
            , String contentType) {
        this.name = name;
        this.originalFilename = originalFilename;
        this.contentType = contentType;
        this.bytes = newFileBytes;
    }

    public MultipartFileDecorator(byte[] newFileBytes, String name, String originalFilename) {
        this.name = name;
        this.originalFilename = originalFilename;
        this.contentType = getContentTypeByFile(originalFilename);
        this.bytes = newFileBytes;
    }

    /**
     * 获取文件后缀名
     *
     * @param fileName 文件名
     * @return 文件后缀名
     */
    private static String getFileExtension(String fileName) {
        if (!BootAppUtil.isNullOrEmpty(fileName)) {
            return fileName.substring(fileName.lastIndexOf('.') + 1);
        } else {
            return "";
        }
    }

    /**
     * 按文件名获取ContentType
     *
     * @param fileName 文件名
     * @return ContentType
     */
    public static String getContentTypeByFile(String fileName) {
        String contentType;
        String fileExtension = getFileExtension(fileName).toLowerCase();
        switch (fileExtension) {
            case "xls":
            case "xlt":
            case "xla":
                contentType = "application/vnd.ms-excel";
                break;
            case "xlsx":
                contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                break;
            case "zip":
            case "tar":
            case "pdf":
                contentType = "application/" + fileExtension;
                break;
            default:
                contentType = "application/octet-stream";
        }
        return contentType;
    }

    @Override
    public String getName() {
        if (mpf == null) {
            return name;
        }
        return mpf.getName();
    }

    @Override
    public String getOriginalFilename() {
        if (mpf == null) {
            return originalFilename;
        }
        return mpf.getOriginalFilename();
    }

    @Override
    public String getContentType() {
        if (mpf == null) {
            return contentType;
        }
        return mpf.getContentType();
    }

    @Override
    public boolean isEmpty() {
        return bytes.length == 0;
    }

    @Override
    public long getSize() {
        return bytes.length;
    }

    @Override
    public byte[] getBytes() {
        return bytes;
    }

    @Override
    public InputStream getInputStream() {
        return new ByteArrayInputStream(bytes);
    }

    @Override
    public void transferTo(File destination) throws IOException {
        try (OutputStream outputStream = new FileOutputStream(destination)) {
            outputStream.write(bytes);
        }
    }
}

package com.qm.tds.base.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 修改记录（数据日志）
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sysb100")
@Schema(description = "修改记录（数据日志）")
public class UpdateLogDO implements Serializable {

    @Schema(description = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "表名")
    @TableField("VTABLENAME")
    private String vtablename;

    @Schema(description = "列名")
    @TableField("VCOLNAME")
    private String vcolname;

    @Schema(description = "标签")
    @TableField("VCOLTEXT")
    private String vcoltext;

    @Schema(description = "数据ID")
    @TableField("NDATAID")
    private String ndataid;

    @Schema(description = "变更前值")
    @TableField("VOLDVALUE")
    private String voldvalue;

    @Schema(description = "变更后值")
    @TableField("VNEWVALUE")
    private String vnewvalue;

    @Schema(description = "操作员")
    @TableField("NOPR")
    private String nopr;

    @Schema(description = "操作时间")
    @TableField("DOPR")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dopr;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "操作员代码")
    @TableField(exist = false)
    private String voprcode;

    @Schema(description = "操作员名称")
    @TableField(exist = false)
    private String voprtext;

    @Schema(description = "语言代码")
    private String vlanguagecode;
}

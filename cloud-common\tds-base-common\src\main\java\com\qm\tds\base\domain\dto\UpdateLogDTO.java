package com.qm.tds.base.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 修改记录（数据日志）
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
@Schema(description = "日志UpdateLogDTO对象")
@Data
public class UpdateLogDTO extends JsonParamDto {

    private static final long serialVersionUID = 1L;
    private String id;
    @Schema(description = "表名")
    private String vtablename;
    @Schema(description = "列名")
    private String vcolname;
    @Schema(description = "标签")
    private String vcoltext;
    @Schema(description = "数据ID")
    private String ndataid;
    @Schema(description = "变更前值")
    private String voldvalue;
    @Schema(description = "变更后值")
    private String vnewvalue;
    @Schema(description = "操作员")
    private String nopr;
    @Schema(description = "操作时间")
    private Date dopr;
    @Schema(description = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @Schema(description = "语言代码")
    private String vlanguagecode;
}

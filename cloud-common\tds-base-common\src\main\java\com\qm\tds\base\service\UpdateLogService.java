package com.qm.tds.base.service;

import com.qm.tds.api.service.IQmBaseService;
import com.qm.tds.base.domain.bean.UpdateLogDO;

import java.util.List;

/**
 * <p>
 * 修改记录（数据日志） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
public interface UpdateLogService extends IQmBaseService<UpdateLogDO> {

    /**
     * @description 批量保存数据修改记录
     * <AUTHOR>
     * @date 2020/7/10 8:36
     */
    public boolean saveUpdateLogBatch(List<UpdateLogDO> list);
}

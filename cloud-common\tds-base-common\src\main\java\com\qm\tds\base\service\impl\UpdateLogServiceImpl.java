package com.qm.tds.base.service.impl;


import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.base.domain.bean.UpdateLogDO;
import com.qm.tds.base.mapper.UpdateLogMapper;
import com.qm.tds.base.service.UpdateLogService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 修改记录（数据日志） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-07
 */
@Service
public class UpdateLogServiceImpl extends QmBaseServiceImpl<UpdateLogMapper, UpdateLogDO> implements UpdateLogService {

    @Override
    public boolean saveUpdateLogBatch(List<UpdateLogDO> list) {
        return saveOrUpdateBatch(list, list.size());
    }
}

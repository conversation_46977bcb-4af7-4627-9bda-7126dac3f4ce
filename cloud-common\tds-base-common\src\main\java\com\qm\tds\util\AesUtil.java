package com.qm.tds.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;

import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * 加密工具类 实现aes加密、解密
 */
@SuppressWarnings("restriction")
@Slf4j
public class AesUtil {
    private AesUtil() {
    }

    /**
     * 密钥（16进制，和前台保持一致，或者是作为参数直接传过来也可以）
     */
    @Value("${default.key:}")
    private static String DEFAULT_KEY;
    /**
     * 算法PKCS5Padding
     */
    private static final String ALGORITHMSTR = "AES/ECB/PKCS5Padding";

    /**
     * AES加密--为base 64 code
     *
     * <AUTHOR>
     * @date 2020/6/22 8:45
     */
    public static String encrypt(String content) throws NoSuchPaddingException, BadPaddingException, NoSuchAlgorithmException, IllegalBlockSizeException, InvalidKeyException {
        return base64Encode(aesEncryptToBytes(content));
    }

    /**
     * AES解密--并解密base 64 code
     *
     * <AUTHOR>
     * @date 2020/6/22 8:45
     */
    public static String decrypt(String encryptStr) throws IllegalBlockSizeException, InvalidKeyException, BadPaddingException, NoSuchAlgorithmException, NoSuchPaddingException {
        encryptStr = encryptStr.replace("#", "/");
        return StringUtils.isEmpty(encryptStr) ? null : aesDecryptByBytes(base64Decode(encryptStr));
    }

    /**
     * base 64 encode编码
     *
     * <AUTHOR>
     * @date 2020/6/22 8:45
     */
    private static String base64Encode(byte[] bytes) {
        return Base64.encodeBase64String(bytes);
    }

    /**
     * base 64 decode解码---》
     * 因为传过来的值是通过base64编码而后再进行aes加密出来的，所以解密之前先进行base64解码
     *
     * <AUTHOR>
     * @date 2020/6/22 8:45
     */
    private static byte[] base64Decode(String base64Code) {
        return StringUtils.isEmpty(base64Code) ? null : java.util.Base64.getDecoder().decode(base64Code);
    }

    /**
     * AES加密
     *
     * <AUTHOR>
     * @date 2020/6/22 8:45
     */
    private static byte[] aesEncryptToBytes(String content) throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(128);
        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(DEFAULT_KEY.getBytes(), "AES"));

        return cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * AES解密
     *
     * <AUTHOR>
     * @date 2020/6/22 8:45
     */
    private static String aesDecryptByBytes(byte[] encryptBytes) throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(128);
        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(DEFAULT_KEY.getBytes(), "AES"));
        byte[] decryptBytes = cipher.doFinal(encryptBytes);

        return new String(decryptBytes, StandardCharsets.UTF_8);
    }
}

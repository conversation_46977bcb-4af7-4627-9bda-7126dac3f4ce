package com.qm.tds.util;

import com.qm.tds.base.domain.LoginKeyDO;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.DecimalFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

public class BootAppUtil {

    private BootAppUtil() {
    }

    /**
     * 判断入参是否为空。
     * 判断空的规则如下：
     * object：null
     * string：null、空字符串、null字符串
     * 数值：null、0
     * 日期型：小于1970-01-01 00:00:01
     *
     * @param obj 待判断参数
     * @return true为空
     */
    public static boolean isNullOrEmpty(Object obj) {
        boolean bRet = false;

        if (obj == null) {
            bRet = true;
        } else if (obj instanceof String) {
            bRet = isNullOrEmpty(obj.toString());
        } else if (obj instanceof Integer) {
            bRet = (Integer) obj == 0;
        } else if (obj instanceof Double) {
            bRet = (Double) obj == 0;
        } else if (obj instanceof Float) {
            bRet = (Float) obj == 0;
        } else if (obj instanceof Long) {
            bRet = (Long) obj == 0;
        } else if (obj instanceof Date) {
            Calendar c = Calendar.getInstance();
            c.set(1970, 0, 1, 0, 0, 1);
            c.set(Calendar.MILLISECOND, 0);//把毫秒数也设置为0
            bRet = ((Date) obj).before(c.getTime());
        }

        return bRet;
    }

    /**
     * 判断字符串是否为空
     *
     * @param str 待检测字符串
     * @return 为空则返回true，否则返回false
     */
    public static boolean isNullOrEmpty(String str) {
        return StringUtils.isBlank(str) || "null".equals(str);
    }

    /**
     * 检测字符串是否不为空(null,"","null")
     *
     * @param s 待检测字符串
     * @return 不为空则返回true，否则返回false
     */
    public static boolean isnotNullOrEmpty(String s) {
        return StringUtils.isNotBlank(s) && !"null".equals(s);
    }

    /**
     * 字符串转换为字符串数组
     *
     * @param str        字符串
     * @param splitRegex 分隔符
     * @return 字符串数组。如果字符串为空，则返回null
     */
    public static String[] str2StrArray(String str, String splitRegex) {
        if (isNullOrEmpty(str)) {
            return new String[0];
        } else {
            return str.split(splitRegex);
        }
    }

    /**
     * 随机对象定位为全局，防止出现并发条件下获得的随机数是相同的问题。
     */
    private static Random random = new Random();

    /**
     * 获取业务系统的单据编号
     * 采用线程锁的方式 防止业务单据号编码重复
     *
     * @return 单据编号
     */
    public static synchronized String getBizNo() {
        DecimalFormat df = new DecimalFormat("00");
        return DateFormatUtils.format(new Date(), "yyyyMMddHHmmss")
                + df.format(random.nextInt(100));
    }

    /**
     * 获取客户端访问服务端的环境信息
     *
     * @param request 当前Request请求对象
     * @return 获取User-Agent信息
     */
    public static String getHeaderUserAgent(HttpServletRequest request) {
        return request.getHeader("User-Agent");
    }

    /**
     * 获取当前请求的用户信息
     *
     * @return 当前请求的用户信息
     */
    public static LoginKeyDO getLoginKey() {
        //获取当前http上下文Request对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        RequestContextHolder.setRequestAttributes(attributes, true);
        //组装用户信息
        LoginKeyDO loginKey = new LoginKeyDO();
        if (null != attributes) {
            HttpServletRequest request = attributes.getRequest();
            /*
             * String nCompanyId = request.getParameter("ncompanyid");
             * String nOperatorId = request.getParameter("userId");
             * String vLanguageCode = request.getParameter("lang");
             * String vPersonCode = request.getParameter("vpersoncode");
             * String nCustgroupId = request.getParameter("ncustgroupid");
             * String appId = request.getParameter("appId");
             * String userAgent = request.getParameter("userAgent");
             * String jwt = request.getParameter("jwt");
             * String authorization = request.getParameter("Authorization");
             */
            //用户信息
            String companyId = request.getHeader("companyId");
            String userId = request.getHeader("userId");
            String userName = request.getHeader("userName");
            String loginTimestamp = request.getHeader("loginTimestamp");
            String lang = request.getHeader("lang");
            String personCode = request.getHeader("personCode");
            String custGroupId = request.getHeader("custGroupId");
            String appId = request.getHeader("appId");
            String tenantId = request.getHeader("tenantId");
            if (userName != null) {
                try {
                    userName = URLDecoder.decode(userName, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    // 字符转义失败，使用Header中的原始字符串
                }
            }
            loginKey.setCompanyId(companyId);
            loginKey.setOperatorId(userId);
            loginKey.setOperatorName(userName);
            loginKey.setLoginTimestamp(loginTimestamp);
            loginKey.setLanguageCode(BootAppUtil.isNullOrEmpty(lang) ? "zh" : lang);
            loginKey.setCustGroupid(custGroupId);
            loginKey.setPersonCode(personCode);
            loginKey.setAppId(appId);
            loginKey.setTenantId(tenantId);
        }
        return loginKey;
    }
}

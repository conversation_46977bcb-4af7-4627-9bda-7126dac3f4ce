package com.qm.tds.util;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import com.qm.tds.api.exception.QmException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;

/**
 * <AUTHOR>
 * @description COS 文件平台工具类
 * @date 2020/11/2 16:39
 */
@Component
@Slf4j
public class COSUtils {
    /**
     * 密钥id
     */
    @Value("${qm.base.upload.cos-secretId:}")
    private String secretId;
    /**
     * 密钥key
     */
    @Value("${qm.base.upload.cos-secretKey:}")
    private String secretKey;
    /**
     * 区域
     */
    @Value("${qm.base.upload.cos-regionName:}")
    private String regionName;
    /**
     * 存储桶key
     */
    @Value("${qm.base.upload.cos-bucket:}")
    private String bucket;
    /**
     * appid
     */
    @Value("${qm.base.upload.cos-appid:}")
    private String appid;
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * @return 客户端
     * <AUTHOR>
     * @Deprecated 创建客户端
     */
    private COSClient createCOSClient() {
        // 2 设置 bucket 的区域, COS 地域的简称请参照 https://cloud.tencent.com/document/product/436/6224
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        // clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参见源码或者常见问题 Java SDK 部分。
        Region region = new Region(regionName);
        ClientConfig clientConfig = new ClientConfig(region);
        // 3 生成 cos 客户端。
        return new COSClient(cred, clientConfig);
    }

    public Boolean uploadFile(String key, MultipartFile multipartFile) {
        Boolean result = false;
        COSClient cosClient = this.createCOSClient();
        //bucket 名需包含APPID
        String bucketName = bucket + "-" + appid;
        ObjectMetadata objectMetadata = new ObjectMetadata();
        try {
            //设置输入流长为
            objectMetadata.setContentLength(multipartFile.getSize());
            //设置content type,默认是application/octet-stream
            objectMetadata.setContentType(multipartFile.getContentType());
            //生成PutObjectRequest
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, multipartFile.getInputStream(), objectMetadata);
            //上传文件
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            // putobjectResult会返回文件的etag
            String etag = putObjectResult.getETag();
            result = true;
        } catch (CosServiceException e) {
            log.info("---error--"+e.getMessage(), e);
            String cosServiceException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosServiceException");
            throw new QmException(cosServiceException, e);
        } catch (CosClientException e) {
            log.info("---error--"+e.getMessage(), e);
            String cosServiceException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosServiceException");
            throw new QmException(cosServiceException, e);
        } catch (IOException e) {
            log.info("---error--"+e.getMessage(), e);
            String cosServiceException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosServiceException");
            throw new QmException(cosServiceException, e);
        } finally {
            cosClient.shutdown();
        }

        return result;
    }

    public Boolean uploadFile(String key, Long size, String contentType, InputStream inputStream) {
        Boolean result = false;
        COSClient cosClient = this.createCOSClient();
        //bucket 名需包含APPID
        String bucketName = bucket + "-" + appid;
        ObjectMetadata objectMetadata = new ObjectMetadata();
        try {
            //设置输入流长为
            objectMetadata.setContentLength(size);
            //设置content type,默认是application/octet-stream
            objectMetadata.setContentType(contentType);
            //生成PutObjectRequest
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, inputStream, objectMetadata);
            //上传文件
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            // putobjectResult会返回文件的etag
            String etag = putObjectResult.getETag();
            result = true;
        } catch (CosServiceException e) {
            log.info("---error--"+e.getMessage(), e);
            String cosServiceException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosServiceException");
            throw new QmException(cosServiceException, e);
        } catch (CosClientException e) {
            log.info("---error--"+e.getMessage(), e);
            String cosServiceException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosServiceException");
            throw new QmException(cosServiceException, e);
        } finally {
            cosClient.shutdown();
        }

        return result;
    }

    /*
     * 服务器端本地下载
     * */
    public Boolean downloadFile(String key, String outputFilePath) {
        Boolean result = false;
        COSClient cosClient = this.createCOSClient();
        //bucket 名需包含APPID
        String bucketName = bucket + "-" + appid;
        try {
            GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, key);
            File downLoadFile = new File(outputFilePath);
            ObjectMetadata downObjectMeta = cosClient.getObject(getObjectRequest, downLoadFile);
            result = true;
        } catch (CosServiceException e) {
            log.info("---error--"+e.getMessage(), e);

            String cosServiceException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosServiceException");
            throw new QmException(cosServiceException, e);
        } catch (CosClientException e) {
            log.info("---error--"+e.getMessage(), e);
            String cosClientException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosClientException");
            throw new QmException(cosClientException, e);
        } finally {
            cosClient.shutdown();
        }
        return result;
    }

    public COSObjectInputStream downLoadStream(String key) {
        COSObjectInputStream result = null;
        COSClient cosClient = this.createCOSClient();
        //bucket 名需包含APPID
        String bucketName = bucket + "-" + appid;
        try {
            GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, key);
            //限流使用的单位是bit/s,这里设置下载带宽带限制为10MB/S
            getObjectRequest.setTrafficLimit(80 * 1024 * 1024);
            COSObject cosObject = cosClient.getObject(getObjectRequest);
            result = cosObject.getObjectContent();
            //下载对象的CRC64
            String crc64Ecma = cosObject.getObjectMetadata().getCrc64Ecma();
        } catch (CosServiceException e) {
            log.info("---error--"+e.getMessage(), e);

            String cosServiceException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosServiceException");
            throw new QmException(cosServiceException, e);
        } catch (CosClientException e) {
            log.info("---error--"+e.getMessage(), e);
            String cosClientException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosClientException");
            throw new QmException(cosClientException, e);
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
            String fileioError = i18nUtil.getMessage("ERR.basecommon.COSUtils.fileioError");
            throw new QmException(fileioError, e);
        } finally {
            cosClient.shutdown();
        }
        return result;
    }

    public String generatePresignedUrl(String key) {
        String result = "";
        COSClient cosClient = this.createCOSClient();
        //bucket 名需包含APPID
        String bucketName = bucket + "-" + appid;
        try {
            //半小时后过期
            Date expirationtime = new Date(System.currentTimeMillis() + 30L * 60L * 1000L);
            URL url = cosClient.generatePresignedUrl(bucketName, key, expirationtime);
            if (!BootAppUtil.isNullOrEmpty(url)) {
                result = url.toString();
            }
        } catch (CosServiceException e) {
            log.info("---error--"+e.getMessage(), e);

            String cosServiceException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosServiceException");
            throw new QmException(cosServiceException, e);
        } catch (CosClientException e) {
            log.info("---error--"+e.getMessage(), e);
            String cosClientException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosClientException");
            throw new QmException(cosClientException, e);
        } finally {
            cosClient.shutdown();
        }
        return result;
    }


    public Boolean deleteFile(String key) {
        Boolean result = false;
        COSClient cosClient = this.createCOSClient();
        //bucket 名需包含APPID
        String bucketName = bucket + "-" + appid;
        try {
            cosClient.deleteObject(bucketName, key);
            result = true;
        } catch (CosServiceException e) {
            log.info("---error--"+e.getMessage(), e);

            String cosServiceException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosServiceException");
            throw new QmException(cosServiceException, e);
        } catch (CosClientException e) {
            log.info("---error--"+e.getMessage(), e);
            String cosClientException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosClientException");
            throw new QmException(cosClientException, e);
        } finally {
            cosClient.shutdown();
        }
        return result;
    }

    /*
     * 查询对象是否在腾讯云cos上存在
     * */
    public Boolean doesObjectExist(String key) {
        Boolean result = false;
        COSClient cosClient = this.createCOSClient();
        String bucketName = bucket + "-" + appid;
        ;
        try {
            result = cosClient.doesObjectExist(bucketName, key);
        } catch (CosServiceException e) {
            log.info("---error--"+e.getMessage(), e);

            String cosServiceException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosServiceException");
            throw new QmException(cosServiceException, e);
        } catch (CosClientException e) {
            log.info("---error--"+e.getMessage(), e);
            String cosClientException = i18nUtil.getMessage("ERR.basecommon.COSUtils.cosClientException");
            throw new QmException(cosClientException, e);
        } finally {
            cosClient.shutdown();
        }
        return result;
    }
}

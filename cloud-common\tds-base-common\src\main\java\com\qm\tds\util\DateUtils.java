package com.qm.tds.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.qm.tds.api.util.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * 时间、日期公用类
 */
@SuppressWarnings("unused")
@Slf4j
public class DateUtils {

    private static I18nUtil i18nUtil;

    private static I18nUtil getI18nUtil() {
        if (i18nUtil == null) {
            i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
        }
        return i18nUtil;
    }

    private DateUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 日期格式
     */
    public static final String DATE_PATTERN = "yyyy-MM-dd";
    /**
     * 时间格式
     */
    public static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    /**
     * 时间戳格式
     */
    public static final String TIMESTAMP_PATTERN = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * 获取系统可识别的最小日期
     *
     * @return 最小日期1900-01-01 00:00:00
     */
    public static Date getMinDate() {
        return new Date(-2209017600000L);
    }

    public static LocalDateTime getLocalMinDate() {
        return LocalDateTime.of(1900,1,1,0,0);
    }

    /**
     * 获取系统时间
     *
     * @return 系统时间
     * @deprecated 此功能仅是对老版本兼容，建议使用{@link DateUtils#getSysdateStr()}替代
     */
    @Deprecated()
    public static String getSysdate() {
        return DateUtils.getSysdateStr();
    }

    /**
     * 获取系统服务器上时间统一为东八区时间
     *
     * @return 系统时间
     */
    public static String getSysdateStr() {
        return DateUtils.getSysdateStr(DATE_TIME_PATTERN);
    }

    /**
     * 获取系统时间
     *
     * @param vPattern 日期格式掩码
     * @return 系统时间
     */
    public static String getSysdateStr(String vPattern) {
        if (StringUtils.isBlank(vPattern)) {
            vPattern = DATE_TIME_PATTERN;
        }
        return DateFormatUtils.format(
                DateUtils.getSysdateTime(),
                vPattern,
                TimeZone.getTimeZone("Asia/Shanghai"));
    }

    /**
     * 获取系统时间，日后可能调整为获取数据库时间
     *
     * @return 系统时间
     */
    public static Date getSysdateTime() {
        //TODO 日后可能调整为获取数据库时间
        return new Date();
    }

    /**
     * 获取当前系统时间戳
     *
     * @return 当前系统时间戳
     */
    public static Timestamp getSysTimestamp() {
        return new Timestamp(System.currentTimeMillis());
    }

    /**
     * 根据给定的日期格式，格式化指定的日期
     *
     * @param date    待转化日期
     * @param pattern 格式
     * @return 转换后的日期字符串
     */
    public static String format(Date date, String pattern) {
        return format(date, pattern, null);
    }

    /**
     * 格式化日期 带时区
     *
     * @param date        时间
     * @param pattern     格式化
     * @param timeZoneStr 时区
     * @return java.lang.String
     * @date 2020/6/19 16:37
     * <AUTHOR>
     */
    public static String format(Date date, String pattern, String timeZoneStr) {
        if (date == null) {
            date = getMinDate();
        }
        if (StringUtils.isBlank(timeZoneStr)) {
            return DateFormatUtils.format(date, pattern);
        }
        TimeZone timeZone;
        try {
            timeZone = TimeZone.getTimeZone(timeZoneStr);
        } catch (Exception e) {
            return DateFormatUtils.format(date, pattern);
        }
        return DateFormatUtils.format(date, pattern, timeZone);
    }

    /**
     * 将 {@link DateUtils#DATE_TIME_PATTERN} 格式字符串转换成Date类型
     *
     * @param vDateStr {@link DateUtils#DATE_TIME_PATTERN}格式字符串（yyyy-MM-dd HH:mm:ss）
     * @return 日期
     */
    public static Date parse(String vDateStr) {
        return parse(vDateStr, DATE_TIME_PATTERN);
    }

    /**
     * 将指定格式字符串转换成Date类型
     *
     * @param vDateStr 日期格式字符串
     * @param vPattern 日期格式
     * @return 日期
     */
    public static Date parse(String vDateStr, String vPattern) {
        Date dRet;

        if (BootAppUtil.isNullOrEmpty(vDateStr)) {
            dRet = getMinDate();
        } else {
            try {
                dRet = org.apache.commons.lang3.time.DateUtils.parseDate(vDateStr, vPattern);
            } catch (ParseException e) {
                log.info("---error--"+"字符串[" + vDateStr + "]转换为[" + vPattern + "]格式日期失败！", e);
                dRet = getMinDate();
            }
        }
        return dRet;
    }

    /**
     * 获取某个时间与系统当前时间的差值
     *
     * @param date 日期
     * @return 时间差
     */
    public static String getTimeBeforeNow(Date date) {
        Date now = new Date();
        long l = now.getTime() - date.getTime();
        long day = l / (24 * 60 * 60 * 1000);
        long hour = (l / (60 * 60 * 1000) - day * 24);
        long min = ((l / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long s = (l / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        String r = "";
        if (day > 0) {
            r += day + "Day";
        }
        if (hour > 0) {
            r += hour + "Hour";
        }
        if (min > 0) {
            r += min + "Minute";
        }
        if (s > 0) {
            r += s + "Second";
        }
        r += "Before";
        return r;
    }

    /**
     * 更改时间戳格式
     */
    public static Date sqlTimestamp(String str) {
        Timestamp ts = new Timestamp(System.currentTimeMillis());
        StringBuilder sb = new StringBuilder(str);
        sb.replace(sb.length() - 7, sb.length() - 6, ".");
        try {
            ts = Timestamp.valueOf(sb.toString());
        } catch (Exception e) {
            log.info("---error--"+"更改时间戳格式失败！", e);
        }
        return ts;
    }


    /**
     * 两个时间差
     *
     * @param type 1.日 2.月 3.年 4.小时
     * <AUTHOR>
     * @date 2020/7/28 8:19
     */
    public static int compareDate(Date begin, Date end, int type) {
        Calendar beginTime = Calendar.getInstance();
        beginTime.setTime(begin);
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(end);
        switch (type) {
            case 1:
                // 不足24小时不算1天
                return (int) ((end.getTime() - begin.getTime()) / (1000 * 3600 * 24));
            case 2:
                // 正常应该是：(年-1)*12+月。但是因为两个相减，所以不减一也无所谓。
                return (endTime.get(Calendar.YEAR) * 12 + endTime.get(Calendar.MONTH)) - (beginTime.get(Calendar.YEAR) * 12 + beginTime.get(Calendar.MONTH));
            case 3:
                return endTime.get(Calendar.YEAR) - beginTime.get(Calendar.YEAR);
            case 4:
                // 不足60分钟不算1小时
                return (int) ((end.getTime() - begin.getTime()) / (1000 * 3600));
            default:
                return 0;
        }
    }


    /**
     * 获取月第一天  yyyyMM
     */
    public static Date getMonthFirstDay(String month) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parse(month, "yyyyMM"));
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.MONTH, 0);
        return calendar.getTime();
    }

    /**
     * 获取月最后一天 yyyyMM
     */
    public static Date getMonthLastDay(String month) {
        DateTime anyDay = DateUtil.parse(month, "yyyyMM");
        DateTime lastDate = DateUtil.endOfMonth(anyDay);
        return lastDate.toJdkDate();
    }

    /**
     * 某个日期前或后几天
     *
     * @param date 标准日期
     * @param days 正数：几天后；负数：几天前；0：今天
     * @return 调整后的日期
     */
    public static Date addDays(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, days);
        return calendar.getTime();
    }

    /**
     * 某个日期前或后几月
     *
     * @param date  标准日期
     * @param month 正数：几月后的今天；负数：几月前的今天；0：今天
     * @return 调整后的日期
     */
    public static Date addMonths(Date date, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, month);
        return calendar.getTime();
    }

    public static String getWeekDays(Date date) {
        Calendar cal = Calendar.getInstance();
        String sunday = getI18nUtil().getMessage("ERR.basecommon.DateUtils.sunday");
        String monday = getI18nUtil().getMessage("ERR.basecommon.DateUtils.monday");
        String tuesday = getI18nUtil().getMessage("ERR.basecommon.DateUtils.tuesday");
        String wednesday = getI18nUtil().getMessage("ERR.basecommon.DateUtils.wednesday");
        String thursday = getI18nUtil().getMessage("ERR.basecommon.DateUtils.thursday");
        String friday = getI18nUtil().getMessage("ERR.basecommon.DateUtils.friday");
        String saturday = getI18nUtil().getMessage("ERR.basecommon.DateUtils.saturday");
        String[] weekDays = {sunday, monday, tuesday, wednesday, thursday, friday, saturday};
        try {
            cal.setTime(date);
        } catch (Exception e) {
            e.printStackTrace();
        }
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1; // 指示一个星期中的某天。
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }
}

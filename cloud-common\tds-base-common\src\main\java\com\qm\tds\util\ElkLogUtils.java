package com.qm.tds.util;

import com.alibaba.fastjson.JSONObject;
import com.qm.tds.api.domain.LogLevelEnum;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2020/7/29$ 15:27$
 **/
@Component
public class ElkLogUtils {
    @Autowired
    private I18nUtil i18nUtil;
    private Logger logger = LoggerFactory.getLogger(ElkLogUtils.class);

    // 获取当前服务名称
    @Value("${spring.application.name}")
    private String serviceName;

    /**
     * @param tag       业务标识
     * @param level     日志级别
     * @param msg       保存的日志信息
     * @param msgExtend 日志自定义信息
     * @description 保存ELK日志
     * <AUTHOR>
     * @date 2020/7/29 16:08
     */
    public void saveElkLog(String tag, LogLevelEnum level, String msg, String msgExtend) {
        try {
            if (BootAppUtil.isNullOrEmpty(tag)) {
                String message = i18nUtil.getMessage("ERR.basecommon.ElkLogUtils.logMarkNull");
                logger.error(message);
            }
            if (BootAppUtil.isNullOrEmpty(level)) {
                String message = i18nUtil.getMessage("ERR.basecommon.ElkLogUtils.logLevelNull");
                logger.error(message);
            }
            String className = new Exception().getStackTrace()[1].getClassName();
            String methodName = new Exception().getStackTrace()[1].getMethodName();
            writeElkLog(className, methodName, tag, level, msg, msgExtend);

        } catch (Exception e) {
            String message = i18nUtil.getMessage("ERR.basecommon.ElkLogUtils.saveElkLogFail");
            logger.error(message + e.getMessage(), e);
        }
    }

    /**
     * @description 写入日志
     * <AUTHOR>
     * @date 2020/7/29 16:19
     */
    private void writeElkLog(String className, String methodName, String tag, LogLevelEnum level, String msg, String msgExtend) {
        try {
            JSONObject json = new JSONObject();
            //请求服务名称
            json.put("SERVICE_NAME", serviceName);
            //产生日志的类名
            json.put("TYPE_NAME", className);
            //产生日志的方法名
            json.put("METHOD_NAME", methodName);
            //访问时间
            json.put("ACCESS_TIME", DateFormatUtils.format(DateUtils.getSysdateTime(), "yyyy-MM-dd HH:mm:ss"));
            //日志的业务类型
            json.put("TAG", tag);
            // 日志级别
            json.put("LEVEL", level.getCode());
            // 日志内容
            if (BootAppUtil.isNullOrEmpty(msg)) {
                msg = "";
            }
            json.put("MSG", msg);
            // 日志扩展内容
            if (BootAppUtil.isNullOrEmpty(msgExtend)) {
                msgExtend = "{}";
            }
            json.put("MSG_EXTEND", msgExtend);
            // 登陆信息
            json.put("HEADER_INFO", BootAppUtil.getLoginKey());
            // 保存日志
            switch (level.getCode()) {
                case 1:
                    logger.info(json.toJSONString());
                    break;
                case 2:
                    logger.debug(json.toJSONString());
                    break;
                case 3:
                    logger.warn(json.toJSONString());
                    break;
                case 4:
                    logger.error(json.toJSONString());
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            String message = i18nUtil.getMessage("ERR.basecommon.ElkLogUtils.elkLogBuildFail");
            logger.error(message, e);
        }
    }
}

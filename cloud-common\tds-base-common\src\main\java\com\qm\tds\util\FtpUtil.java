package com.qm.tds.util;

import com.qcloud.cos.utils.IOUtils;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.util.SpringContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.support.WebApplicationContextUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.SocketException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @description FTP工具类
 * @date 2020/7/14 15:51
 */
@Slf4j
public class FtpUtil {

    private static I18nUtil i18nUtil;

    private static I18nUtil getI18nUtil() {
        if (i18nUtil == null) {
            i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
        }
        return i18nUtil;
    }
    /**
     * 文件路径分隔符
     */
    private static final String PATH_SPLIT = "/";

    private String ftpHost;
    private String ftpUserName;
    private String ftpPassword;
    private String floder;

    public String getFloder() {
        return floder;
    }

    public FtpUtil() {
        init();
    }

    private void init() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();

                ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(request.getServletContext());
                Environment env = ac.getBean(Environment.class);

                ftpHost = env.getProperty("spring.ftp.ftpHost");
                ftpUserName = env.getProperty("spring.ftp.ftpUserName");
                ftpPassword = env.getProperty("spring.ftp.ftpPassword");
                floder = env.getProperty("spring.ftp.floder");
            }
        } catch (Exception ex) {
            log.info("---error--"+"初始化FTP相关参数失败！", ex);
        }
    }

    private FTPClient ftpClient = null;

    /**
     * @description 获取ftp客户端
     * <AUTHOR>
     * @date 2020/7/14 15:10
     */
    public FTPClient getFTPClient() throws IOException {
        ftpClient = new FTPClient();
        ftpClient.enterLocalPassiveMode();        //这个设置允许被动连接--访问远程ftp时需要
        ftpClient.setDataTimeout(600000);       //设置传输超时时间为60秒
        ftpClient.setConnectTimeout(600000);       //连接超时为60秒
        ftpClient.setControlEncoding("UTF-8"); // 中文支持
        try {
            ftpClient.connect(ftpHost, 21);// 连接FTP服务器
            ftpClient.login(ftpUserName, ftpPassword);// 登陆FTP服务器
            log.debug("FTP连接的IP为{}，用户名：{}，密码是：{}", ftpHost, ftpUserName, ftpPassword);
            log.debug("FTP登录返回的CODE为：{}", ftpClient.getReplyCode());
            if (!FTPReply.isPositiveCompletion(ftpClient.getReplyCode())) {
                log.info("---error--"+"未连接到FTP，用户名或密码错误。");
                ftpClient.disconnect();
            }
        } catch (SocketException e) {
            log.info(e.getMessage(), e);
            String message = getI18nUtil().getMessage("ERR.basecommon.FtpUtil.ftpIpUrlWrong");
            throw new QmException(message, e);
        } catch (IOException e) {
            log.info(e.getMessage(), e);
            String message = getI18nUtil().getMessage("ERR.basecommon.FtpUtil.ftpPortWrong");
            throw new QmException(message, e);
        }
        return ftpClient;
    }

    /**
     * 上传文件
     *
     * @param path        ftp服务保存地址
     * @param fileName    上传到ftp的文件名
     * @param inputStream 输入文件流
     * @return
     */
    public boolean uploadFile(FTPClient client, String path, String fileName, InputStream inputStream) {
        boolean flag = false;
        try {
            ftpClient = client;
            ftpClient.setBufferSize(32 * 1024 * 1024);
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            // 进行文件夹切换
            if (path.indexOf('/') > -1) {
                String[] paths = path.split(PATH_SPLIT);
                for (String p : paths) {
                    createDirecroty(p);
//                    ftpClient.changeWorkingDirectory(p);
                }
            } else {
                createDirecroty(path);
//                ftpClient.changeWorkingDirectory(path);
            }
            log.debug("开始存储文件[" + fileName + "]....");
            ftpClient.storeFile(fileName, inputStream);
            inputStream.close();
            ftpClient.logout();
            flag = true;
        } catch (Exception e) {
            log.info("---error--"+"上传文件失败", e);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                    log.debug("上传文件成功");
                    log.debug("关闭上传文件流FTP客户端");
                } catch (IOException e) {
                    log.info("---error--"+e.getMessage(), e);
                }
            } else {
                log.debug("上传文件结束，不知道成功与否！");
            }
        }
        return flag;
    }

    //改变目录路径
    public boolean changeWorkingDirectory(String directory) throws IOException {
        boolean flag = true;
        try {
            flag = ftpClient.changeWorkingDirectory(directory);
            if (flag) {
                log.info("进入文件夹" + directory + " 成功！");
            } else {
                log.info("进入文件夹" + directory + " 失败！开始创建文件夹");
            }
        } catch (IOException ioe) {
            log.info("---error--"+ioe.getMessage(), ioe);
        }
        return flag;
    }

    /**
     * 创建多层目录文件，如果有ftp服务器已存在该文件，则不创建，如果无，则创建
     */
    public boolean createDirecroty(String remote) throws IOException {
        boolean success = false;
        String directory = remote + PATH_SPLIT;
        // 如果远程目录不存在，则递归创建远程服务器目录
        try {
            if (!directory.equalsIgnoreCase(PATH_SPLIT) && !changeWorkingDirectory(directory)) {
                int start = directory.startsWith(PATH_SPLIT) ? 1 : 0;
                int end = directory.indexOf('/', start);
                while (true) {
                    String subDirectory = new String(remote.substring(start, end).getBytes("GBK"), StandardCharsets.ISO_8859_1);
                    String path = PATH_SPLIT + subDirectory;
                    //false表示当前文件夹下没有文件
                    if (!existFile(path)) makeDirectory(subDirectory);
                    changeWorkingDirectory(subDirectory);
                    start = end + 1;
                    end = directory.indexOf('/', start);
                    // 检查所有目录是否创建完毕
                    if (end <= start) {
                        break;
                    }
                    success = true;
                }
            }
        } catch (IOException e) {
            log.info("---error--"+e.getMessage(), e);
        }
        return success;
    }

    //判断ftp服务器文件是否存在
    public boolean existFile(String path) throws IOException {
        boolean flag = false;
        FTPFile[] ftpFileArr = ftpClient.listFiles(path);
        if (ftpFileArr.length > 0) {
            flag = true;
        }
        return flag;
    }

    //创建目录
    public void makeDirectory(String dir) {
        try {
            boolean flag = ftpClient.makeDirectory(dir);
            log.info("创建文件夹 {} {}！", dir, flag ? "成功" : "失败");
        } catch (Exception e) {
            log.info("---error--"+"创建文件夹异常！", e);
        }
    }

    /**
     * 下载文件 *
     *
     * @param pathName FTP服务器文件目录 *
     * @param fileName 文件名称 *
     * @return
     */
    public boolean downloadFile(FTPClient client, String pathName, String fileName, OutputStream os) {
        boolean flag = false;
        String message = getI18nUtil().getMessage("ERR.basecommon.FtpUtil.downloadFail");
        String tip = message;
        try {
            log.info("开始下载文件!");
            ftpClient = client;
            ftpClient.setBufferSize(32 * 1024 * 1024);
            //切换FTP目录
            // 进行文件夹切换
            if (pathName.indexOf('/') > -1) {
                String[] paths = pathName.split(PATH_SPLIT);
                Arrays.stream(paths).filter(s -> !BootAppUtil.isNullOrEmpty(s))
                        .forEach(new Consumer<String>() {
                            @SneakyThrows
                            @Override
                            public void accept(String s) {
                                createDirecroty(s);
                                ftpClient.changeWorkingDirectory(s);
                            }
                        });
            }
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE); // 文件传输类型
            FTPFile[] ftpFiles = ftpClient.listFiles();
            Optional<FTPFile> any = Arrays.stream(ftpFiles).filter(ftpFile -> fileName.equalsIgnoreCase(ftpFile.getName())).findAny();
            if (any.isPresent()) {
                ftpClient.retrieveFile(any.get().getName(), os);
                os.close();
                flag = true;
            }
            ftpClient.logout();
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), tip);
            log.info("---error--"+e.getMessage(), e);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                    log.info("下载文件成功");
                    log.info("关闭下载文件流FTP客户端");
                } catch (IOException e) {
                    log.info("---error--"+e.getMessage(), e);
                }
            }
            if (null != os) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.info("---error--"+e.getMessage(), e);
                }
            }
        }
        return flag;
    }

    /**
     * @description 获取ftp文件流
     * <AUTHOR>
     * @date 2020/11/19 14:21
     */
    public InputStream getFtpFileStreamN(FTPClient client, String pathName, String fileName) {
        InputStream input = null;
        try {
            log.info("开始下载文件.");
            ftpClient = client;
            ftpClient.setBufferSize(32 * 1024 * 1024);
            //切换FTP目录
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE); // 文件传输类型
            input = ftpClient.retrieveFileStream(pathName + "/" + fileName);
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
        }
        return input;
    }

    /**
     * @description 获取ftp 文件byte[]
     * <AUTHOR>
     * @date 2020/11/26 14:21
     */
    public byte[] getFtpFileByte(FTPClient client, String allfileName) {
        byte[] resultArrayListByte = null;
        InputStream input = null;
        try {
            log.info("开始下载文件");
            ftpClient = client;
            ftpClient.setBufferSize(32 * 1024 * 1024);
            //切换FTP目录
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE); // 文件传输类型
            input = ftpClient.retrieveFileStream(allfileName);
            if (input != null) {
                resultArrayListByte = IOUtils.toByteArray(input);
            } else {
                log.info("下载文件不在FTP服务器上2[" + allfileName + "]");
            }
        } catch (IOException e) {
            log.info("---error--"+e.getMessage(), e);
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    if (input != null) {
                        ftpClient.completePendingCommand();
                    } else {
                        ftpClient.logout();
                    }
                    ftpClient.disconnect();
                    log.info("FTP客户端关闭");
                } catch (IOException e) {
                    log.info("---error--"+e.getMessage(), e);
                }
            }
            if (null != input) {
                try {
                    input.close();
                    log.info("FTP获得输入流关闭");
                } catch (IOException e) {
                    log.info("---error--"+e.getMessage(), e);
                }
            }

        }
        return resultArrayListByte;
    }


    /**
     * @description 获取FTP是否存在该文件
     * <AUTHOR>
     * @date 2020/11/19 14:21
     */
    public Boolean seacrhFileInFTP(FTPClient client, String pathName, String fileName) {
        Boolean result = false;
        try {
            log.info("开始查找文件");
            ftpClient = client;
            ftpClient.setBufferSize(32 * 1024 * 1024);
            //切换FTP目录
            // 进行文件夹切换
            if (pathName.indexOf('/') > -1) {
                String[] paths = pathName.split(PATH_SPLIT);
                for (String p : paths) {
                    if (!BootAppUtil.isNullOrEmpty(p)) {
                        createDirecroty(p);
                        ftpClient.changeWorkingDirectory(p);
                    }
                }
            }
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE); // 文件传输类型
            FTPFile[] ftpFiles = ftpClient.listFiles();
            for (FTPFile file : ftpFiles) {
                if (fileName.equalsIgnoreCase(file.getName())) {
                    result = true;
                    log.info("找到文件");
                    //找到文件后就跳出循环
                    break;
                }
            }
            ftpClient.logout();
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                    log.info("查找文件过程关闭");
                } catch (IOException e) {
                    log.info("---error--"+e.getMessage(), e);
                }
            }
        }
        return result;
    }


    /**
     * @description 获取ftp文件流
     * <AUTHOR>
     * @date 2020/7/15 14:21
     */
    public InputStream getFtpFileStream(FTPClient client, String pathName, String fileName) {
        InputStream input = null;
        ftpClient = client;
        try {
            log.info("开始下载文件");

            //切换FTP目录
            ftpClient.changeWorkingDirectory(pathName);
            ftpClient.setBufferSize(32 * 1024 * 1024);
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE); // 文件传输类型
            FTPFile[] ftpFiles = ftpClient.listFiles();
            for (FTPFile file : ftpFiles) {
                if (fileName.equalsIgnoreCase(file.getName())) {
                    input = ftpClient.retrieveFileStream(file.getName());
                }
            }
            ftpClient.logout();
            log.info("获取文件流成功");
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                    log.info("获取文件流FTP客户端关闭");
                } catch (IOException e) {
                    log.info("---error--"+e.getMessage(), e);
                }
            }
        }
        return input;
    }

    /**
     * 删除文件 *
     *
     * @param pathName FTP服务器保存目录 *
     * @param fileName 要删除的文件名称 *
     * @return
     */
    public boolean deleteFile(FTPClient client, String pathName, String fileName) {
        boolean flag = false;
        try {
            log.info("开始删除文件");
            ftpClient = client;
            //切换FTP目录
            ftpClient.changeWorkingDirectory(pathName);
            ftpClient.dele(fileName);
            ftpClient.logout();
            flag = true;
            log.info("删除文件成功");
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException e) {
                    log.info("---error--"+e.getMessage(), e);
                }
            }
        }
        return flag;
    }

}

package com.qm.tds.util;

import com.qm.tds.base.domain.LoginKeyDO;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.HashMap;
import java.util.Locale;

@Slf4j
@Component
public class I18nUtil {

    @Autowired
    private MessageSource messageSource;

    /**
     * 封装国际化显示信息
     * @param msgKey 映射id
     */
    public String getMessage(String msgKey){
        try {
            String message =messageSource.getMessage(msgKey, null, langMapping());
            return message;
        } catch (Exception ex) {
            log.info("---error--"+"多语言错误日志！" + ex.getMessage(), ex);
            return "Not Found "+ msgKey;
        }

    }

    /**
     * 封装国际化显示信息-带拼装参数
     * @param msgKey 映射id
     * @param params 拼装参数，可以为空
     */
    public String getMessage(String msgKey, String...  params){
        try {
            String message =messageSource.getMessage(msgKey, null, langMapping());
            message = String.format(message, params);
            return message;
        } catch (Exception ex) {
            log.info("---error--"+"多语言错误日志！" + ex.getMessage(), ex);
            return "Not Found "+ msgKey;
        }

    }
    /**
     * 封装国际化显示信息
     * @param msgKey 映射id
     * @param lang 语言标识  格式：Locale
     */
    public String getMessage(String msgKey,Locale lang){
        try {
            String message =messageSource.getMessage(msgKey, null, lang);
            return message;
        } catch (Exception ex) {
            log.info("---error--"+"多语言错误日志！" + ex.getMessage(), ex);
            return "Not Found "+ msgKey;
        }
    }

    /**
     * 封装国际化显示信息-带拼装参数
     * @param msgKey 映射id
     * @param params 拼装参数，可以为空
     * @param lang 语言标识  格式：Locale
     */
    public String getMessage(String msgKey, String[]  params,Locale lang){
        try {
            String message =messageSource.getMessage(msgKey, null, lang);
            message = String.format(message, params);
            return message;
        } catch (Exception ex) {
            log.info("---error--"+"多语言错误日志！" + ex.getMessage(), ex);
            return "Not Found "+ msgKey;
        }
    }


    /**
     * 语言标识对照翻译
     */
    public Locale langMapping(){

        HashMap<String,Locale> langMap = new HashMap();
        //key为系统 （languageCode） ，value 为标准国际化地区标识
        langMap.put("zh",Locale.SIMPLIFIED_CHINESE);//中文简体
        langMap.put("tw",Locale.TRADITIONAL_CHINESE);//中文繁体
        langMap.put("en",Locale.US);//英文
        langMap.put("ES",new Locale("es", "ES"));//西班牙文
        langMap.put("FR",Locale.FRANCE);//法文
        langMap.put("RU",new Locale("ru", "RU"));//俄文
        langMap.put("VT",new Locale("vi", "VI"));//越南文
        langMap.put("AR",new Locale("ar", "AR"));//阿语
        langMap.put("ID",new Locale("id", "ID"));//印度尼西亚语言

        //获取当前http上下文Request对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        RequestContextHolder.setRequestAttributes(attributes, true);
        //组装用户信息
        LoginKeyDO loginKey = new LoginKeyDO();
        String lang ="";
        if (null != attributes) {
            HttpServletRequest request = attributes.getRequest();
            lang = BootAppUtil.isNullOrEmpty(request.getHeader("lang")) ? "zh" : request.getHeader("lang");
        }

        //System.out.println("----languageCodeMapping--入参--languageCode--"+languageCode);

        Locale rep = Locale.forLanguageTag("");
        if(null != langMap.get(lang)){
            rep = langMap.get(lang);
        }else{
            rep = Locale.SIMPLIFIED_CHINESE;
        }
        return rep;
    }
}

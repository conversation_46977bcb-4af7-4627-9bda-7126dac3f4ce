package com.qm.tds.util;

import com.qm.tds.api.exception.QmException;
import org.w3c.dom.Element;
import com.qm.tds.api.util.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageTypeSpecifier;
import javax.imageio.ImageWriter;
import javax.imageio.metadata.IIOMetadata;
import javax.imageio.plugins.jpeg.JPEGImageWriteParam;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.font.GlyphVector;
import java.awt.image.BufferedImage;
import java.io.*;

/**
 * <AUTHOR>
 * @description 图片工具类 缩略图
 * @date 2020/11/17 13:36
 */
@SuppressWarnings("unused")
@Component
@Slf4j
public class ImageUtil {
    @Autowired
    private I18nUtil i18nUtil;
    private static final Boolean DEFAULT_FORCE = false;
    /**
     * 默认后缀名
     */
    public static final String SUFFIX_DEFAULT = "unkown";

    /**
     * 根据输入的图片文件生成缩略图
     *
     * @param imageFile 图片文件
     * @param fix       图片文件后缀
     * @param width     缩略图宽度
     * @param height    缩略图高度
     * @param force     是否强制按照宽高生成缩略图（如果为false，则生成最佳比例缩略图）
     */
    private Boolean thumbnailImage(File imageFile, String fix, int width, int height, boolean force, OutputStream os) {
        boolean result = false;
        try {
            if (imageFile.exists()) {
                Image inputImage = ImageIO.read(imageFile);
                String inputFix = fix.toLowerCase();
                if (!force) {
                    //根据原图与要求的缩略图比例，找到最合适的缩略图比例
                    int imageWidth = inputImage.getWidth(null);
                    int imageHeight = inputImage.getHeight(null);
                    if (((imageWidth * 0.1) / width) < ((imageHeight * 1.0) / height)) {
                        if (imageWidth > width) {
                            height = Integer.parseInt(new java.text.DecimalFormat("0").format(imageHeight * width / (imageWidth * 1.0)));
                        }
                        if (imageHeight > height) {
                            width = Integer.parseInt(new java.text.DecimalFormat("0").format(imageWidth * height / (imageHeight * 1.0)));
                        }
                    }
                }
                BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
                Graphics graphics = bufferedImage.getGraphics();
                graphics.drawImage(inputImage, 0, 0, width, height, Color.LIGHT_GRAY, null);
                graphics.dispose();
                //形成新的文件输出流
                ImageIO.write(bufferedImage, inputFix, os);
                result = true;
            }
        } catch (IOException e) {
            log.info("---error--"+e.getMessage(), e);
            String fileIOError = i18nUtil.getMessage("ERR.basecommon.COSUtils.fileioError");
            throw new QmException(fileIOError, e);
        }
        return result;
    }

    /**
     * 根据输入的图片文件生成缩略图
     *
     * @param inputStream 图片文件
     * @param fix         图片文件后缀
     * @param width       缩略图宽度
     * @param height      缩略图高度
     * @param force       是否强制按照宽高生成缩略图（如果为false，则生成最佳比例缩略图）
     */
    private Boolean thumbnailImage(InputStream inputStream, String fix, int width, int height, boolean force, OutputStream os) {
        boolean result = false;
        try {
            Image inputImage = ImageIO.read(inputStream);
            String inputFix = fix.toLowerCase();
            if (!force) {
                //根据原图与要求的缩略图比例，找到最合适的缩略图比例
                int imageWidth = inputImage.getWidth(null);
                int imageHeight = inputImage.getHeight(null);
                if (((imageWidth * 0.1) / width) < ((imageHeight * 1.0) / height)) {
                    if (imageWidth > width) {
                        height = Integer.parseInt(new java.text.DecimalFormat("0").format(imageHeight * width / (imageWidth * 1.0)));
                    } else {
                        if (imageHeight > height) {
                            width = Integer.parseInt(new java.text.DecimalFormat("0").format(imageWidth * height / (imageHeight * 1.0)));
                        }
                    }
                }
            }
            BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics graphics = bufferedImage.getGraphics();
            graphics.drawImage(inputImage, 0, 0, width, height, Color.LIGHT_GRAY, null);
            graphics.dispose();
            //形成新的文件输出流
            ImageIO.write(bufferedImage, inputFix, os);
            result = true;
        } catch (IOException e) {
            log.info("---error--"+e.getMessage(), e);
            String fileIOError = i18nUtil.getMessage("ERR.basecommon.COSUtils.fileioError");
            throw new QmException(fileIOError, e);
        }
        return result;
    }

    /**
     * 根据输入的图片文件生成缩略图
     *
     * @param inputStream 图片文件
     * @param fix         图片文件后缀
     * @param width       缩略图宽度
     * @param height      缩略图高度
     * @param force       是否强制按照宽高生成缩略图（如果为false，则生成最佳比例缩略图）
     */
    private InputStream thumbanailImage(InputStream inputStream, String fix, int width, int height, Boolean force) {
        byte[] data = thumbanailImageBytes(inputStream, fix, width, height, force);
        return new ByteArrayInputStream(data);
    }

    /**
     * 生成缩略图
     *
     * @param inputStream 图片文件
     * @param fix         图片文件后缀
     * @param width       缩略图宽度
     * @param height      缩略图高度
     * @param force       是否强制按照宽高生成缩略图（如果为false，则生成最佳比例缩略图）
     * @return 缩略图
     */
    public byte[] thumbanailImageBytes(InputStream inputStream, String fix, int width, int height, boolean force) {
        byte[] result;
        try {
            Image inputImage = ImageIO.read(inputStream);
            String inputFix = fix.toLowerCase();
            if (!force) {
                //根据原图与要求的缩略图比例，找到最合适的缩略图比例
                int imageWidth = inputImage.getWidth(null);
                int imageHeight = inputImage.getHeight(null);
                if (((imageWidth * 0.1) / width) < ((imageHeight * 1.0) / height)) {
                    if (imageWidth > width) {
                        height = Integer.parseInt(new java.text.DecimalFormat("0").format(imageHeight * width / (imageWidth * 1.0)));
                    } else {
                        if (imageHeight > height) {
                            width = Integer.parseInt(new java.text.DecimalFormat("0").format(imageWidth * height / (imageHeight * 1.0)));
                        }
                    }
                }
            }
            BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics graphics = bufferedImage.getGraphics();
            graphics.drawImage(inputImage, 0, 0, width, height, Color.LIGHT_GRAY, null);
            graphics.dispose();

            //修改图片写入方式，为JPEGImageEncoder，原因ImageIO不支持jfif格式----20211230-
            ByteArrayOutputStream bs = new ByteArrayOutputStream();
            if(!"jfif".equals(inputFix) && !"JFIF".equals(inputFix)){//图片格式为其他
                //形成新的文件输输入流
                ImageOutputStream imOut;
                imOut = ImageIO.createImageOutputStream(bs);
                ImageIO.write(bufferedImage, inputFix, imOut);
            }else{//图片格式为jfif
                //JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(bs);
                //encoder.encode(bufferedImage);

                //FileOutputStream fos = new FileOutputStream("123.jfif"); //输出到文件流
                //新的方法
                saveAsJPEG(100, bufferedImage, (float)1, bs);
            }
            result = bs.toByteArray();
        } catch (IOException e) {
            log.info("---error--"+e.getMessage(), e);
            String fileIOError = i18nUtil.getMessage("ERR.basecommon.COSUtils.fileioError");
            throw new QmException(fileIOError, e);
        }
        return result;
    }

    public Boolean thumbnailImage(String imagePath, String fix, int width, int height, Boolean force, OutputStream os) {
        File imageFile = new File(imagePath);
        return thumbnailImage(imageFile, fix, width, height, force, os);
    }

    public Boolean thumbnailImage(String imagePath, String fix, int width, int height, OutputStream os) {
        File imageFile = new File(imagePath);
        return thumbnailImage(imageFile, fix, width, height, DEFAULT_FORCE, os);
    }

    public Boolean thumbnailImage(File imageFile, String fix, int width, int height, OutputStream os) {
        return thumbnailImage(imageFile, fix, width, height, DEFAULT_FORCE, os);
    }

    public Boolean thumbnailImage(InputStream inputStream, String fix, int width, int height, OutputStream os) {
        return thumbnailImage(inputStream, fix, width, height, DEFAULT_FORCE, os);
    }

    public InputStream thumbanailImage(InputStream inputStream, String fix, int width, int height) {
        return thumbanailImage(inputStream, fix, width, height, DEFAULT_FORCE);
    }

    /**
     * 获取文件后缀名
     *
     * @param filename 文件全限定名
     * @return 文件后缀名
     */
    public static String getSuffix(String filename) {
        String s = StringUtils.trimAllWhitespace(filename);
        String[] dotSplit = s.split("\\.");
        if (dotSplit.length <= 1) {
            log.warn("文件名[{}]格式不正确，使用默认后缀名[{}]", filename, SUFFIX_DEFAULT);
            return SUFFIX_DEFAULT;
        }
        return dotSplit[dotSplit.length - 1];
    }

    /**
     * 等比放大缩小图片
     *
     * @param source 图片文件
     * @param zoom   缩放比例   * 放大 > 1    * 缩小 < 1
     * @return 缩放后的图片文件
     */
    public static BufferedImage zoomImage(BufferedImage source, float zoom) {
        int targetWidth = (int) (source.getWidth(null) * zoom);
        int heightWidth = (int) (source.getHeight(null) * zoom);
        int minX = (int) (source.getMinX() * zoom);
        int minY = (int) (source.getMinY() * zoom);
        log.info("[-ImageUtil-].zoomImage:target={},{}", targetWidth, heightWidth);
        BufferedImage targetImage = new BufferedImage(targetWidth, heightWidth, source.getType());
        Graphics graphics = targetImage.getGraphics();
        graphics.drawImage(source, minX, minY, targetWidth, heightWidth, null);
        graphics.dispose();
        return targetImage;
    }

    /**
     * 更改图片大小
     *
     * @param source    图片来源
     * @param sizeLimit 图片最大边长限制
     * @param format    原图片格式
     * @return 修改后的图片
     */
    public static byte[] compressImg(byte[] source, int sizeLimit, String format) {
        BufferedImage sourceImage;
        try {
            // ByteArrayInputStream属于内存流，无需关闭刷新等操作
            sourceImage = ImageIO.read(new ByteArrayInputStream(source));
            int sourceHeight = sourceImage.getHeight();
            int sourceWidth = sourceImage.getWidth();
            if (sourceHeight < sizeLimit && sourceWidth < sizeLimit) {
                return source;
            }
            //计算缩放比例
            float zoom = (float) sizeLimit / Math.max(sourceHeight, sourceWidth);
            BufferedImage targetImage = ImageUtil.zoomImage(sourceImage, zoom);
            ByteArrayOutputStream tempStream = new ByteArrayOutputStream();
            ImageIO.write(targetImage, format, tempStream);
            return tempStream.toByteArray();
        } catch (IOException e) {
            log.info("---error--"+"[-UploadFileServiceImpl-].updateImg", e);
            I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
            String message = i18nUtil.getMessage("ERR.basecommon.ImageUtil.imgConvertExecption");
            throw new QmException(message);
        }
    }


    public static byte[] getThumbnail(String suffix, int width, int height) {
        BufferedImage image = new BufferedImage(200, 200, BufferedImage.TYPE_INT_RGB);
        Graphics2D graphics = (Graphics2D) image.getGraphics();
        Font font = new Font("微软雅黑", Font.PLAIN, 60);
        graphics.setFont(font);
        graphics.setBackground(Color.decode("#EFEFEF"));
        graphics.clearRect(0, 0, 200, 200);

        suffix = suffix.toUpperCase();


        GlyphVector v = graphics.getFont().createGlyphVector(graphics.getFontMetrics().getFontRenderContext(), suffix);
        Shape shape = v.getOutline();
        Rectangle bounds = shape.getBounds();
        int x = (image.getWidth() - bounds.width) / 2 - bounds.x;
        int y = (image.getHeight() - bounds.height) / 2 - bounds.y;


        graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);//设置抗锯齿
        graphics.setPaint(new Color(0, 0, 0, 64));//阴影颜色
        graphics.drawString(suffix, x, y);//先绘制阴影
        graphics.setPaint(Color.LIGHT_GRAY);//正文颜色
        graphics.drawString(suffix, x, y);//用正文颜色覆盖上去


        // 防止图片变色
        BufferedImage image2 = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_BGR);
        Graphics g = image2.getGraphics();
        g.drawImage(image, 0, 0, null);
        g.dispose();
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            ImageIO.write(image2, "jpg", outputStream);
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();

        }
        return null;
    }

    /**
     * 以JPEG编码保存图片
     * @param dpi  分辨率
     * @param image_to_save  要处理的图像图片
     * @param JPEGcompression  压缩比
     * @param fos 文件输出流
     * @throws IOException
     */
    public static void saveAsJPEG(Integer dpi ,BufferedImage image_to_save, float JPEGcompression, ByteArrayOutputStream fos) throws IOException {

        //useful documentation at http://docs.oracle.com/javase/7/docs/api/javax/imageio/metadata/doc-files/jpeg_metadata.html
        //useful example program at http://johnbokma.com/java/obtaining-image-metadata.html to output JPEG data

        //old jpeg class
        //com.sun.image.codec.jpeg.JPEGImageEncoder jpegEncoder  =  com.sun.image.codec.jpeg.JPEGCodec.createJPEGEncoder(fos);
        //com.sun.image.codec.jpeg.JPEGEncodeParam jpegEncodeParam  =  jpegEncoder.getDefaultJPEGEncodeParam(image_to_save);

        // Image writer
//		  JPEGImageWriter imageWriter = (JPEGImageWriter) ImageIO.getImageWritersBySuffix("jpeg").next();
        ImageWriter imageWriter  =   ImageIO.getImageWritersBySuffix("jpg").next();
        ImageOutputStream ios  =  ImageIO.createImageOutputStream(fos);
        imageWriter.setOutput(ios);
        //and metadata
        IIOMetadata imageMetaData  =  imageWriter.getDefaultImageMetadata(new ImageTypeSpecifier(image_to_save), null);


        if(dpi !=  null && !dpi.equals("")){

            //old metadata
            //jpegEncodeParam.setDensityUnit(com.sun.image.codec.jpeg.JPEGEncodeParam.DENSITY_UNIT_DOTS_INCH);
            //jpegEncodeParam.setXDensity(dpi);
            //jpegEncodeParam.setYDensity(dpi);

            //new metadata
            Element tree  =  (Element) imageMetaData.getAsTree("javax_imageio_jpeg_image_1.0");
            Element jfif  =  (Element)tree.getElementsByTagName("app0JFIF").item(0);
            jfif.setAttribute("Xdensity", Integer.toString(dpi) );
            jfif.setAttribute("Ydensity", Integer.toString(dpi));

        }


        if(JPEGcompression >= 0 && JPEGcompression <= 1f){

            //old compression
            //jpegEncodeParam.setQuality(JPEGcompression,false);

            // new Compression
            JPEGImageWriteParam jpegParams  =  (JPEGImageWriteParam) imageWriter.getDefaultWriteParam();
            jpegParams.setCompressionMode(JPEGImageWriteParam.MODE_EXPLICIT);
            jpegParams.setCompressionQuality(JPEGcompression);

        }

        //old write and clean
        //jpegEncoder.encode(image_to_save, jpegEncodeParam);

        //new Write and clean up
        imageWriter.write(imageMetaData, new IIOImage(image_to_save, null, null), null);
        ios.close();
        imageWriter.dispose();

    }
}

package com.qm.tds.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
public class JSONUtils {

    private JSONUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static String beanToJson(Object object, String dataFormatString) {
        if (object != null) {
            if (StringUtils.isNoneEmpty(dataFormatString)) {
                return JSONObject.toJSONString(object);
            }
            return JSON.toJSONStringWithDateFormat(object, dataFormatString);
        } else {
            return null;
        }
    }

    /**
     * Bean对象转JSON
     *
     * @param object
     * @return
     */
    public static String beanToJson(Object object) {
        if (object != null) {
            return JSON.toJSONString(object);
        } else {
            return "{}";
        }
    }

    /**
     * String转JSON字符串
     *
     * @param key
     * @param value
     * @return
     */
    public static String stringToJsonByFastjson(String key, String value) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(value)) {
            return null;
        }
        Map<String, String> map = new HashMap<>(16);
        map.put(key, value);
        return beanToJson(map, null);
    }

    /**
     * 根据json字符串组装domain object
     *
     * @param inParaJsonStr Json字符串
     * @param clazz         Java Object Class
     * @return domain object
     */
    public static <T> T packingDOFromJsonStr(String inParaJsonStr, Class<T> clazz) {
        JSONObject jsonobject = JSONObject.parseObject(inParaJsonStr);
        T retObj = JSONObject.toJavaObject(jsonobject, clazz);
        if (retObj == null) {
            try {
                retObj = clazz.newInstance();
            } catch (Exception ex) {
                log.info("---error--"+"[" + clazz.getName() + "]参数实例化失败！", ex);
            }
        }
        return retObj;
    }

    /**
     * 根据Json字符串组装Object List
     *
     * @param inParaJsonStr Json字符串
     * @param clazz         Java Object Class
     * @param <T>           泛型对象
     * @return Object List
     */
    public static <T> List<T> packingDOListFromJsonStr(String inParaJsonStr, Class<T> clazz) {
        List<T> tmpList = null;
        try {
            if (!BootAppUtil.isNullOrEmpty(inParaJsonStr)) {
                tmpList = JSONObject.parseArray(inParaJsonStr, clazz);
            } else {
                tmpList = new ArrayList<>();
            }
        } catch (Exception ex) {
            tmpList = new ArrayList<>();
            log.info("---error--"+"packingDOListFromJsonStr转换失败！", ex);
        }
        return tmpList;
    }

    /**
     * json字符串转map
     *
     * @param json
     * @return
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> jsonToMap(String json) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        return JSON.parseObject(json, Map.class);
    }

    /**
     * 将异常信息组装到Json兑现各种
     *
     * @param e                异常信息
     * @param request          当前http request请求对象
     * @param microServiceName 子服务名
     * @return 包含异常信息的json对象
     */
    public static JSONObject getErrorLogJSONObject(Exception e, HttpServletRequest request, String microServiceName) {
        InetAddress inetAddress = null;
        try {
            inetAddress = InetAddress.getLocalHost();
        } catch (UnknownHostException ex) {
            log.info("---error--"+"JSONUtils类getErrorLogJSONObject方法 ==获取服务器IP地址异常==>" + ex.toString(), ex);
        }

        String vRequestUrl = request.getRequestURL().toString();
        String vQueryString = request.getQueryString();
        String vRequestMode = request.getParameter("model");
        String vParameters = request.getParameter("inParaJsonStr");

        JSONObject errorLogJson = new JSONObject();
        errorLogJson.put("vModel", vRequestMode);
        errorLogJson.put("vRequestUrl", vRequestUrl + "?" + vQueryString);
        errorLogJson.put("vParameters", vParameters);
        errorLogJson.put("vServerIp", inetAddress == null ? "" : inetAddress.toString());
        errorLogJson.put("vServerName", microServiceName);
        StringBuilder errorOutString = new StringBuilder();
        errorOutString.append(e.getMessage() + "\r\n");
        StackTraceElement[] trace = e.getStackTrace();
        for (StackTraceElement s : trace) {
            errorOutString.append("\tat " + s + "\r\n");
        }

        errorLogJson.put("vException", errorOutString.toString());
        return errorLogJson;
    }
}

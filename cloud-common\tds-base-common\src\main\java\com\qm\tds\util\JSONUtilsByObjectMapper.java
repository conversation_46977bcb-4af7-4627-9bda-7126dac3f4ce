package com.qm.tds.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/*
 * JSON字符串直接转成JSON对象，包括JSON字符串中多层递归转化为JSON对象list中多层嵌套
 * ly
 * 2020-12-2
 * */
@Slf4j
public class JSONUtilsByObjectMapper {
    private JSONUtilsByObjectMapper() {
        throw new IllegalStateException("Utility class");
    }

    // 定义jackson对象
    private static final ObjectMapper MAPPER = new ObjectMapper();

    /**
     * 将对象转换成json字符串。
     * <p>Title: pojoToJson</p>
     * <p>Description: </p>
     *
     * @param data
     * @return
     */
    public static String objectToJson(Object data) {
        try {
            String string = MAPPER.writeValueAsString(data);
            return string;
        } catch (JsonProcessingException e) {
            log.info("---error--"+"objectToJson异常！" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 将json结果集转化为对象
     *
     * @param jsonData json数据
     * @param beanType 对象中的object类型
     * @return
     */
    public static <T> T jsonToPojo(String jsonData, Class<T> beanType) {
        try {
            T t = MAPPER.readValue(jsonData, beanType);
            return t;
        } catch (Exception e) {
            log.info("---error--"+"jsonToPojo异常！" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 将json数据转换成pojo对象list
     * <p>Title: jsonToList</p>
     * <p>Description: </p>
     *
     * @param jsonData
     * @param beanType
     * @return
     */
    public static <T> List<T> jsonToList(String jsonData, Class<T> beanType) {
        List<T> list = null;
        JavaType javaType = MAPPER.getTypeFactory().constructParametricType(List.class, beanType);
        try {
            list = MAPPER.readValue(jsonData, javaType);
        } catch (Exception e) {
            log.info("---error--"+"jsonToList异常！" + e.getMessage(), e);
        }
        return list;
    }
}

package com.qm.tds.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.DigestUtils;

/**
 * <AUTHOR>
 * @Title: MD5Utils
 * @ProjectName cloud-common
 * @Description: MD5基础Util类
 * @date 2019/1/2 8:23
 */
@Slf4j
public class MD5Utils {

    private MD5Utils() {
        throw new IllegalStateException("Utility class");
    }

    public static String encrypt(String str) {
        return DigestUtils.md5DigestAsHex(str.getBytes());
    }

    /**
     * 密钥
     */
    private static String char1 = "`1234567890-=~!@#$%^&*()_+qwertyuiop[]\\QWERTYUIOP{}|asdfghjkl;ASDFGHJKL:zxcvbnm,./ZXCVBNM<>? '\"";

    /**
     * TDS CRM密码加密
     *
     * @param password 原始密码
     * @return 加密后密码
     */
    public static String encryptByTdsAndCrm(String password) {
        try {
            if (BootAppUtil.isNullOrEmpty(password)) {
                return "";
            }
            int passwdlen;
            int j;
            int k;
            String[] chars = new String[95];
            StringBuilder newpass = new StringBuilder();
            passwdlen = password.trim().length();
            String[] newpasswd = new String[passwdlen];
            for (int i = 0; i < char1.length() - 1; i++) {
                chars[i] = char1.substring(i, i + 1);
            }

            for (int i = 0; i <= passwdlen - 1; i++) {
                newpasswd[i] = password.substring(i, i + 1);
                for (j = 0; j <= 94; j++) {
                    if (newpasswd[i].equals(chars[j])) {
                        break;
                    }
                }
                k = j + i + 1;
                if (k > 94) {
                    k = k - 95;
                }
                newpasswd[i] = chars[k];
                newpass.append(newpasswd[i]);
            }
            return newpass.toString();
        } catch (Exception e) {
            log.info("---error--"+"encryptByTdsAndCrm异常！", e);
        }
        return null;
    }
}

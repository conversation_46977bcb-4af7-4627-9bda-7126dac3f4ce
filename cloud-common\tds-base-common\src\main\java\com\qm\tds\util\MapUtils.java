package com.qm.tds.util;

/**
 * <p>MapUtils</p>
 * <p>
 * 地图坐标
 * </p>
 *
 * <AUTHOR> wjq
 * @date 2021/11/4
 */
public class MapUtils {
    private MapUtils() {
    }

    /**
     * 地球平均半径,单位：m；不是赤道半径。赤道为6378左右
     */
    private static final double EARTH_RADIUS = 6371000;

    /**
     * @param lat1 坐标1维度
     * @param lng1 坐标1经度
     * @param lat2 坐标2维度
     * @param lng2 坐标2经度
     * @return 坐标1和坐标2之间的球面距离
     */
    public static double getDistance(double lat1, double lng1, double lat2, double lng2) {
        // 经纬度（角度）转弧度。弧度用作参数，以调用Math.cos和Math.sin
        double radiansAX = Math.toRadians(lng1); // A经弧度
        double radiansAY = Math.toRadians(lat1); // A纬弧度
        double radiansBX = Math.toRadians(lng2); // B经弧度
        double radiansBY = Math.toRadians(lat2); // B纬弧度

        // 公式中“cosβ1cosβ2cos（α1-α2）+sinβ1sinβ2”的部分，得到∠AOB的cos值
        double cos = Math.cos(radiansAY) * Math.cos(radiansBY) * Math.cos(radiansAX - radiansBX) + Math.sin(radiansAY) * Math.sin(radiansBY);
        double acos = Math.acos(cos); // 反余弦值
        // System.out.println("∠AOB = " + Math.toDegrees(acos)); // 球心角 值域[0,180]
        return Math.round(EARTH_RADIUS * acos); // 最终结果
    }
}

package com.qm.tds.util;

import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.util.SpringContextHolder;
import org.apache.commons.lang3.RandomStringUtils;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Random;
import java.util.UUID;

/**
 * 随机工具类
 */
public class RandomUtils {

    private RandomUtils() {
    }

    private static Random random;

    /**
     * 获取随机类
     *
     * @return 随机类
     */
    public static Random getRandom() {
        if (random == null) {
            try {
                random = SecureRandom.getInstanceStrong();
            } catch (NoSuchAlgorithmException ex) {
                I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
                String message = i18nUtil.getMessage("ERR.basecommon.RandomUtils.generateRandomQueueError");
                throw new QmException(message, ex);
            }
        }
        return random;
    }

    /**
     * 大写字母集合。去除可能会被误会的字母。
     */
    public static final String CHAR_UPPER = "ABCDEFGHJKLMNPQRSTUVWXYZ";
    /**
     * 所有大写字母集合。
     */
    public static final String CHAR_UPPER_ALL = "ABCDEFGHJKLMNOPQRSTUVWXYZ";
    /**
     * 小写字母集合。去除可能会被误会的字母。
     */
    public static final String CHAR_LOWER = "abcdefghijkmnpqrstuvwxyz";
    /**
     * 所有小写字母集合。
     */
    public static final String CHAR_LOWER_ALL = "abcdefghijklmnopqrstuvwxyz";
    /**
     * 数字集合。去除可能会被误会的字母。
     */
    public static final String CHAR_DECIMAL = "23456789";
    /**
     * 所有数字集合。
     */
    public static final String CHAR_DECIMAL_ALL = "1234567890";
    /**
     * 符号集合。去除可能会被误会的符号。
     */
    public static final String CHAR_SYMBOL = "!@#$%^&*()_-+=,.<>[]{}?";

    /**
     * 从字符串中获取随机的几个字母。
     *
     * @param str 原始字符串
     * @param num 字母个数
     * @return 随机的字母
     */
    private static String getRandomStr(String str, int num) {
        int length = str.length();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < num; i++) {
            sb.append(str.charAt(getRandom().nextInt(length - 1)));
        }
        return sb.toString();
    }

    /**
     * 将数组顺序打乱
     *
     * @param arr 数组
     * @param <T> 数组元素
     */
    public static <T> void shuffle(T[] arr) {
        int length = arr.length;
        for (int i = length; i > 0; i--) {
            int randInd = getRandom().nextInt(i);
            T temp = arr[randInd];
            arr[randInd] = arr[i - 1];
            arr[i - 1] = temp;
        }
    }

    /**
     * 将数组顺序打乱
     *
     * @param arr 数组
     */
    private static void shuffle(char[] arr) {
        int length = arr.length;
        for (int i = length; i > 0; i--) {
            int randInd = getRandom().nextInt(i);
            char temp = arr[randInd];
            arr[randInd] = arr[i - 1];
            arr[i - 1] = temp;
        }
    }

    /**
     * 将一个字符串打乱顺序
     *
     * @param str 原始字符串
     * @return 打乱后的字符串
     */
    public static String shuffle(String str) {
        char[] tmp = str.toCharArray();
        shuffle(tmp);
        return new String(tmp);
    }

    /**
     * 生成随机字符串。
     * 例如生成包含大写字母、小写字母、数字的6位随机字符串示例如下：
     * <code>
     * RandomUtils.getRandomStr(6, RandomUtils.CHAR_UPPER, RandomUtils.CHAR_LOWER, RandomUtils.CHAR_DECIMAL);
     * </code>
     *
     * @param length  随机字符串长度。
     * @param strList 字符串源。如果为null，则使用 RandomUtils.CHAR_UPPER_ALL, RandomUtils.CHAR_LOWER_ALL, RandomUtils.CHAR_DECIMAL_ALL 的合计。
     * @return 随机字符串。每个字符串源中至少会有一个。
     */
    public static String getRandomStr(int length, String... strList) {
        Random r = getRandom();
        StringBuilder sb = new StringBuilder();
        // 如果入参为空，则使用 RandomUtils.CHAR_UPPER_ALL, RandomUtils.CHAR_LOWER_ALL, RandomUtils.CHAR_DECIMAL_ALL 的合计。
        if (strList == null || strList.length == 0) {
            strList = new String[1];
            strList[0] = RandomUtils.CHAR_UPPER_ALL + RandomUtils.CHAR_LOWER_ALL + RandomUtils.CHAR_DECIMAL_ALL;
        }
        // 生成随机字符串。每个字符串源中至少会有一个。
        for (int i = 0; i < strList.length; i++) {
            if (i == strList.length - 1) {
                // 最后一组补全剩下的字母，不需要随机获取个数了。
                sb.append(getRandomStr(strList[i], length - sb.length()));
            } else {
                // (strList.length - i) 是为了确保每个字符串源至少都会有
                sb.append(getRandomStr(strList[i], r.nextInt(length - sb.length() - (strList.length - i)) + 1));
            }
        }
        return shuffle(sb.toString());
    }

    /**
     * 生成随机中文字
     *
     * @param length 随机字符串长度
     * @return 随机字符串
     */
    public static String getRandomChinese(int length) {
        /**
         * 4E00是汉字在Unicode编码的起始位置；
         * 9FA5是汉字在Unicode编码的终止位置；
         */
        return RandomStringUtils.random(length, 0x4e00, 0x9fa5, false, false);
    }

    /**
     * 生成随机ID
     *
     * @return 随机ID
     */
    public static String getRandomID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成随机手机号
     *
     * @return 随机手机号
     */
    public static String getRandomMobile() {
        return "13" + getRandomStr(9, CHAR_DECIMAL_ALL);
    }
}

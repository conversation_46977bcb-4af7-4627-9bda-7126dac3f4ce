package com.qm.tds.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RedisUtils {

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 分割字符，默认[:]，使用:可用于rdm分组查看
     */
    private static final String KEY_SPLIT_CHAR = ":";

    /**
     * redis的key键规则定义
     *
     * @param module 模块名称
     * @param func   方法名称
     * @param args   参数..
     * @return key
     */
    public String keyBuilder(String module, String func, String... args) {
        StringBuilder key = new StringBuilder();
        // KEY_SPLIT_CHAR 为分割字符
        key.append(module).append(KEY_SPLIT_CHAR).append(func);
        if (null != args) {
            for (String arg : args) {
                key.append(KEY_SPLIT_CHAR).append(arg);
            }
        }
        return key.toString();
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public Boolean expire(final String key, final long timeout) {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return true=设置成功；false=设置失败
     */
    @SuppressWarnings("unchecked")
    public Boolean expire(final String key, final long timeout, final TimeUnit unit) {
        return redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取以key为前缀的Key列表
     *
     * @param key Key的前缀
     * @return 以key为前缀的Key列表
     */
    public Set<String> getKeys(String key) {
        return redisTemplate.keys(key + "*");
    }

    /**
     * 删除单个key
     *
     * @param key 键
     * @return true=删除成功；false=删除失败
     */
    @SuppressWarnings("unchecked")
    public Boolean del(final String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 删除多个key
     *
     * @param keys 键集合
     * @return 成功删除的个数
     */
    @SuppressWarnings("unchecked")
    public Long del(final Collection<String> keys) {
        return redisTemplate.delete(keys);
    }

    /**
     * 按Key的前缀删除一组Key
     *
     * @param keyPrefix Key的前缀
     * @return 成功删除的个数
     */
    public Long delMutil(final String keyPrefix) {
        String key = keyPrefix + KEY_SPLIT_CHAR + "*";
        Set<String> keyList = getKeys(key);
        return redisTemplate.delete(keyList);
    }

    /**
     * 存入普通对象
     *
     * @param key   Redis键
     * @param value 值
     */
    @SuppressWarnings("unchecked")
    public void set(final String key, final Object value) {
        try {
            // 默认设置90天有效期，防止数据存储时间太长导致Redis中的无用数据太多，拖累Redis性能。
            redisTemplate.opsForValue().set(key, value, 90, TimeUnit.DAYS);
        } catch (RedisConnectionFailureException ex) {
            // 当redis连接异常时，可以继续后续业务逻辑而不是被异常中断。
            log.info("---error--"+"保存[" + key + "][" + value + "]失败！", ex);
        }
    }

    // 存储普通对象操作

    /**
     * 存入普通对象
     *
     * @param key     键
     * @param value   值
     * @param timeout 有效期，单位秒
     */
    @SuppressWarnings("unchecked")
    public void set(final String key, final Object value, final long timeout) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
        } catch (RedisConnectionFailureException ex) {
            // 当redis连接异常时，可以继续后续业务逻辑而不是被异常中断。
            log.info("---error--"+"保存[" + key + "][" + value + "][" + timeout + "]失败！", ex);
        }
    }

    /**
     * 获取普通对象
     *
     * @param key 键
     * @return 对象
     */
    public Object get(final String key) {
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (RedisConnectionFailureException ex) {
            // 失败之后返回null，以便当redis连接异常时，可以继续后续业务逻辑而不是被异常中断。
            log.info("---error--"+"获取[" + key + "]失败！", ex);
            return null;
        }
    }

    // 存储Hash操作

    /**
     * 往Hash中存入数据
     *
     * @param key   Redis键
     * @param hKey  Hash键
     * @param value 值
     */
    @SuppressWarnings("unchecked")
    public void hPut(final String key, final String hKey, final Object value) {
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * 往Hash中存入多个数据
     *
     * @param key    Redis键
     * @param values Hash键值对
     */
    @SuppressWarnings("unchecked")
    public void hPutAll(final String key, final Map<String, Object> values) {
        redisTemplate.opsForHash().putAll(key, values);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    @SuppressWarnings("unchecked")
    public Object hGet(final String key, final String hKey) {
        return redisTemplate.opsForHash().get(key, hKey);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    @SuppressWarnings("unchecked")
    public List<Object> hMultiGet(final String key, final Collection<Object> hKeys) {
        return redisTemplate.opsForHash().multiGet(key, hKeys);
    }

    // 存储Set相关操作

    /**
     * 往Set中存入数据
     *
     * @param key    Redis键
     * @param values 值
     * @return 存入的个数
     */
    @SuppressWarnings("unchecked")
    public long sSet(final String key, final Object... values) {
        Long count = redisTemplate.opsForSet().add(key, values);
        return count == null ? 0 : count;
    }

    /**
     * 删除Set中的数据
     *
     * @param key    Redis键
     * @param values 值
     * @return 移除的个数
     */
    @SuppressWarnings("unchecked")
    public long sDel(final String key, final Object... values) {
        Long count = redisTemplate.opsForSet().remove(key, values);
        return count == null ? 0 : count;
    }

    /**
     * 往List中存入数据
     *
     * @param key   Redis键
     * @param value 数据
     * @return 存入的个数
     */
    @SuppressWarnings("unchecked")
    public long lPush(final String key, final Object value) {
        Long count = redisTemplate.opsForList().rightPush(key, value);
        return count == null ? 0 : count;
    }

    /**
     * 往List中存入多个数据
     *
     * @param key    Redis键
     * @param values 多个数据
     * @return 存入的个数
     */
    @SuppressWarnings("unchecked")
    public long lPushAll(final String key, final Collection<Object> values) {
        Long count = redisTemplate.opsForList().rightPushAll(key, values);
        return count == null ? 0 : count;
    }

    /**
     * 往List中存入多个数据
     *
     * @param key    Redis键
     * @param values 多个数据
     * @return 存入的个数
     */
    @SuppressWarnings("unchecked")
    public long lPushAll(final String key, final Object... values) {
        Long count = redisTemplate.opsForList().rightPushAll(key, values);
        return count == null ? 0 : count;
    }

    /**
     * 从List中获取begin到end之间的元素
     *
     * @param key   Redis键
     * @param start 开始位置
     * @param end   结束位置（start=0，end=-1表示获取全部元素）
     * @return List对象
     */
    @SuppressWarnings("unchecked")
    public List<Object> lGet(final String key, final int start, final int end) {
        return redisTemplate.opsForList().range(key, start, end);
    }

    /**
     * 清空缓存
     */
    public void clearCache(String key) {
        //清楚缓存
        List<Object> objects = lGet(key, 0, -1);
        if (null != objects && !objects.isEmpty()) {
            List<String> keys = objects.stream().map(Object::toString).collect(Collectors.toList());
            del(keys);
        }
    }

}

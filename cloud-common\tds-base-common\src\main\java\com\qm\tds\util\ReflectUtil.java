package com.qm.tds.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ExceptionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.util.SpringContextHolder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.Objects;

/**
 * 反射工具类
 *
 * <AUTHOR>
 * @date 2020/5/28 9:26
 */
@Slf4j
public class ReflectUtil {

    private ReflectUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 主键
     */
    private static final String ID = "id";
    /**
     * 时间戳
     */
    private static final String DTSTAMP = "dtstamp";

    /**
     * 判断类中是否存在某个属性
     *
     * @param entity 类
     * @param key    属性
     * @return boolean
     * <AUTHOR>
     * @date 2020/5/28 9:32
     */
    public static boolean containsKey(Object entity, String key) {
        Object o = JSON.toJSON(entity);
        JSONObject jsonObj = new JSONObject();
        if (o instanceof JSONObject) {
            jsonObj = (JSONObject) o;
        }
        return jsonObj.containsKey(key);
    }

    /**
     * 获取属性值
     *
     * @param entity 类型
     * @param name   属性名字
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2020/5/28 9:39
     */
    public static Object getValueByName(Object entity, String name) {
        try {
            if (entity instanceof Map) {
                return ((Map<?, ?>) entity).get(name);
            } else {
                Class<?> cls = entity.getClass();
                Field field = cls.getDeclaredField(name);
                field.setAccessible(true);
                return field.get(entity);
            }
        } catch (NoSuchFieldException e) {
            I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
            String message = i18nUtil.getMessage("ERR.basecommon.ReflectUtil.nonexistProperty");
            log.info("---error--"+message + e.getMessage(), e);
            throw new QmException(message + e.getMessage());
        } catch (IllegalAccessException e) {
            I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
            String message = i18nUtil.getMessage("ERR.basecommon.ReflectUtil.getPropertyException");
            log.info("---error--"+message + e.getMessage(), e);
            throw new QmException(message + e.getMessage());
        }
    }

    /**
     * 根据类中主键、时间戳属性进行逻辑处理
     * 主键存在时间戳不存在返回false 主键不存或者为null,空字符串 在返回true
     *
     * @param entity 类
     * @return boolean
     * <AUTHOR>
     * @date 2020/5/28 9:48
     */
    public static boolean judgeIdAndTimestamp(Object entity) {
        boolean flagId = containsKey(entity, ID);
        //id 存在
        if (flagId) {
            Object valId = getValueByName(entity, ID);
            //id是否为空
            if (!StringUtils.checkValNull(valId) && !Objects.isNull(valId)) {
                //时间戳存在并且不为空
                boolean bFlag = false;
                if (containsKey(entity, DTSTAMP) && !Objects.isNull(getValueByName(entity, DTSTAMP))) {
                    bFlag = true;
                }
                return bFlag;
            }
        }
        return true;
    }

    /**
     * 根据类中时间戳属性进行逻辑处理
     *
     * @param entity 类
     * @return boolean
     * <AUTHOR>
     * @date 2020/5/28 12:48
     */
    public static boolean judgeTimestamp(Object entity) {
        //时间戳存在并且不为空
        if (containsKey(entity, DTSTAMP) && !Objects.isNull(getValueByName(entity, DTSTAMP))) {
            return true;
        }
        I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
        String message = i18nUtil.getMessage("ERR.basecommon.ReflectUtil.timestampNull");
        throw new QmException(message);
    }

    /**
     * 从属性的ApiModelProperty中获取描述信息
     *
     * @param newCls 属性所属的Class
     * @param filed  属性
     * @return 属性的描述信息
     */
    public static String getFieldLabel(Class<?> newCls, Field filed) {
        String label = null;
        try {
            Field f = newCls.getDeclaredField(filed.getName());
            // 判断不等于空
            Schema annotation = f.getDeclaredAnnotation(Schema.class);
            if (!BootAppUtil.isNullOrEmpty(annotation)) {
                label = annotation.description();

                // 如果value没有取到值，则再从name获取属性名称
                if (BootAppUtil.isNullOrEmpty(label)) {
                    label = annotation.name();
                }
            }
            // 如果ApiModelProperty属性中没有取到值，则使用字段名称
            if (BootAppUtil.isNullOrEmpty(label)) {
                label = f.getName();
            }
        } catch (NoSuchFieldException e) {
            throw ExceptionUtils.mpe("Error: NoSuchField in %s.  Cause:", e, filed.getName());
        }
        return label;
    }
}

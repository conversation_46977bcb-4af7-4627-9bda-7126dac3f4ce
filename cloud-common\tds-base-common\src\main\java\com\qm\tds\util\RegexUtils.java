package com.qm.tds.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description: 正则表达式工具类
 * @author: Cyl
 * @time: 2020/6/23 11:07
 */
@SuppressWarnings("unused")
public class RegexUtils {

    private RegexUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 手机号码正则表达式
     */
    public static final String PHONE = "^0{0,1}[1][3-9][0-9]{9}$";

    /**
     * 座机号正则表达式
     */
    public static final String TEL = "(\\+\\d+)?(\\d{3,4}\\-?)?\\d{7,8}$";

    /**
     * 邮箱正则表达式
     */
    public static final String EMAIL = "^[\\w-]+(\\.[\\w-]+)*@[\\w-]+(\\.[\\w-]+)+$";

    /**
     * 网址正则表达式
     */
    public static final String URL = "^(http|https)://?((([\\w-]+\\.)+[\\w-]+(/[\\w-./?%&=]*)?)|)";

    /**
     * 身份证号正则表达式
     */
    public static final String IDCARDNO = "^\\d{15}$|^(\\d{17}[\\dx])$|^(\\d{17}[\\dX])$";

    /**
     * 中文文字正则表达式
     */
    public static final String CHINESECHAR = "^[\\w\\uFF00-\\uFFFF\\u4e00-\\u9fa5\\u3000-\\u301e\\ufe10-\\ufe19\\ufe30-\\ufe44\\ufe50-\\ufe6b\\uff01-\\uffee]+$";

    /**
     * 牌照号正则表达式
     */
    public static final String LICENSEPLATE = "^[\\u4e00-\\u9fa5]{1}[A-Z]{1}[A-Z_0-9]{5}[\\u4e00-\\u9fa5]?$";

    /**
     * VIN正则表达式
     */
    public static final String VIN = "^[A-Z0-9]{17}$";

    /**
     * 判断是否为手机号码 符合返回ture
     *
     * @param str
     * @return boolean
     */
    public static boolean isPhone(String str) {
        return regular(str, PHONE);
    }

    /**
     * 判断是否为座机号 符合返回ture
     *
     * @param str
     * @return boolean
     */
    public static boolean isTel(String str) {
        return regular(str, TEL);
    }

    /**
     * 判断是否为邮箱  符合返回ture
     *
     * @param str
     * @return boolean
     */
    public static boolean isEmail(String str) {
        return regular(str, EMAIL);
    }

    /**
     * 判断是否为网址  符合返回ture
     *
     * @param str
     * @return boolean
     */
    public static boolean isUrl(String str) {
        return regular(str, URL);
    }

    /**
     * 判断是否为身份证号 符合返回ture
     *
     * @param str
     * @return boolean
     */
    public static boolean isIdCardNO(String str) {
        return regular(str, IDCARDNO);
    }

    /**
     * 判断是否为中文文字 符合返回ture
     *
     * @param str
     * @return boolean
     */
    public static boolean isChineseChar(String str) {
        return regular(str, CHINESECHAR);
    }

    /**
     * 判断是否为牌照号 符合返回ture
     *
     * @param str
     * @return boolean
     */
    public static boolean isLicensePlate(String str) {
        return regular(str, LICENSEPLATE);
    }

    /**
     * 判断是否为VIN 符合返回ture
     *
     * @param str
     * @return boolean
     */
    public static boolean isVin(String str) {
        return regular(str, VIN);
    }

    /**
     * 匹配是否符合正则表达式pattern 匹配返回true
     *
     * @param str     匹配的字符串
     * @param pattern 匹配模式
     * @return boolean
     */
    private static boolean regular(String str, String pattern) {
        if (null == str || str.trim().length() <= 0) {
            return false;
        }
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(str);
        return m.matches();
    }


}

package com.qm.tds.util;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description: steam分页
 * @author: Cyl
 * @time: 2020/6/10 09:46
 */
public class StreamPageList<T> {

    /**
     * 当前页码
     */
    private long currentPage = 1;
    /**
     * 页数大小
     */
    private long pageSize;
    /**
     * 总页数
     */
    private long totalPages;

    /**
     * 总条数
     */
    private long total;

    /**
     * 排序
     */
    private Comparator<? super T> comparator;

    /**
     * 分页数据
     */
    private List<T> items;

    /**
     * @param data        需要分页的数据
     * @param size        每页显示的条数
     * @param currentPage 当前页码
     */
    public StreamPageList(List<T> data, long size, long currentPage) {
        if (size <= 0) {
            pageSize = 99999;
        } else {
            pageSize = size;
        }
        Optional<List<T>> list = Optional.ofNullable(data);
        items = list.orElse(new ArrayList<>());
        total = items.size();

        if (total % pageSize > 0) {
            totalPages = (total / pageSize) + 1;
        } else {
            totalPages = total / pageSize;
        }

        setCurrentPage(currentPage);
    }

    public void setCurrentPage(long currentPage) {
        if (currentPage < 1) {
            this.currentPage = 1;
        } else {
            this.currentPage = Math.min(currentPage, totalPages);
        }
    }

    public long getCurrentPage() {
        return currentPage;
    }

    /**
     * 获取items数据的部分结果
     * {@link Stream#skip(long)}抛弃前long个数据
     * {@link Stream#limit(long)}保留long个数据
     *
     * @return 过滤后的数据
     */
    public List<T> getItems() {
        Stream<T> limit = items.stream()
                .skip((currentPage < 1 ? 1 : currentPage - 1) * pageSize)
                .limit(pageSize);
        if (BootAppUtil.isNullOrEmpty(comparator)) {
            return limit.parallel().collect(Collectors.toList());
        }
        return limit.sorted(comparator).collect(Collectors.toList());
    }


    public long getTotal() {
        return total;
    }

    public long getPageSize() {
        return pageSize;
    }

    public long getTotalPages() {
        return totalPages;
    }

    public void setComparator(Comparator<? super T> comparator) {
        this.comparator = comparator;
    }
}

package com.qm.tds.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qm.common.uiep.mp.service.impl.UiepBaseServiceImpl;
import com.qm.common.uiep.table.domain.TableInfo;
import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.mp.pagination.QmPage;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.Function;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.select.SelectExpressionItem;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 表格工具类
 * @author: Cyl
 * @time: 2020/7/8 15:20
 */
@Slf4j
public class TableUtils {

    private TableUtils() {
    }

    /**
     * 将MybatisPlus标准的分页信息转换为前台表格组件能识别的分页信息
     *
     * @param pageInfo MybatisPlus标准的分页信息
     * @return 分页信息对象
     */
    public static <T> QmPage<T> convertQmPageFromMpPage(IPage<T> pageInfo) {
        return QmPage.convertFromMpPage(pageInfo);
    }

    /**
     * 从DTO中提取分页查询需要的IPage对象
     *
     * @param dto 自定义查询入参
     * @param <T> 实体对象
     * @return IPage对象
     */
    public static <T> IPage<T> convertToIPage(JsonParamDto dto) {
        IPage<T> pageInfo = new Page<>();
        pageInfo.setSize(dto.getPageSize());
        pageInfo.setCurrent(dto.getCurrentPage());
        return pageInfo;
    }

    /**
     * 添加表格附加的高级信息（过滤、排序等）
     *
     * @param queryWrapper 查询构造器
     * @param jsonParamDto 表格附加的高级信息
     * @return 查询构造器
     */
    public static <T> QueryWrapper<T> appendTableAdditional(QueryWrapper<T> queryWrapper, JsonParamDto jsonParamDto) {
        TableInfo tableInfo = new TableInfo();
        if (jsonParamDto != null) {
            tableInfo.setPageIndex(jsonParamDto.getCurrentPage());
            tableInfo.setPageSize(jsonParamDto.getPageSize());
            tableInfo.setSort(jsonParamDto.getTsortby());
            tableInfo.setFilter(jsonParamDto.getTheadFilter());
            tableInfo.setWhere(jsonParamDto.getTwhere());
            tableInfo.setSummary(jsonParamDto.getTsummary());
            tableInfo.setGroup(jsonParamDto.getTgroupby());
        }
        UiepBaseServiceImpl<?, T> serviceImpl = new UiepBaseServiceImpl<>();
        serviceImpl.appendTableAdditional(queryWrapper, tableInfo);

        return queryWrapper;
    }

    /**
     * @param column 是否需要列名
     * @description: 行转列
     * @author: Cyl
     * @time: 2020/7/28 11:13
     */
    public static <T> List rowWrap(Class<T> clazz, List<T> list, boolean column) {
        Field[] fields = clazz.getDeclaredFields();
        List<List> result = new ArrayList<>(fields.length);
        for (int i = 0; i < fields.length; i++) {
            result.add(new ArrayList());
        }
        for (T t : list) {
            for (int i = 0; i < fields.length; i++) {
                List l = result.get(i);
                Field field = fields[i];
                try {
                    ReflectionUtils.makeAccessible(field);// 用来替代field.setAccessible(true);函数
                    if (l.isEmpty() && column) {
                        l.add(field.getName());
                    }
                    l.add(field.get(t));
                } catch (IllegalAccessException e) {
                    log.info("---error--"+"rowWrap 失败", e);
                }
            }
        }
        return result;
    }

    /**
     * 获取查询字段对应的SelectExpressionItem
     *
     * @param functionName 函数名
     * @param column       列名
     * @param alias        别名
     * @return
     */
    public static SelectExpressionItem getExpressionItem(String functionName, String column, String alias) {
        Function function = new Function();
        function.setName(functionName);
        List<Expression> expressions = new ArrayList<>();
        Column strValue = new Column(column);
        ExpressionList expressionList = new ExpressionList();
        expressions.add(strValue);
        expressionList.setExpressions(expressions);
        function.setParameters(expressionList);
        SelectExpressionItem selectExpressionItem = new SelectExpressionItem(function);
        Alias a = new Alias(alias);
        selectExpressionItem.setAlias(a);
        return selectExpressionItem;
    }
}

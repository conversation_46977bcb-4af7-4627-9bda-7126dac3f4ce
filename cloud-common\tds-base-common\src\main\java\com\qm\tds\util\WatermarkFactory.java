package com.qm.tds.util;

import com.qm.tds.util.extend.ImagesWatermarkUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/1
 */
@Component
public class WatermarkFactory implements ApplicationContextAware {


    @Value("#{'${qm.base.upload.limit-format:jpg,jpeg,png,jfif}'.split(',')}")
    public void setImageFormat(List<String> imageFormat) {
        this.imageFormat = imageFormat;
    }

    private static List<String> imageFormat;

    private static ApplicationContext applicationContext;

    public static WatermarkUtils getWatermarkUtils(String suffix) {
        if (imageFormat.contains(suffix.toLowerCase())) {
            return new ImagesWatermarkUtils();
        }
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}

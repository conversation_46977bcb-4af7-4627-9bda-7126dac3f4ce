package com.qm.tds.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/1
 */
public interface WatermarkUtils {
    /**
     * 全屏铺满水印
     */
    MultipartFile fullScreen(MultipartFile file, String text) throws IOException;

    /**
     * 右下角写入水印
     */
    MultipartFile bottomRightCorner(MultipartFile file, String text) throws IOException;
}

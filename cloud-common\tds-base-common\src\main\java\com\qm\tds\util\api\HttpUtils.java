package com.qm.tds.util.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;
import java.util.Map;

@Slf4j
public class HttpUtils {

	public static JSONObject doPostJSON(String URL, String jsonBody, Header[] headers) throws Exception {
		CloseableHttpClient httpclient = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(URL);
		httpPost.setHeaders(headers);
		httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
		StringEntity se = new StringEntity(jsonBody);
		se.setContentEncoding("UTF-8");
		se.setContentType("application/json");
		httpPost.setEntity(se);
		CloseableHttpResponse response = httpclient.execute(httpPost);
		String resData = EntityUtils.toString(response.getEntity(), "UTF-8");
		final JSONObject resJson = JSON.parseObject(resData);
		httpclient.close();
		return resJson;
	}

	public static JSONObject doGetJSON(String URL, Header[] headers) throws Exception {
		CloseableHttpClient httpclient = HttpClients.createDefault();
		System.out.println("-----URL-----"+URL);
		HttpGet httpGet = new HttpGet(URL);
		httpGet.setHeaders(headers);
		CloseableHttpResponse response = httpclient.execute(httpGet);
		String resData = EntityUtils.toString(response.getEntity(), "UTF-8");
		final JSONObject resJson = JSON.parseObject(resData);
		httpclient.close();
		return resJson;
	}

	public static String get(String url, String param) {
		String result = "";
		BufferedReader in = null;
		try {
			String urlNameString;
			if(param==null){
				urlNameString = url;
			}else {
				urlNameString = url + "?" + param;
			}
			URL realUrl = new URL(urlNameString);
			// 打开和URL之间的连接
			URLConnection connection = realUrl.openConnection();
			// 设置通用的请求属性
			connection.setRequestProperty("accept", "*/*");
			connection.setRequestProperty("connection", "Keep-Alive");
			connection.setRequestProperty("user-agent",
					"Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			// 建立实际的连接
			connection.connect();
			// 获取所有响应头字段
			Map<String, List<String>> map = connection.getHeaderFields();
			// 遍历所有的响应头字段
			for (String key : map.keySet()) {
				System.out.println(key + "--->" + map.get(key));
			}
			// 定义 BufferedReader输入流来读取URL的响应
			in = new BufferedReader(new InputStreamReader(
					connection.getInputStream()));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (Exception e) {
			log.info("---error--"+e.getMessage());
		}
		// 使用finally块来关闭输入流
		finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (Exception e2) {
				log.info("---error--"+e2.getMessage());
			}
		}
		return result;
	}

	public static String post(String actionUrl, String params)
			throws IOException {
		String serverURL = actionUrl;
		StringBuffer sbf = new StringBuffer();
		String strRead = null;
		URL url = new URL(serverURL);
		HttpURLConnection connection = (HttpURLConnection)url.openConnection();
		connection.setRequestMethod("POST");//请求post方式
		connection.setDoInput(true);
		connection.setDoOutput(true);
		//header内的的参数在这里set
		//connection.setRequestProperty("key", "value");
		connection.setRequestProperty("Content-Type", "application/json");
		connection.connect();

		OutputStreamWriter writer = new OutputStreamWriter(connection.getOutputStream(),"UTF-8");
		//body参数放这里
		writer.write(params);
		writer.flush();
		InputStream is = connection.getInputStream();
		BufferedReader reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
		while ((strRead = reader.readLine()) != null) {
			sbf.append(strRead);
			sbf.append("\r\n");
		}
		reader.close();
		connection.disconnect();
		String results = sbf.toString();
		return results;
	}
}

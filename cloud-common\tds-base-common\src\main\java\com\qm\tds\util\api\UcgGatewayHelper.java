package com.qm.tds.util.api;

import org.apache.http.Header;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;

import java.nio.charset.Charset;
import java.util.Map;

/**
 * 通过网关调用ERPV6服务的工具类
 *
 * 	注意：这个类除了超时时间，其他都不需要动。
 *
 * <AUTHOR>
 *
 */
public class UcgGatewayHelper {

	/**
	 * 这三个超时时间可以修改，调用ERPV6服务的超时设置，请根据自己的需求进行修改 单位: 毫秒
	 */
	// 设置从connect Manager获取Connection 超时时间，单位毫秒
	private static final int HTTP_CONNECTION_REQUEST_TIMEOUT_MILLIS = 1 * 60 * 1000;
	// 设置连接超时时间，单位毫秒。
	private static final int HTTP_CONNECT_TIMEOUT_MILLIS = 2 * 60 * 1000;
	// 请求获取数据的超时时间，单位毫秒。 如果访问一个接口，多少时间内无法返回数据，就直接放弃此次调用。
	private static final int HTTP_SOCKET_TIMEOUT_MILLIS = 60 * 60 * 1000;

	//常量定义，勿动
	private static final String APPLICATION_JSON = "application/json";
	private static final String UTF_8 = "UTF-8";
	private static final String HTTP_CONTENT_TYPE_APPLICATION_JSON = "application/json";
	private static final String HTTP_CONTENT_TYPE = "Content-Type";
	private static final String ACCESSTOKEN = "?access_token=";


	private static String doGetJSONReturnString(String URL, Header[] headers) throws Exception {
		CloseableHttpClient httpclient = HttpClients.createDefault();
		HttpGet httpGet = new HttpGet(URL);
		// 设置超时
		httpGet.setConfig(setTimeOut());
		httpGet.setHeaders(headers);
		CloseableHttpResponse response = httpclient.execute(httpGet);
		String resData = EntityUtils.toString(response.getEntity(), UTF_8);
		httpclient.close();
		return resData;
	}

	public static String doPostJSONReturnString(String URL, String jsonBody, Header[] headers) throws Exception {
		CloseableHttpClient httpclient = HttpClients.createDefault();
		HttpPost httpPost = new HttpPost(URL);
		httpPost.setConfig(setTimeOut());
		httpPost.setHeaders(headers);
		httpPost.addHeader(HTTP_CONTENT_TYPE, HTTP_CONTENT_TYPE_APPLICATION_JSON);
		// 设置请求体编码为UTF8 避免乱码
		StringEntity se = new StringEntity(jsonBody, Charset.forName(UTF_8));
		se.setContentEncoding(UTF_8);
		se.setContentType(APPLICATION_JSON);
		httpPost.setEntity(se);
		// 发送请求
		CloseableHttpResponse response = httpclient.execute(httpPost);
		String resData = EntityUtils.toString(response.getEntity(), UTF_8);
		httpclient.close();
		return resData;
	}


	private static String doPutJSONReturnString(String URL, String jsonBody , Header[] headers) throws Exception {
		CloseableHttpClient httpclient = HttpClients.createDefault();
		HttpPut httpPut = new HttpPut(URL);
		httpPut.setConfig(setTimeOut());
		httpPut.setHeaders(headers);
		httpPut.addHeader(HTTP_CONTENT_TYPE, HTTP_CONTENT_TYPE_APPLICATION_JSON);
		// 设置请求体编码为UTF8 避免乱码
		StringEntity se = new StringEntity(jsonBody, Charset.forName(UTF_8));
		se.setContentEncoding(UTF_8);
		se.setContentType(APPLICATION_JSON);
		httpPut.setEntity(se);
		CloseableHttpResponse response = httpclient.execute(httpPut);
		String resData = EntityUtils.toString(response.getEntity(), UTF_8);
		httpclient.close();
		return resData;
	}

	private static String doDeleteJSONReturnString(String URL, Header[] headers) throws Exception {
		CloseableHttpClient httpclient = HttpClients.createDefault();
		HttpDelete httpDelete = new HttpDelete(URL);
		// 设置超时
		httpDelete.setConfig(setTimeOut());
		httpDelete.setHeaders(headers);
		CloseableHttpResponse response = httpclient.execute(httpDelete);
		String resData = EntityUtils.toString(response.getEntity(), UTF_8);
		httpclient.close();
		return resData;
	}


	/**
	 * 设置超时时间
	 *
	 * @return
	 */
	private static RequestConfig setTimeOut() {
		// 设置超时时间
		RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(HTTP_CONNECT_TIMEOUT_MILLIS)
				.setConnectionRequestTimeout(HTTP_CONNECTION_REQUEST_TIMEOUT_MILLIS)
				.setSocketTimeout(HTTP_SOCKET_TIMEOUT_MILLIS).build();
		return requestConfig;
	}

	/**
	 * 调用服务
	 *
	 * @param method
	 * @param url
	 * @param accessToken
	 * @param jsonBody
	 * @param headerParas
	 * @return
	 * @throws Exception
	 */
	public static String requestService(String method, String url, String accessToken, String jsonBody,
										Map<String, String> headerParas) throws Exception {
		Header[] headers = null;
		String result = null;
		method = method.toUpperCase();
		if (headerParas != null && headerParas.size() > 0) {
			int index = 0;
			headers = new Header[headerParas.size()];
			for (Map.Entry<String, String> entry : headerParas.entrySet()) {
				headers[index++] = new BasicHeader(entry.getKey(), entry.getValue());
			}
		}
		switch(method){
			case "GET":
				result = doGetJSONReturnString(url + "?access_token=" + accessToken,headers);
				break;
			case "POST":
			case "ANY":
				System.out.println("-----url-----"+url+ "?access_token=" + accessToken);
				result = doPostJSONReturnString(url + "?access_token=" + accessToken,jsonBody, headers);
				break;
			case "PUT":
				result = doPutJSONReturnString(url + "?access_token=" + accessToken,jsonBody, headers);
				break;
			case "DELETE":
				result = doDeleteJSONReturnString(url + "?access_token=" + accessToken, headers);
				break;
		}
		return result;
	}

}

package com.qm.tds.util.extend;

import com.qm.tds.base.domain.MultipartFileDecorator;
import com.qm.tds.util.ImageUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/1
 */
public abstract class AbstractWatermarkUtils {



    /**
     * @param inputStream
     * @param text
     * @return
     * @throws IOException
     */
    public MultipartFile drawText(MultipartFile inputStream, List<String> text, Font font) throws IOException {
        String suffix = ImageUtil.getSuffix(inputStream.getOriginalFilename());

        BufferedImage read = ImageIO.read(inputStream.getInputStream());
        Graphics2D graphics = read.createGraphics();
        String stringMaxLength = getStringMaxLength(text);

        graphics.setFont(font);
        graphics.setColor(Color.decode("#E6E6E6"));
        graphics.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, 0.5f));

        // 初始化文字坐标
        int width = read.getWidth() - graphics.getFontMetrics().stringWidth(stringMaxLength);
        int height = read.getHeight() - (graphics.getFontMetrics().getHeight() * text.size());
        // 循环写入水印
        for (int i = 0; i < text.size(); i++) {
            long tempHeight = height + graphics.getFontMetrics().getHeight() * i + 10L;
            graphics.drawString(text.get(i), width - 20f, tempHeight);
        }
        graphics.dispose();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(read, suffix, outputStream);
        return new MultipartFileDecorator(inputStream, outputStream.toByteArray());
    }


    protected static String getStringMaxLength(List<String> text) {
        String maxLeng = "";
        for (String s : text) {
            if (StringUtils.isEmpty(s)) {
                continue;
            }
            if (maxLeng.length() < s.length()) {
                maxLeng = s;
            }
        }
        return maxLeng;
    }


}

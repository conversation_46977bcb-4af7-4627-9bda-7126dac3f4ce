package com.qm.tds.util.extend;

import com.google.common.collect.Lists;
import com.qm.tds.base.domain.MultipartFileDecorator;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.ImageUtil;
import com.qm.tds.util.WatermarkUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.font.GlyphVector;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/1
 */
@Slf4j
public class ImagesWatermarkUtils extends AbstractWatermarkUtils implements WatermarkUtils {


    @Override
    public MultipartFile fullScreen(MultipartFile file, String text) throws IOException {
        String suffix = ImageUtil.getSuffix(file.getOriginalFilename());


        BufferedImage image = ImageIO.read(file.getInputStream());
        int width = image.getWidth(null);
        int height = image.getHeight(null);

        Font font = new Font("微软雅黑", Font.PLAIN, 18);


        JLabel label = new JLabel(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "    " + text);
        FontMetrics metrics = label.getFontMetrics(font);
        int textWidth = metrics.stringWidth(label.getText());
        int textHeight = metrics.getHeight();

        int row = height * 2 / textHeight;
        int column = width * 2 / textWidth;
        int y = ~(height - 1);
        for (int i = 0; i < row; i++) {
            int x = 0;
            for (int j = 0; j < column; j++) {
                Graphics2D graphics = image.createGraphics();

                graphics.setFont(font);
                graphics.rotate(Math.toRadians(30));
                graphics.setStroke(new BasicStroke(0.1f));

                GlyphVector v = graphics.getFont().createGlyphVector(graphics.getFontMetrics().getFontRenderContext(), label.getText());


                Shape shape = v.getOutline();
                graphics.translate(x, y);
                graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                graphics.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, 0.8f));

                graphics.setColor(Color.decode("#EFEFEF"));
                graphics.fill(shape);
                graphics.setColor(Color.WHITE.darker().darker());
                graphics.draw(shape);
                graphics.dispose();

                x = x + textWidth + 80;
            }
            y = y + 80;
        }

        //防止图片变色
        BufferedImage read = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_BGR);
        Graphics g = read.getGraphics();
        g.drawImage(image, 0, 0, null);
        g.dispose();


        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        if(!"jfif".equals(suffix) && !"JFIF".equals(suffix)) {//图片格式为其他
            ImageIO.write(image, suffix, outputStream);
        }else{
            //修改图片写入方式，为JPEGImageEncoder，原因ImageIO不支持jfif格式----20211230---start--
            /*JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(outputStream);
            encoder.encode(image);*/
            //新的方法
            ImageUtil.saveAsJPEG(100, image, (float)1, outputStream);
            //修改图片写入方式，为JPEGImageEncoder，原因ImageIO不支持jfif格式----20211230---end--
        }
        return new MultipartFileDecorator(file, outputStream.toByteArray());
    }

    @Override
    public MultipartFile bottomRightCorner(MultipartFile file, String text) throws IOException {
        String suffix = ImageUtil.getSuffix(file.getOriginalFilename());
        BufferedImage read = ImageIO.read(file.getInputStream());

        List<String> list = Lists.newArrayList();
        Date date = new Date();
        list.add(DateFormatUtils.format(date, "HH:ss"));
        list.add(DateFormatUtils.format(date, "yyyy-MM-dd") + " " + DateUtils.getWeekDays(date));
        if (StringUtils.isNotEmpty(text)) {
            list.add(text);
        }

        for (int i = 0; i < list.size(); i++) {

            Graphics2D graphics = read.createGraphics();
            graphics.setFont(new Font("微软雅黑", Font.BOLD, 40));
            graphics.setStroke(new BasicStroke(2));
            if (i == 1) {
                graphics.setFont(new Font("微软雅黑", Font.BOLD, 24));
                graphics.setStroke(new BasicStroke(1.2f));
            }
            if (i == 2) {
                graphics.setFont(new Font("微软雅黑", Font.BOLD, 20));
                graphics.setStroke(new BasicStroke(1f));
            }
            GlyphVector v = graphics.getFont().createGlyphVector(graphics.getFontMetrics().getFontRenderContext(), list.get(i));
            Shape shape = v.getOutline();
            Rectangle bounds = shape.getBounds();
            int x = (read.getWidth() - bounds.width) / 2 - bounds.x;
            int y = read.getHeight() - (bounds.height * list.size());
            graphics.translate(
                    x,
                    y + (i * 20)
            );
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            graphics.setColor(Color.WHITE);
            graphics.fill(shape);
            graphics.setColor(Color.decode("#E6E6E6").darker().darker());
            graphics.draw(shape);
            graphics.dispose();

        }

        //防止图片变色
        BufferedImage image = new BufferedImage(read.getWidth(), read.getHeight(), BufferedImage.TYPE_INT_BGR);
        Graphics g = image.getGraphics();
        g.drawImage(read, 0, 0, null);
        g.dispose();

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        if(!"jfif".equals(suffix) && !"JFIF".equals(suffix)) {//图片格式为其他
            ImageIO.write(image, suffix, outputStream);
        }else{
            //修改图片写入方式，为JPEGImageEncoder，原因ImageIO不支持jfif格式----20211230---start--
           /* JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(outputStream);
            encoder.encode(image);*/
            //新的方法
            ImageUtil.saveAsJPEG(100, image, (float)1, outputStream);
            //修改图片写入方式，为JPEGImageEncoder，原因ImageIO不支持jfif格式----20211230---end--
        }
        return new MultipartFileDecorator(file, outputStream.toByteArray());
    }

    public static void main(String[] args) {

        BufferedImage image = new BufferedImage(200, 200, BufferedImage.TYPE_INT_RGB);
        Graphics2D graphics = (Graphics2D) image.getGraphics();
        Font font = new Font("微软雅黑", Font.PLAIN, 60);
        graphics.setFont(font);
        graphics.setBackground(Color.decode("#EFEFEF"));
        graphics.clearRect(0, 0, 200, 200);

        GlyphVector v = graphics.getFont().createGlyphVector(graphics.getFontMetrics().getFontRenderContext(), "Excel");
        Shape shape = v.getOutline();
        Rectangle bounds = shape.getBounds();
        int x = (image.getWidth() - bounds.width) / 2 - bounds.x;
        int y = (image.getHeight() - bounds.height) / 2 - bounds.y;


        graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);//设置抗锯齿
        graphics.setPaint(new Color(0, 0, 0, 64));//阴影颜色
        graphics.drawString("Excel", x, y);//先绘制阴影
        graphics.setPaint(Color.LIGHT_GRAY);//正文颜色
        graphics.drawString("Excel", x, y);//用正文颜色覆盖上去


        // 防止图片变色
        BufferedImage image2 = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_BGR);
        Graphics g = image2.getGraphics();
        g.drawImage(image, 0, 0, null);
        g.dispose();


        File file = new File("C:\\Users\\<USER>\\Pictures\\Saved Pictures\\Demo.jpg");
        try (OutputStream outputStream = new FileOutputStream(file)) {
            ImageIO.write(image2, "jpg", outputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}

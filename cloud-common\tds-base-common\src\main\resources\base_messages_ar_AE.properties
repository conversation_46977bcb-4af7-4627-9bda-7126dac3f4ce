#这里填写阿语翻译
ERR.basecommon.ControllerAspect.databaseInterrupt=انقطع الاتصال بقاعدة البيانات، يرجى المحاولة لاحقًا:
ERR.basecommon.ControllerAspect.databaseCannotConn=لا يجوز الاتصال بقاعدة البيانات، يرجى المحاولة لاحقًا.
ERR.basecommon.ControllerAspect.saveFailUniqueExist=فشل في الحفظ! بيانات بند الزر الفريد (مثل: الرمز وإلخ) موجودة بالفعل، يرجى إعادة إدخاله من جديد!
ERR.basecommon.ControllerAspect.columnLengthOverPrompt=يتجاوز طول الحقل [%s] الحد الأقصى للطول، يرجى إدخاله من جديد!
ERR.basecommon.ControllerAspect.columnLengthOver=يتجاوز طول الحقل الحد الأقصى للطول، يرجى إدخاله من جديد!
ERR.basecommon.ControllerAspect.columnNullPrompt=لا يجوز أن يكون الحقل [%s] فارغًا، يرجى إدخاله من جديد!
ERR.basecommon.ControllerAspect.columnNull=لا يجوز أن يكون الحقل المطلوب فارغًا، يرجى إدخاله من جديد!
ERR.basecommon.ControllerAspect.sqlFormatError=تنسيق عبارة SQL غير صحيح!
ERR.basecommon.ControllerAspect.createTrancException=شؤون الإنشاء غير طبيعية، يرجى التحقق من مصدر البيانات:
ERR.basecommon.ControllerAspect.remoteServerInnerException=يوجد الاستثناء الداخلي للخدمة عن بعد، يرجى التحقق من منطق رمز الخدمة عن بعد أو ما إذا انتهت مهلة الاتصال:
ERR.basecommon.ControllerAspect.attachOverLimitPrompt=حد حجم الملحق غير طبيعي! حجم الحد: %s، حجم الملف الذي تم تحميله %s
ERR.basecommon.ControllerAspect.redisConnOvertime=انتهت مهلة الاتصال بـredis، يرجى التحقق من بيئة الشبكة أو المحاولة مرة أخرى في وقت لاحق!
ERR.basecommon.ControllerAspect.httpRequestParamReadException=قراءة معلمات الإدخال لطلب HTTP غير طبيعية، كانت معلمات الإدخال الخاصة بك فارغة أو كان تنسيق معلمات الإدخال غير صحيح.
ERR.basecommon.ControllerAspect.datasourceConfigWrong=خطأ في تكوين مصدر البيانات، يرجى التحقق من مصدر البيانات:
ERR.basecommon.ControllerAspect.totalInfoPrompt=هناك %s بند من المعلومات:
ERR.basecommon.Swagger2Config.tenderId=معرف المستأجر
ERR.basecommon.Swagger2Config.companyId=معرف الشركة
ERR.basecommon.Swagger2Config.operaterId=معرف المشغل
ERR.basecommon.Swagger2Config.operaterName=اسم المشغل
ERR.basecommon.Swagger2Config.languageCode=رمز اللغة
ERR.basecommon.Swagger2Config.personCode=رمز الفرد
ERR.basecommon.Swagger2Config.customGroupId=معرف مجموعة العملاء
ERR.basecommon.Swagger2Config.requestSource=مصدر الطلب
ERR.basecommon.Swagger2Config.loginUniqueMark=العلامة الوحيدة لتسجيل الدخول
ERR.basecommon.Swagger2Config.interface= الواجهة
ERR.basecommon.Swagger2Config.apiDoc=وثائق واجهة EP API
ERR.basecommon.QmException.diyException=معلومات الاستثناء المخصصة
ERR.basecommon.QmRemoteHystrix.invokeException=الاتصال بالواجهة غير طبيعية!
ERR.basecommon.QmRemoteHystrix.undoneMethod=لم يتم تنفيذ الطريقة بعد، ولن تكون ذات فائدة في المستقبل.
ERR.basecommon.QmBaseServiceImpl.infoChangedReflush=تم تغيير المعلومات، يرجى التحديث وإعادة تشغيله من جديد!
ERR.basecommon.QmBaseServiceImpl.deleteDataNonexist=البيانات المطلوبة من حذفها غير موجودة، يرجى المحاولة مرة أخرى!
ERR.basecommon.common.saveFail=فشل في الحفظ!
ERR.basecommon.common.delSuccess=تم الحذف بنجاح!
ERR.basecommon.common.delFail=فشل في الحذف!
ERR.basecommon.common.operateSuccess=تم التشغيل بنجاح!
ERR.basecommon.common.operateFail=فشل في التشغيل!
ERR.basecommon.common.uploadSuccess=تم التحميل بنجاح!
ERR.basecommon.common.uploadFail=فشل في التحميل!
ERR.basecommon.UploadFileServiceImpl.fileTypeError=نوع الملف غير صحيح، يمكن تحميله فقط:
ERR.basecommon.UploadFileServiceImpl.downloadError=خطأ في عملية تحميل الملف، يرجى عرض السجل اليومي!
ERR.basecommon.UploadFileServiceImpl.urlWrong=يوجد الخطأ في عنوان الملف، فشل في تحميل الملف! url
ERR.basecommon.UploadFileServiceImpl.responseHeaderException=رأس تعيين response غير طبيعي
ERR.basecommon.COSUtils.cosServiceException=COS Service في سحابة تينسنت غير طبيعية، يرجى البحث عن السجل غير الصحيح في الخادم
ERR.basecommon.COSUtils.cosClientException=COS Client في سحابة تينسنت غير طبيعية، يرجى البحث عن السجل غير الصحيح في الخادم
ERR.basecommon.COSUtils.fileioError=تشغيل IO للملف غير صحيح، يرجى عرض السجل اليومي
ERR.basecommon.DateUtils.sunday=الأحد
ERR.basecommon.DateUtils.monday=الاثنين
ERR.basecommon.DateUtils.tuesday=الثلاثاء
ERR.basecommon.DateUtils.wednesday=الأربعاء
ERR.basecommon.DateUtils.thursday=الخميس
ERR.basecommon.DateUtils.friday=الجمعة
ERR.basecommon.DateUtils.saturday=السبت
ERR.basecommon.ElkLogUtils.logMarkNull=علامة السجل اليومي فارغة، يرجى التأكد مما إذا تم تمرير القيمة عند الاتصال
ERR.basecommon.ElkLogUtils.logLevelNull=مستوى السجل اليومي فارغ، يرجى التأكد مما إذا تم تمرير القيمة عند الاتصال
ERR.basecommon.ElkLogUtils.saveElkLogFail=فشل في حفظ السجل اليومي Elk!
ERR.basecommon.ElkLogUtils.elkLogBuildFail=فشل في إنشاء سجل Elk!
ERR.basecommon.CosOperator.cosFileioError=خطأ في تشغيل IO لملف COS تينسنت، يرجى عرض السجل اليومي
ERR.basecommon.CosOperator.cosUploadFail=فشل في تحميل الملف cos، يرجى عرض السجل اليومي!
ERR.basecommon.FtpOperator.ftpCauseError=خطأ في FTP
ERR.basecommon.FtpOperator.ftpNotFoundFile=لا يوجد هذا الملف على خادم FTP أو قد تم حذف هذا الملف
ERR.basecommon.TdsOperator.fileServiceUrlNull=لا يمكن أن يكون خادم الملف [عنوان الخادم] فارغًا!
ERR.basecommon.TdsOperator.serviceUrlIncorrect=عنوان الخادم غير صحيح!
ERR.basecommon.TdsOperator.soapServiceCreateFail=فشل في إنشاء SoapService [
ERR.basecommon.FtpUtil.ftpIpUrlWrong=قد يكون عنوان IP لـFTP غير صحيح، يرجى تكوينه بشكل صحيح
ERR.basecommon.FtpUtil.ftpPortWrong=منفذ FTP غير صحيح، يرجى تكوينه بشكل صحيح
ERR.basecommon.FtpUtil.downloadFail=فشل في تحميل الملف
ERR.basecommon.ImageUtil.imgConvertExecption=تحويل ملف الصورة غير طبيعي، قد لم تقوم بتحميل الصورة!
ERR.basecommon.RandomUtils.generateRandomQueueError=خطأ في إنشاء الطابور العشوائي!
ERR.basecommon.ReflectUtil.nonexistProperty=هذه الخاصية غير موجودة:
ERR.basecommon.ReflectUtil.getPropertyException=قيمة الخاصية التي تم الحصول عليها غير طبيعية:
ERR.basecommon.ReflectUtil.timestampNull=الطابع الزمني الوارد فارغ
############################
ERR.basecommon.UploadFileServiceImpl.fileUploadFail=aفشل في تحميل الملف
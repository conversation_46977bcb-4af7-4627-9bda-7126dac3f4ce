#这里填写西班牙语翻译
ERR.basecommon.ControllerAspect.databaseInterrupt=La conexión a la base de datos se ha interrumpido, por favor, inténtelo luego:
ERR.basecommon.ControllerAspect.databaseCannotConn=No se ha podido conectar a la base de datos, por favor, inténtelo más tarde.
ERR.basecommon.ControllerAspect.saveFailUniqueExist=¡Error al guardar! Los datos de la clave única (por ejemplo, el código, etc.) ya existen, por favor, ¡vuelva a introducirlos!
ERR.basecommon.ControllerAspect.columnLengthOverPrompt=La longitud del campo [%s] de letras excede el límite máximo de longitud, por favor, ¡vuelva a introducirlo!
ERR.basecommon.ControllerAspect.columnLengthOver=La longitud del campo de letras excede el límite máximo de longitud, por favor, ¡vuelva a introducirlo!
ERR.basecommon.ControllerAspect.columnNullPrompt=El campo [%s] de letras no puede estar vacío, por favor, ¡vuelva a introducirlo!
ERR.basecommon.ControllerAspect.columnNull=El campo obligatorio de letras no puede estar vacío, por favor, ¡vuelva a introducirlo!
ERR.basecommon.ControllerAspect.sqlFormatError=¡El formato de la instrucción SQL es incorrecto!
ERR.basecommon.ControllerAspect.createTrancException=Error en la creación de transacción, por favor, compruebe la fuente de datos:
ERR.basecommon.ControllerAspect.remoteServerInnerException=Error interno en el servicio remoto, por favor, compruebe la lógica del código del servicio remoto o si el tiempo de llamada se ha agotado:
ERR.basecommon.ControllerAspect.attachOverLimitPrompt=¡El límite de tamaño de los archivos adjuntos es anormal! Tamaño limitado: %s, tamaño del archivo cargado %s
ERR.basecommon.ControllerAspect.redisConnOvertime=Se agotó el tiempo de espera de la conexión redis, compruebe las condiciones de red o vuelva a intentarlo más tarde.
ERR.basecommon.ControllerAspect.httpRequestParamReadException=Error en leer parámetros de entrada de solicitud HTTP, su entrada está vacía o el formato de la entrada es incorrecto.
ERR.basecommon.ControllerAspect.datasourceConfigWrong=La fuente de datos está mal configurada, por favor, compruebe la fuente de datos:
ERR.basecommon.ControllerAspect.totalInfoPrompt=Un total de %s informaciones:
ERR.basecommon.Swagger2Config.tenderId=ID del inquilino
ERR.basecommon.Swagger2Config.companyId=ID de la empresa
ERR.basecommon.Swagger2Config.operaterId=ID del operador
ERR.basecommon.Swagger2Config.operaterName=Nombre del operador
ERR.basecommon.Swagger2Config.languageCode=Código del idioma
ERR.basecommon.Swagger2Config.personCode=Código de personal
ERR.basecommon.Swagger2Config.customGroupId=ID del grupo de clientes
ERR.basecommon.Swagger2Config.requestSource=Fuente de la solicitud
ERR.basecommon.Swagger2Config.loginUniqueMark=Identificador único de inicio de sesión
ERR.basecommon.Swagger2Config.interface=Interfaz
ERR.basecommon.Swagger2Config.apiDoc=Documentación de la interfaz EPAPI
ERR.basecommon.QmException.diyException=Información de anomalías personalizada
ERR.basecommon.QmRemoteHystrix.invokeException=¡Error de llamada a la interface!
ERR.basecommon.QmRemoteHystrix.undoneMethod=El método sin práctica y no habrá importancia en el futuro.
ERR.basecommon.QmBaseServiceImpl.infoChangedReflush=La información ha cambiado, por favor actualice y vuelva a intentar esta operación.
ERR.basecommon.QmBaseServiceImpl.deleteDataNonexist=Los datos a eliminar no existen, por favor, ¡inténtelo de nuevo!
ERR.basecommon.common.saveFail=¡Error al guardar!
ERR.basecommon.common.delSuccess=¡Borrado con éxito!
ERR.basecommon.common.delFail=¡El borrado ha fallado!
ERR.basecommon.common.operateSuccess=¡Operación exitosa!
ERR.basecommon.common.operateFail=¡Operación fallida!
ERR.basecommon.common.uploadSuccess=¡Carga exitosa!
ERR.basecommon.common.uploadFail=¡Carga fallida!
ERR.basecommon.UploadFileServiceImpl.fileTypeError=Error del tipo de archivo, sólo puede cargar:
ERR.basecommon.UploadFileServiceImpl.downloadError=Error de operación de descarga de archivo, ¡verifique el registro!
ERR.basecommon.UploadFileServiceImpl.urlWrong=Error de dirección de archivo, ¡la descarga del archivo ha fallado! url
ERR.basecommon.UploadFileServiceImpl.responseHeaderException=Error en el establecimiento de la cabecera de la respuesta
ERR.basecommon.COSUtils.cosServiceException=Tencent Cloud COSService es anormal, verifique el registro de errores en el servidor
ERR.basecommon.COSUtils.cosClientException=Tencent Cloud COSClient es anormal, verifique el registro de errores en el servidor
ERR.basecommon.COSUtils.fileioError=Error de operación de IO de archivo, por favor revise los registros
ERR.basecommon.DateUtils.sunday=Domingo
ERR.basecommon.DateUtils.monday=Lunes
ERR.basecommon.DateUtils.tuesday=Martes
ERR.basecommon.DateUtils.wednesday=Miércoles
ERR.basecommon.DateUtils.thursday=Jueves
ERR.basecommon.DateUtils.friday=Viernes
ERR.basecommon.DateUtils.saturday=Sábado
ERR.basecommon.ElkLogUtils.logMarkNull=El identificador de registro está vacío, por favor, compruebe si pasa el valor durante la llamada.
ERR.basecommon.ElkLogUtils.logLevelNull=El nivel de registro está vacío, por favor, compruebe si pasa el valor durante la llamada.
ERR.basecommon.ElkLogUtils.saveElkLogFail=¡Fallo en guardar el registro de Elk!
ERR.basecommon.ElkLogUtils.elkLogBuildFail=¡Fallo en la compilación del registro de Elk!
ERR.basecommon.CosOperator.cosFileioError=Error de operación IO del archivo Tencent COS, por favor, compruebe el registro.
ERR.basecommon.CosOperator.cosUploadFail=Fallo de cos en la carga de archivo, por favor, ¡compruebe el registro!
ERR.basecommon.FtpOperator.ftpCauseError=Error de FTP
ERR.basecommon.FtpOperator.ftpNotFoundFile=No hay tal archivo en el servidor FTP o el archivo ha sido borrado
ERR.basecommon.TdsOperator.fileServiceUrlNull=El servidor de archivos [dirección del servidor] no puede estar vacío.
ERR.basecommon.TdsOperator.serviceUrlIncorrect=¡La dirección del servidor es incorrecta!
ERR.basecommon.TdsOperator.soapServiceCreateFail=Fallo al crear SoapService [
ERR.basecommon.FtpUtil.ftpIpUrlWrong=La dirección IP del FTP puede ser incorrecta, por favor configúrela correctamente.
ERR.basecommon.FtpUtil.ftpPortWrong=Posible fallo del puerto FTP, por favor configúrelo correctamente
ERR.basecommon.FtpUtil.downloadFail=Error al descargar el archivo
ERR.basecommon.ImageUtil.imgConvertExecption=Error de conversión del archivo de imagen, ¡puede que no hayas subido una foto!
ERR.basecommon.RandomUtils.generateRandomQueueError=¡Error al generar la cola aleatoria!
ERR.basecommon.ReflectUtil.nonexistProperty=La propiedad no existe:
ERR.basecommon.ReflectUtil.getPropertyException=Error al obtener el valor de la propiedad:
ERR.basecommon.ReflectUtil.timestampNull=La marca de tiempo entrante está vacía.
############################=
ERR.basecommon.UploadFileServiceImpl.fileUploadFail=Error en la carga del archivo

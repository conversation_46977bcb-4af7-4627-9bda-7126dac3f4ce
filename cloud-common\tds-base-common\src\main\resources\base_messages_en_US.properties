#这里填写英文翻译
ERR.basecommon.ControllerAspect.databaseInterrupt=The database connection is interrupted, please try again later:
ERR.basecommon.ControllerAspect.databaseCannotConn=Unable to connect to database, please try again later.
ERR.basecommon.ControllerAspect.saveFailUniqueExist=Save failed! Unique key item data (such as: code, etc.) already exists, please re-enter!
ERR.basecommon.ControllerAspect.columnLengthOverPrompt=The field [%s] length exceeds the maximum limit, please re-enter!
ERR.basecommon.ControllerAspect.columnLengthOver=The field length exceeds the maximum limit, please re-enter!
ERR.basecommon.ControllerAspect.columnNullPrompt=Field [%s] cannot be empty, please re-enter!
ERR.basecommon.ControllerAspect.columnNull=Required fields should not be null, please re-enter!
ERR.basecommon.ControllerAspect.sqlFormatError=Incorrect SQL statement format!
ERR.basecommon.ControllerAspect.createTrancException=The transaction creation is abnormal, please check data source:
ERR.basecommon.ControllerAspect.remoteServerInnerException=The internal remote service is abnormal, please check the remote service code logic or call timeout:
ERR.basecommon.ControllerAspect.attachOverLimitPrompt=Abnormal attachment size limit! Limit size: %s, uploaded file size %s
ERR.basecommon.ControllerAspect.redisConnOvertime=The redis connection is timed out, please check the network environment or try again later!
ERR.basecommon.ControllerAspect.httpRequestParamReadException=The HTTP request input parameter is abnormally read. Your input parameter is empty or the input parameter format is incorrect.
ERR.basecommon.ControllerAspect.datasourceConfigWrong=The data source is misconfigured, please check the data source:
ERR.basecommon.ControllerAspect.totalInfoPrompt=A total of %s items of information:
ERR.basecommon.Swagger2Config.tenderId=tenant id
ERR.basecommon.Swagger2Config.companyId=Company ID
ERR.basecommon.Swagger2Config.operaterId=Operator ID
ERR.basecommon.Swagger2Config.operaterName=Operator name
ERR.basecommon.Swagger2Config.languageCode=Language code
ERR.basecommon.Swagger2Config.personCode=Personnel code
ERR.basecommon.Swagger2Config.customGroupId=Customer group id
ERR.basecommon.Swagger2Config.requestSource=Request source
ERR.basecommon.Swagger2Config.loginUniqueMark=Login unique ID
ERR.basecommon.Swagger2Config.interface= Interface
ERR.basecommon.Swagger2Config.apiDoc=EP API Interface document
ERR.basecommon.QmException.diyException=Abnormal custom information
ERR.basecommon.QmRemoteHystrix.invokeException=The calling interface is abnormal!
ERR.basecommon.QmRemoteHystrix.undoneMethod=The method has not been implemented yet, and will be of little use in the future.
ERR.basecommon.QmBaseServiceImpl.infoChangedReflush=The information has been changed, please refresh and try again!
ERR.basecommon.QmBaseServiceImpl.deleteDataNonexist=The data to be deleted does not exist, please try again!
ERR.basecommon.common.saveFail=Save failed!
ERR.basecommon.common.delSuccess=Deletion succeeded!
ERR.basecommon.common.delFail=Deletion failed!
ERR.basecommon.common.operateSuccess=Operation succeeded!
ERR.basecommon.common.operateFail=Operation failed!
ERR.basecommon.common.uploadSuccess=Upload succeeded!
ERR.basecommon.common.uploadFail=Upload failed!
ERR.basecommon.UploadFileServiceImpl.fileTypeError=Wrong file type, upload only:
ERR.basecommon.UploadFileServiceImpl.downloadError=File download operation error, please check the log!
ERR.basecommon.UploadFileServiceImpl.urlWrong=Wrong file address, file download failed! url
ERR.basecommon.UploadFileServiceImpl.responseHeaderException=Abnormal response set header
ERR.basecommon.COSUtils.cosServiceException=Tencent cloud COS Service is abnormal, please check the error log on the server
ERR.basecommon.COSUtils.cosClientException=Tencent cloud COS Client is abnormal, please check the error log on the server
ERR.basecommon.COSUtils.fileioError=File IO operation error, please check the log
ERR.basecommon.DateUtils.sunday=Sunday
ERR.basecommon.DateUtils.monday=Monday
ERR.basecommon.DateUtils.tuesday=Tuesday
ERR.basecommon.DateUtils.wednesday=Wednesday
ERR.basecommon.DateUtils.thursday=Thursday
ERR.basecommon.DateUtils.friday=Friday
ERR.basecommon.DateUtils.saturday=Saturday
ERR.basecommon.ElkLogUtils.logMarkNull=The log ID is empty, please confirm whether a value is input when calling
ERR.basecommon.ElkLogUtils.logLevelNull=The log level is empty, please confirm whether a value is input when calling
ERR.basecommon.ElkLogUtils.saveElkLogFail=Elk log save failed!
ERR.basecommon.ElkLogUtils.elkLogBuildFail=Elk log build failed!
ERR.basecommon.CosOperator.cosFileioError=Tencent COS file IO operation error, please check the log
ERR.basecommon.CosOperator.cosUploadFail=Cos file upload failed, please check the log!
ERR.basecommon.FtpOperator.ftpCauseError=FTP error
ERR.basecommon.FtpOperator.ftpNotFoundFile=The file does not exist on the FTP server or the file has been deleted
ERR.basecommon.TdsOperator.fileServiceUrlNull=File server [server address] should not be null!
ERR.basecommon.TdsOperator.serviceUrlIncorrect=Incorrect server address!
ERR.basecommon.TdsOperator.soapServiceCreateFail=SoapService creation failed[
ERR.basecommon.FtpUtil.ftpIpUrlWrong=FTP IP address may be wrong, please configure correctly
ERR.basecommon.FtpUtil.ftpPortWrong=FTP port is wrong, please configure correctly
ERR.basecommon.FtpUtil.downloadFail=File download failed
ERR.basecommon.ImageUtil.imgConvertExecption=The conversion of the image file is abnormal, the uploaded image may not be a photo!
ERR.basecommon.RandomUtils.generateRandomQueueError=Random queue generation error!
ERR.basecommon.ReflectUtil.nonexistProperty=The attribute does not exist:
ERR.basecommon.ReflectUtil.getPropertyException=Abnormal attribute value acquisition:
ERR.basecommon.ReflectUtil.timestampNull=Timestamp input is empty

############################
ERR.basecommon.UploadFileServiceImpl.fileUploadFail=Failed to upload file
#这里填写法语翻译
ERR.basecommon.ControllerAspect.databaseInterrupt=La connexion à la base de données a été interrompue, veuillez réessayer plus tard :
ERR.basecommon.ControllerAspect.databaseCannotConn=Impossible de se connecter à la base de données, veuillez réessayer plus tard.
ERR.basecommon.ControllerAspect.saveFailUniqueExist=Echec de la sauvegarde ! Les données uniques de l'élément clé (telles que : code, etc.) Existent déjà, veuillez les saisir à nouveau !
ERR.basecommon.ControllerAspect.columnLengthOverPrompt=La longueur du champ [%s] dépasse la limite de longueur maximale, veuillez saisir à nouveau !
ERR.basecommon.ControllerAspect.columnLengthOver=La longueur du champ dépasse la limite de longueur maximale, veuillez saisir à nouveau !
ERR.basecommon.ControllerAspect.columnNullPrompt=Le champ [%s] ne peut pas être vide, veuillez le saisir à nouveau !
ERR.basecommon.ControllerAspect.columnNull=Les champs obligatoires ne peuvent pas être vides, veuillez les saisir à nouveau !
ERR.basecommon.ControllerAspect.sqlFormatError=Le format de l'instruction SQL est incorrect !
ERR.basecommon.ControllerAspect.createTrancException=Créer une exception de transaction, veuillez vérifier la source de données :
ERR.basecommon.ControllerAspect.remoteServerInnerException=Exception interne de service à distance, veuillez vérifier la logique du code de service à distance ou le délai d'appel :
ERR.basecommon.ControllerAspect.attachOverLimitPrompt=La limite de taille de pièce jointe est anormale ! Taille limite : %s, taille du fichier téléchargé %s
ERR.basecommon.ControllerAspect.redisConnOvertime=La connexion redis a expiré, veuillez vérifier l'environnement réseau ou réessayer plus tard !
ERR.basecommon.ControllerAspect.httpRequestParamReadException=Le paramètre d'entrée de la requête HTTP est lu anormalement. Votre paramètre d'entrée est vide ou le format du paramètre d'entrée est incorrect.
ERR.basecommon.ControllerAspect.datasourceConfigWrong=La source de données est mal configurée, veuillez vérifier la source de données :
ERR.basecommon.ControllerAspect.totalInfoPrompt=Un total de %s informations :
ERR.basecommon.Swagger2Config.tenderId=Identifiant du locataire
ERR.basecommon.Swagger2Config.companyId=ID de l'entreprise
ERR.basecommon.Swagger2Config.operaterId=ID de l'opérateur
ERR.basecommon.Swagger2Config.operaterName=Nom de l'opérateur
ERR.basecommon.Swagger2Config.languageCode=Code langue
ERR.basecommon.Swagger2Config.personCode=Code du personnel
ERR.basecommon.Swagger2Config.customGroupId=ID de groupe de clients
ERR.basecommon.Swagger2Config.requestSource=Origine de la demande
ERR.basecommon.Swagger2Config.loginUniqueMark=Identifiant unique de connexion
ERR.basecommon.Swagger2Config.interface=Interface
ERR.basecommon.Swagger2Config.apiDoc=Documentation de l'interface EPAPI
ERR.basecommon.QmException.diyException=Informations sur les exceptions personnalisées
ERR.basecommon.QmRemoteHystrix.invokeException=L'interface d'appel est anormale !
ERR.basecommon.QmRemoteHystrix.undoneMethod=La méthode n'a pas encore été mise en œuvre et sera peu utile à l'avenir.
ERR.basecommon.QmBaseServiceImpl.infoChangedReflush=Les informations ont été modifiées, veuillez actualiser et réessayer !
ERR.basecommon.QmBaseServiceImpl.deleteDataNonexist=Les données à supprimer n'existent pas, veuillez réessayer !
ERR.basecommon.common.saveFail=Echec de la sauvegarde !
ERR.basecommon.common.delSuccess=Supprimé avec succès !
ERR.basecommon.common.delFail=Echec de la suppression !
ERR.basecommon.common.operateSuccess=Opération réussie !
ERR.basecommon.common.operateFail=L'opération a échoué!
ERR.basecommon.common.uploadSuccess=Téléchargé avec succès !
ERR.basecommon.common.uploadFail=Le téléchargement a échoué !
ERR.basecommon.UploadFileServiceImpl.fileTypeError=Mauvais type de fichier, téléchargement uniquement :
ERR.basecommon.UploadFileServiceImpl.downloadError=Erreur d'opération de téléchargement de fichier, veuillez vérifier le journal !
ERR.basecommon.UploadFileServiceImpl.urlWrong=L'adresse du fichier est erronée, le téléchargement du fichier a échoué ! url
ERR.basecommon.UploadFileServiceImpl.responseHeaderException=Exception d'en-tête d'ensemble de réponses
ERR.basecommon.COSUtils.cosServiceException=Tencent cloud COSService est anormal, veuillez vérifier le journal des erreurs sur le serveur
ERR.basecommon.COSUtils.cosClientException=Tencent cloud COSClient est anormal, veuillez vérifier le journal des erreurs sur le serveur
ERR.basecommon.COSUtils.fileioError=Erreur d'opération d'e/s de fichier, veuillez vérifier le journal
ERR.basecommon.DateUtils.sunday=Dimanche
ERR.basecommon.DateUtils.monday=Lundi
ERR.basecommon.DateUtils.tuesday=Mardi
ERR.basecommon.DateUtils.wednesday=Mercredi
ERR.basecommon.DateUtils.thursday=Jeudi
ERR.basecommon.DateUtils.friday=Vendredi
ERR.basecommon.DateUtils.saturday=Samedi
ERR.basecommon.ElkLogUtils.logMarkNull=L'ID de journal est vide, veuillez confirmer si vous souhaitez transmettre une valeur lors de l'appel
ERR.basecommon.ElkLogUtils.logLevelNull=Le niveau de journalisation est vide, veuillez confirmer si vous souhaitez transmettre une valeur lors de l'appel
ERR.basecommon.ElkLogUtils.saveElkLogFail=Echec de l'enregistrement du journal Elk !
ERR.basecommon.ElkLogUtils.elkLogBuildFail=Echec de la construction du journal Elk !
ERR.basecommon.CosOperator.cosFileioError=Erreur d'opération d'e/s du fichier tencent cos, veuillez vérifier le journal
ERR.basecommon.CosOperator.cosUploadFail=Le téléchargement du fichier par cos a échoué, veuillez vérifier le journal !
ERR.basecommon.FtpOperator.ftpCauseError=Erreur FTP
ERR.basecommon.FtpOperator.ftpNotFoundFile=Le fichier n'existe pas sur le serveur FTP ou le fichier a été supprimé
ERR.basecommon.TdsOperator.fileServiceUrlNull=Le serveur de fichiers [adresse du serveur] ne peut pas être vide !
ERR.basecommon.TdsOperator.serviceUrlIncorrect=L'adresse du serveur est incorrecte !
ERR.basecommon.TdsOperator.soapServiceCreateFail=Impossible de créer SoapService [
ERR.basecommon.FtpUtil.ftpIpUrlWrong=L'adresse IP du FTP peut être erronée, veuillez la configurer correctement
ERR.basecommon.FtpUtil.ftpPortWrong=Le port de FTP est incorrect, veuillez le configurer correctement
ERR.basecommon.FtpUtil.downloadFail=Le téléchargement du fichier a échoué
ERR.basecommon.ImageUtil.imgConvertExecption=La conversion du fichier image est anormale, l'image téléchargée n'est peut-être pas une photo !
ERR.basecommon.RandomUtils.generateRandomQueueError=Erreur lors de la génération d'une file d'attente aléatoire !
ERR.basecommon.ReflectUtil.nonexistProperty=La propriété n'existe pas :
ERR.basecommon.ReflectUtil.getPropertyException=Obtenir l'exception de valeur de propriété :
ERR.basecommon.ReflectUtil.timestampNull=L'horodatage entrant est vide
############################=
ERR.basecommon.UploadFileServiceImpl.fileUploadFail=Echec du téléchargement du fichier

#这里填写俄文翻译
ERR.basecommon.ControllerAspect.databaseInterrupt=Соединение с базой данных прервано, повторить попытку позже:
ERR.basecommon.ControllerAspect.databaseCannotConn=Не удается подключиться к базе данных, повторить попытку позже.
ERR.basecommon.ControllerAspect.saveFailUniqueExist=Сохранение не удалось! Уникальные данные о ключевых позициях (например, код и т.д.) уже существуют, ввести их повторно!
ERR.basecommon.ControllerAspect.columnLengthOverPrompt=Длина поля [%s] превышает максимальный предел длины, ввести его повторно!
ERR.basecommon.ControllerAspect.columnLengthOver=Длина поля превышает максимальный предел длины, ввести его повторно!
ERR.basecommon.ControllerAspect.columnNullPrompt=Поле [%s] не должен быть пустым, ввести его повторно!
ERR.basecommon.ControllerAspect.columnNull=Поля, требующие заполнения, не должны быть пустыми, ввести их повторно!
ERR.basecommon.ControllerAspect.sqlFormatError=Формат фразы SQL неверен!
ERR.basecommon.ControllerAspect.createTrancException=Произошло исключение при создании транзакции, проверить источник данных:
ERR.basecommon.ControllerAspect.remoteServerInnerException=Произошло исключение внутри удаленной службы, проверить логику кода удаленной службы на тайм-аут вызова:
ERR.basecommon.ControllerAspect.attachOverLimitPrompt=Исключение при ограничение размера прикрепленного файла! Предельный размер: %s, размер загруженного файла %s
ERR.basecommon.ControllerAspect.redisConnOvertime=Тайм-аут подключения redis, проверить сетевую среду или повторить попытку позже!
ERR.basecommon.ControllerAspect.httpRequestParamReadException=Произошло исключение при чтении входных параметров HTTP-запроса. Ваш входной параметр пуст или формат входного параметра неверен.
ERR.basecommon.ControllerAspect.datasourceConfigWrong=Неправильная конфигурация источника данных, проверить источник данных:
ERR.basecommon.ControllerAspect.totalInfoPrompt=Всего %s информаций
ERR.basecommon.Swagger2Config.tenderId=Id арендатора
ERR.basecommon.Swagger2Config.companyId=ID компании
ERR.basecommon.Swagger2Config.operaterId=ID оператора
ERR.basecommon.Swagger2Config.operaterName=Имя оператора
ERR.basecommon.Swagger2Config.languageCode=Код языка
ERR.basecommon.Swagger2Config.personCode=Код персонала
ERR.basecommon.Swagger2Config.customGroupId=Id группы клиентов
ERR.basecommon.Swagger2Config.requestSource=Источник запроса
ERR.basecommon.Swagger2Config.loginUniqueMark=Уникальный идентификатор для входа в систему
ERR.basecommon.Swagger2Config.interface=Интерфейс
ERR.basecommon.Swagger2Config.apiDoc=Документ интерфейса EPAPI
ERR.basecommon.QmException.diyException=Информация о пользовательской исключительной ситуации
ERR.basecommon.QmRemoteHystrix.invokeException=Исключение при вызове интерфейса
ERR.basecommon.QmRemoteHystrix.undoneMethod=Этот метод еще не реализован, и в будущем он будет бесполезен.
ERR.basecommon.QmBaseServiceImpl.infoChangedReflush=Информация изменилась, обновить, потом повторить данную операцию!
ERR.basecommon.QmBaseServiceImpl.deleteDataNonexist=Данные, которые вы хотите удалить, не существуют,  повторить попытку еще раз!
ERR.basecommon.common.saveFail=Сохранение не удалось!
ERR.basecommon.common.delSuccess=Удаление удалось!
ERR.basecommon.common.delFail=Удаление не удалось!
ERR.basecommon.common.operateSuccess=Операция прошла успешно!
ERR.basecommon.common.operateFail=Операция прошла неуспешно!
ERR.basecommon.common.uploadSuccess=Загрузка удалась!
ERR.basecommon.common.uploadFail=Загрузка не удалась!
ERR.basecommon.UploadFileServiceImpl.fileTypeError=Тип файла неверен, он может быть только загружен:
ERR.basecommon.UploadFileServiceImpl.downloadError=Операция по загрузке файла была выполнена неправильно, проверить журнал!
ERR.basecommon.UploadFileServiceImpl.urlWrong=Неверный адрес файла, и загрузка файла не удалась! URL-адрес
ERR.basecommon.UploadFileServiceImpl.responseHeaderException=Произошло исключение при настройке заголовки response
ERR.basecommon.COSUtils.cosServiceException=Исключение COSService Tencent Cloud, найти журнал ошибок на сервере
ERR.basecommon.COSUtils.cosClientException=Исключение COSClient Tencent Cloud, найти журнал ошибок на сервере
ERR.basecommon.COSUtils.fileioError=Произошла ошибка при выполнении операции IO, проверить журнал
ERR.basecommon.DateUtils.sunday=Воскресенье
ERR.basecommon.DateUtils.monday=Понедельник
ERR.basecommon.DateUtils.tuesday=Вторник
ERR.basecommon.DateUtils.wednesday=Среда
ERR.basecommon.DateUtils.thursday=Четверг
ERR.basecommon.DateUtils.friday=Пятница
ERR.basecommon.DateUtils.saturday=Суббота
ERR.basecommon.ElkLogUtils.logMarkNull=Идентификатор журнала пуст, подтвердите, передается ли значение при вызове
ERR.basecommon.ElkLogUtils.logLevelNull=Уровень журнала пуст, подтвердите, передается ли значение при вызове
ERR.basecommon.ElkLogUtils.saveElkLogFail=Не удалось сохранить журнал Elk!
ERR.basecommon.ElkLogUtils.elkLogBuildFail=Не удалось создать журнал Elk
ERR.basecommon.CosOperator.cosFileioError=Операция по IO файлов Tencent была выполнена неправильно, проверить журнал.
ERR.basecommon.CosOperator.cosUploadFail=Не удалось загрузить файл cos, проверить журнал!
ERR.basecommon.FtpOperator.ftpCauseError=Произошла ошибка в FTP
ERR.basecommon.FtpOperator.ftpNotFoundFile=Этот файл не существует на FTP-сервере или файл был удален
ERR.basecommon.TdsOperator.fileServiceUrlNull=Файловый сервер [адрес сервера] не должен быть пустым!
ERR.basecommon.TdsOperator.serviceUrlIncorrect=Неверный адрес сервера!
ERR.basecommon.TdsOperator.soapServiceCreateFail=Не удалось создать SoapService[
ERR.basecommon.FtpUtil.ftpIpUrlWrong=IP-адрес FTP может быть неправильным, сконфигурировать его правильно
ERR.basecommon.FtpUtil.ftpPortWrong=Порт FTP неверен, сконфигурировать его правильно
ERR.basecommon.FtpUtil.downloadFail=Не удалось загрузить файл
ERR.basecommon.ImageUtil.imgConvertExecption=Произошло исключение при преобразовании файла изображения, загруженный вами файл может не относиться к фотографии!
ERR.basecommon.RandomUtils.generateRandomQueueError=Произошла ошибка при создании случайной очереди!
ERR.basecommon.ReflectUtil.nonexistProperty=Этот атрибут не существует:
ERR.basecommon.ReflectUtil.getPropertyException=Произошло исключение при получении значения атрибута:
ERR.basecommon.ReflectUtil.timestampNull=Временная метка ввода пуста
############################=
ERR.basecommon.UploadFileServiceImpl.fileUploadFail=Не удалось загрузить файл
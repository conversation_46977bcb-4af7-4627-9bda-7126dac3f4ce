#这里填写越南语翻译
ERR.basecommon.ControllerAspect.databaseInterrupt=Kết nối cơ sở dữ liệu bị gián <PERSON>, hãy thử lại sau:
ERR.basecommon.ControllerAspect.databaseCannotConn=Không thể kết nối với cơ sở dữ liệu, hãy thử lại sau.
ERR.basecommon.ControllerAspect.saveFailUniqueExist=Lưu thất bại! Dữ liệu nút duy nhất (như: mã) đã tồn tại, hãy nhập lại!
ERR.basecommon.ControllerAspect.columnLengthOverPrompt=Độ dài của trường [%s] vượt quá giới hạn độ dài tối đa, hãy nhập lại!
ERR.basecommon.ControllerAspect.columnLengthOver=Độ dài của trường vượt quá giới hạn độ dài tối đa, hãy nhập lại!
ERR.basecommon.ControllerAspect.columnNullPrompt=Trường [%s] không được để trống, hãy nhập lại!
ERR.basecommon.ControllerAspect.columnNull=Các trường bắt buộc điền không được để trống, hãy nhập lại!
ERR.basecommon.ControllerAspect.sqlFormatError=Định dạng câu lệnh SQL không chính xác!
ERR.basecommon.ControllerAspect.createTrancException=Tạo sự việc bất thường, hãy kiểm tra nguồn dữ liệu:
ERR.basecommon.ControllerAspect.remoteServerInnerException=Nội bộ dịch vụ từ xa bất thường, hãy kiểm tra logic mã dịch vụ từ xa hoặc điều phối sử dụng có quá hạn hay không:
ERR.basecommon.ControllerAspect.attachOverLimitPrompt=Giới hạn kích thước phụ lục bất thường! Kích thước giới hạn: %s, kích thước tệp đã tải lên %s
ERR.basecommon.ControllerAspect.redisConnOvertime=Kết nối redis đã quá hạn, hãy kiểm tra môi trường mạng hoặc thử lại sau!
ERR.basecommon.ControllerAspect.httpRequestParamReadException=Yêu cầu HTTP đọc tham số đầu vào bất thường, tham số đầu vào của bạn trống hoặc định dạng tham số đầu vào không chính xác.
ERR.basecommon.ControllerAspect.datasourceConfigWrong=Cấu hình nguồn dữ liệu sai, hãy kiểm tra nguồn dữ liệu:
ERR.basecommon.ControllerAspect.totalInfoPrompt=Tổng số %s mục thông tin:
ERR.basecommon.Swagger2Config.tenderId=id người thuê
ERR.basecommon.Swagger2Config.companyId=ID công ty
ERR.basecommon.Swagger2Config.operaterId=ID nhân viên thao tác
ERR.basecommon.Swagger2Config.operaterName=Tên nhân viên thao tác
ERR.basecommon.Swagger2Config.languageCode=Mã ngôn ngữ
ERR.basecommon.Swagger2Config.personCode=Mã nhân viên
ERR.basecommon.Swagger2Config.customGroupId=id nhóm khách hàng
ERR.basecommon.Swagger2Config.requestSource=Nguồn yêu cầu
ERR.basecommon.Swagger2Config.loginUniqueMark=Biểu tượng duy nhất đăng nhập
ERR.basecommon.Swagger2Config.interface=Giao diện
ERR.basecommon.Swagger2Config.apiDoc=Tệp giao diện EPAPI
ERR.basecommon.QmException.diyException=Thông tin bất thường tùy chỉnh
ERR.basecommon.QmRemoteHystrix.invokeException=Giao diện điều phối sử dụng bất thường!
ERR.basecommon.QmRemoteHystrix.undoneMethod=Phương pháp chưa được thực hiện, sau này cũng sẽ ít được sử dụng.
ERR.basecommon.QmBaseServiceImpl.infoChangedReflush=Thông tin đã được thay đổi, hãy làm mới và thử lại!
ERR.basecommon.QmBaseServiceImpl.deleteDataNonexist=Dữ liệu cần xóa không tồn tại, hãy thử lại!
ERR.basecommon.common.saveFail=Lưu thất bại!
ERR.basecommon.common.delSuccess=Xóa thành công!
ERR.basecommon.common.delFail=Xóa thất bại!
ERR.basecommon.common.operateSuccess=Thao tác thành công!
ERR.basecommon.common.operateFail=Thao tác thất bại!
ERR.basecommon.common.uploadSuccess=Tải lên thành công!
ERR.basecommon.common.uploadFail=Tải lên thất bại!
ERR.basecommon.UploadFileServiceImpl.fileTypeError=Loại tệp sai, chỉ có thể tải lên:
ERR.basecommon.UploadFileServiceImpl.downloadError=Lỗi thao tác tải xuống tệp, hãy kiểm tra nhật ký!
ERR.basecommon.UploadFileServiceImpl.urlWrong=Địa chỉ tệp sai, tải xuống tệp thất bại! url
ERR.basecommon.UploadFileServiceImpl.responseHeaderException=Tiêu đề cài đặt response bất thường
ERR.basecommon.COSUtils.cosServiceException=Tencent Cloud COSService bất thường, hãy kiểm tra nhật ký lỗi trên máy chủ
ERR.basecommon.COSUtils.cosClientException=Tencent Cloud COSClient bất thường, hãy kiểm tra nhật ký lỗi trên máy chủ
ERR.basecommon.COSUtils.fileioError=Lỗi thao tác IO tệp, hãy kiểm tra nhật ký
ERR.basecommon.DateUtils.sunday=Chủ nhật
ERR.basecommon.DateUtils.monday=Thứ hai
ERR.basecommon.DateUtils.tuesday=Thứ ba
ERR.basecommon.DateUtils.wednesday=Thứ tư
ERR.basecommon.DateUtils.thursday=Thứ năm
ERR.basecommon.DateUtils.friday=Thứ sáu
ERR.basecommon.DateUtils.saturday=Thứ bảy
ERR.basecommon.ElkLogUtils.logMarkNull=Biểu tượng nhật ký trống, hãy xác nhận xem có chuyển giá trị khi điều phối sử dụng hay không
ERR.basecommon.ElkLogUtils.logLevelNull=Cấp nhật ký trống, hãy xác nhận xem có chuyển giá trị khi điều phối sử dụng hay không
ERR.basecommon.ElkLogUtils.saveElkLogFail=Lưu nhật ký Elk thất bại!
ERR.basecommon.ElkLogUtils.elkLogBuildFail=Xây dựng nhật ký Elk thất bại!
ERR.basecommon.CosOperator.cosFileioError=Lỗi thao tác IO tệp Tencent COS, hãy kiểm tra nhật ký
ERR.basecommon.CosOperator.cosUploadFail=cos tải tệp lên thất bại, hãy kiểm tra nhật ký!
ERR.basecommon.FtpOperator.ftpCauseError=FTP xảy ra lỗi
ERR.basecommon.FtpOperator.ftpNotFoundFile=Tệp này không tồn tại trên máy chủ FTP hoặc tệp này đã bị xóa
ERR.basecommon.TdsOperator.fileServiceUrlNull=Máy chủ tệp [địa chỉ máy chủ] không được để trống!
ERR.basecommon.TdsOperator.serviceUrlIncorrect=Địa chỉ máy chủ không chính xác!
ERR.basecommon.TdsOperator.soapServiceCreateFail=Tạo SoapService thất bại [
ERR.basecommon.FtpUtil.ftpIpUrlWrong=Địa chỉ IP của FTP có thể bị sai, hãy cấu hình chính xác
ERR.basecommon.FtpUtil.ftpPortWrong=Cổng FTP bị sai, hãy cấu hình chính xác
ERR.basecommon.FtpUtil.downloadFail=Tải xuống tệp thất bại
ERR.basecommon.ImageUtil.imgConvertExecption=Chuyển đổi văn bản hình ảnh bất thường, bạn tải lên có thể không phải là ảnh chụp!
ERR.basecommon.RandomUtils.generateRandomQueueError=Tạo hàng ngẫu nhiên bị lỗi!
ERR.basecommon.ReflectUtil.nonexistProperty=Thuộc tính này không tồn tại:
ERR.basecommon.ReflectUtil.getPropertyException=Lấy giá trị thuộc tính bất thường:
ERR.basecommon.ReflectUtil.timestampNull=Dấu thời gian chuyển vào trống
############################=
ERR.basecommon.UploadFileServiceImpl.fileUploadFail=Tải tệp lên thất bại

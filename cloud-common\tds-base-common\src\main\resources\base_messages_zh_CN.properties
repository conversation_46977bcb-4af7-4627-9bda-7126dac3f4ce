#这里填写中文翻译
ERR.basecommon.ControllerAspect.databaseInterrupt=数据库连接中断，请稍后再试:
ERR.basecommon.ControllerAspect.databaseCannotConn=无法连接数据库，请稍后再试。
ERR.basecommon.ControllerAspect.saveFailUniqueExist=保存失败！唯一键项数据（如：代码等）已经存在，请重新输入！
ERR.basecommon.ControllerAspect.columnLengthOverPrompt=字段[%s]长度超过最大长度限制，请重新输入！
ERR.basecommon.ControllerAspect.columnLengthOver=字段长度超过最大长度限制，请重新输入！
ERR.basecommon.ControllerAspect.columnNullPrompt=字段[%s]不可以为空，请重新输入！
ERR.basecommon.ControllerAspect.columnNull=必填字段不能为空，请重新输入！
ERR.basecommon.ControllerAspect.sqlFormatError=SQL语句格式不正确！
ERR.basecommon.ControllerAspect.createTrancException=创建事务异常，请检查数据源:
ERR.basecommon.ControllerAspect.remoteServerInnerException=远程服务内部异常，请检查远程服务代码逻辑或是否调用超时:
ERR.basecommon.ControllerAspect.attachOverLimitPrompt=附件大小限制异常！限制大小：%s,上传的文件大小%s
ERR.basecommon.ControllerAspect.redisConnOvertime=redis连接超时，请检查网络环境或稍后再试！
ERR.basecommon.ControllerAspect.httpRequestParamReadException=HTTP请求入参读取异常，您的入参为空 或 入参格式不正确。
ERR.basecommon.ControllerAspect.datasourceConfigWrong=数据源配置错误，请检查数据源:
ERR.basecommon.ControllerAspect.totalInfoPrompt=共%s项信息：
ERR.basecommon.Swagger2Config.tenderId=租户id
ERR.basecommon.Swagger2Config.companyId=公司ID
ERR.basecommon.Swagger2Config.operaterId=操作员ID
ERR.basecommon.Swagger2Config.operaterName=操作员名称
ERR.basecommon.Swagger2Config.languageCode=语言代码
ERR.basecommon.Swagger2Config.personCode=人员代码
ERR.basecommon.Swagger2Config.customGroupId=客户组id
ERR.basecommon.Swagger2Config.requestSource=请求来源
ERR.basecommon.Swagger2Config.loginUniqueMark=登录唯一标识
ERR.basecommon.Swagger2Config.interface=接口
ERR.basecommon.Swagger2Config.apiDoc=EP API接口文档
ERR.basecommon.QmException.diyException=自定义异常信息
ERR.basecommon.QmRemoteHystrix.invokeException=调用接口异常！
ERR.basecommon.QmRemoteHystrix.undoneMethod=方法尚未实现，以后也没啥用。
ERR.basecommon.QmBaseServiceImpl.infoChangedReflush=信息已发生变更，请您刷新后重新此操作！
ERR.basecommon.QmBaseServiceImpl.deleteDataNonexist=要删除的数据不存在，请重试！
ERR.basecommon.common.saveFail=保存失败！
ERR.basecommon.common.delSuccess=删除成功！
ERR.basecommon.common.delFail=删除失败！
ERR.basecommon.common.operateSuccess=操作成功！
ERR.basecommon.common.operateFail=操作失败！
ERR.basecommon.common.uploadSuccess=上传成功！
ERR.basecommon.common.uploadFail=上传失败！
ERR.basecommon.UploadFileServiceImpl.fileTypeError=文件类型错误，只能上传:
ERR.basecommon.UploadFileServiceImpl.downloadError=文件下载操作错误，请查看日志！
ERR.basecommon.UploadFileServiceImpl.urlWrong=文件地址有误，文件下载失败！url
ERR.basecommon.UploadFileServiceImpl.responseHeaderException=response设置头部异常
ERR.basecommon.COSUtils.cosServiceException=腾讯云COS Service异常,请在服务器查找错误日志
ERR.basecommon.COSUtils.cosClientException=腾讯云COS Client异常,请在服务器查找错误日志
ERR.basecommon.COSUtils.fileioError=文件IO操作错误，请查看日志
ERR.basecommon.DateUtils.sunday=星期日
ERR.basecommon.DateUtils.monday=星期一
ERR.basecommon.DateUtils.tuesday=星期二
ERR.basecommon.DateUtils.wednesday=星期三
ERR.basecommon.DateUtils.thursday=星期四
ERR.basecommon.DateUtils.friday=星期五
ERR.basecommon.DateUtils.saturday=星期六
ERR.basecommon.ElkLogUtils.logMarkNull=日志标识为空，请确认调用时是否传值
ERR.basecommon.ElkLogUtils.logLevelNull=日志级别为空，请确认调用时是否传值
ERR.basecommon.ElkLogUtils.saveElkLogFail=保存Elk日志失败！
ERR.basecommon.ElkLogUtils.elkLogBuildFail=Elk日志构建失败！
ERR.basecommon.CosOperator.cosFileioError=腾讯COS文件IO操作错误，请查看日志
ERR.basecommon.CosOperator.cosUploadFail=cos上传文件失败，请查看日志！
ERR.basecommon.FtpOperator.ftpCauseError=FTP发生错误
ERR.basecommon.FtpOperator.ftpNotFoundFile=FTP服务器上无此文件或该文件已被删除
ERR.basecommon.TdsOperator.fileServiceUrlNull=文件服务器[服务器地址]不能为空！
ERR.basecommon.TdsOperator.serviceUrlIncorrect=服务器地址不正确！
ERR.basecommon.TdsOperator.soapServiceCreateFail=创建SoapService失败[
ERR.basecommon.FtpUtil.ftpIpUrlWrong=FTP的IP地址可能错误，请正确配置
ERR.basecommon.FtpUtil.ftpPortWrong=FTP的端口错误,请正确配置
ERR.basecommon.FtpUtil.downloadFail=下载文件失败
ERR.basecommon.ImageUtil.imgConvertExecption=图片文件转化异常，您上传的可能不是照片哦！
ERR.basecommon.RandomUtils.generateRandomQueueError=生成随机队列出错！
ERR.basecommon.ReflectUtil.nonexistProperty=不存在该属性:
ERR.basecommon.ReflectUtil.getPropertyException=获取属性值异常:
ERR.basecommon.ReflectUtil.timestampNull=传入时间戳为空
############################
ERR.basecommon.UploadFileServiceImpl.fileUploadFail=上传文件失败
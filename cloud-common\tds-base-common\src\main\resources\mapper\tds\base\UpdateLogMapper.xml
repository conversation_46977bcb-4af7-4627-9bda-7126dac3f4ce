<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.tds.base.mapper.UpdateLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.tds.base.domain.bean.UpdateLogDO">
        <id column="ID" property="id"/>
        <result column="VTABLENAME" property="vtablename"/>
        <result column="VCOLNAME" property="vcolname"/>
        <result column="VCOLTEXT" property="vcoltext"/>
        <result column="NDATAID" property="ndataid"/>
        <result column="VOLDVALUE" property="voldvalue"/>
        <result column="VNEWVALUE" property="vnewvalue"/>
        <result column="NOPR" property="nopr"/>
        <result column="DOPR" property="dopr"/>
        <result column="DTSTAMP" property="dtstamp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    ID, VTABLENAME, VCOLNAME, VCOLTEXT, NDATAID, VOLDVALUE, VNEWVALUE, NOPR, DOPR, DTSTAMP
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
        select
        a.VTABLENAME,
        a.VCOLNAME,
        a.VCOLTEXT,
        a.NDATAID,
        a.VOLDVALUE,
        a.VNEWVALUE,
        a.NOPR,
        a.DOPR,
        a.DTSTAMP,
        a.ID,
        b.VCODE AS VOPRCODE,
        b.VTEXT AS VOPRTEXT,
        b.VLANGUAGECODE
        from sysb100 a
        left join SYSC000_M b on a.NOPR = b.NMAINID
        where a.VCOLNAME not in ('DTSTAMP')
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.tds.base.domain.bean.UpdateLogDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.tds.base.domain.bean.UpdateLogDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.tds.base.domain.bean.UpdateLogDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.tds.base.domain.bean.UpdateLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.tds.base.domain.bean.UpdateLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.tds.base.domain.bean.UpdateLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.tds.base.domain.bean.UpdateLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.tds.base.domain.bean.UpdateLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.tds.base.domain.bean.UpdateLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
</mapper>

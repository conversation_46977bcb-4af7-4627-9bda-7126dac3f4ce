# 数据源配置迁移指南：MySQL -> 人大金仓

## 问题描述

外部项目启动时报错：`java.sql.SQLException: com.mysql.cj.jdbc.Driver`，错误原因是：

1. 项目使用了动态数据源功能，数据源配置存储在数据库中
2. 虽然代码中已移除MySQL依赖，但数据库中的 `datasource_info` 表仍配置了MySQL驱动
3. 运行时系统尝试加载MySQL驱动类但找不到，导致数据源初始化失败

## 解决方案

### 步骤1：检查当前配置

执行以下SQL查看当前使用MySQL驱动的数据源：

```sql
SELECT 
    id, name, driver_class_name, conn_url, service_name
FROM datasource_info 
WHERE driver_class_name IN ('com.mysql.cj.jdbc.Driver', 'com.mysql.jdbc.Driver');
```

### 步骤2：备份数据（重要！）

```sql
CREATE TABLE datasource_info_backup AS SELECT * FROM datasource_info;
CREATE TABLE tenant_datasource_rel_backup AS SELECT * FROM tenant_datasource_rel;
```

### 步骤3：执行迁移脚本

运行提供的迁移脚本：`migrate_mysql_to_kingbase.sql`

或手动执行以下关键步骤：

```sql
-- 更新驱动类名
UPDATE datasource_info 
SET driver_class_name = 'com.kingbase8.Driver'
WHERE driver_class_name IN ('com.mysql.cj.jdbc.Driver', 'com.mysql.jdbc.Driver');

-- 更新连接URL（根据实际情况调整）
UPDATE datasource_info 
SET conn_url = REPLACE(conn_url, 'jdbc:mysql://', 'jdbc:kingbase8://')
WHERE conn_url LIKE 'jdbc:mysql://%';
```

### 步骤4：验证配置

```sql
SELECT id, name, driver_class_name, conn_url, service_name
FROM datasource_info 
ORDER BY service_name, name;
```

### 步骤5：重启应用

更新数据库配置后，需要重启相关的应用服务。

## 人大金仓驱动配置说明

### 驱动类名
- **MySQL**: `com.mysql.cj.jdbc.Driver`
- **人大金仓**: `com.kingbase8.Driver`

### 连接URL格式
- **MySQL**: `**************************************`
- **人大金仓**: `******************************************`

### 依赖配置

确保项目中已正确配置人大金仓依赖：

```xml
<dependency>
    <groupId>cn.com.kingbase</groupId>
    <artifactId>kingbase8</artifactId>
</dependency>
```

## 注意事项

1. **版本兼容性**: 确保人大金仓驱动版本与数据库服务器版本兼容
2. **连接参数**: 某些MySQL特有的连接参数可能需要调整或移除
3. **SQL语法**: 检查应用中是否有MySQL特有的SQL语法需要适配
4. **测试验证**: 在生产环境执行前，建议在测试环境充分验证

## 常见问题

### Q: 更新后仍然报错怎么办？
A: 检查以下几点：
- 确认人大金仓驱动jar包已正确加载
- 验证数据库连接URL格式正确
- 检查数据库服务器是否可访问
- 确认用户名密码正确

### Q: 如何回滚配置？
A: 使用备份表恢复：
```sql
DELETE FROM datasource_info;
INSERT INTO datasource_info SELECT * FROM datasource_info_backup;
```

### Q: 多个微服务如何处理？
A: 需要更新所有相关微服务的数据源配置，可以通过 `service_name` 字段筛选特定服务的配置。

## 相关文件

- `migrate_mysql_to_kingbase.sql`: 迁移脚本
- `datasource_info.sql`: 数据源表结构
- `tenant_datasource_rel.sql`: 租户数据源关联表结构

/*
 Navicat Premium Data Transfer

 Source Server         : ***********
 Source Server Type    : MySQL
 Source Server Version : 80019
 Source Host           : ***********:3306
 Source Schema         : qm_common_dynamic

 Target Server Type    : MySQL
 Target Server Version : 80019
 File Encoding         : 65001

 Date: 03/07/2020 16:47:01
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for datasource_info
-- ----------------------------
DROP TABLE IF EXISTS `datasource_info`;
CREATE TABLE `datasource_info`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据源名称 ',
  `conn_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据源连接地址',
  `username` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据源账号',
  `password` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据源密码',
  `driver_class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据库驱动名',
  `ds_properties` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据源的其他配置',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `DTSTAMP` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '时间戳',
  `service_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '微服务名称',
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '连接池类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- 更新MySQL驱动为人大金仓驱动的SQL脚本
-- 请根据实际情况执行以下SQL语句来更新数据源配置

-- 1. 查看当前使用MySQL驱动的数据源
-- SELECT id, name, driver_class_name, conn_url FROM datasource_info WHERE driver_class_name = 'com.mysql.cj.jdbc.Driver';

-- 2. 更新MySQL驱动为人大金仓驱动
-- UPDATE datasource_info
-- SET driver_class_name = 'com.kingbase8.Driver'
-- WHERE driver_class_name = 'com.mysql.cj.jdbc.Driver';

-- 3. 同时需要更新连接URL格式（如果需要的话）
-- 人大金仓的JDBC URL格式通常为：***********************************
-- 例如：UPDATE datasource_info
-- SET conn_url = REPLACE(conn_url, 'jdbc:mysql://', 'jdbc:kingbase8://')
-- WHERE conn_url LIKE 'jdbc:mysql://%';

-- 4. 验证更新结果
-- SELECT id, name, driver_class_name, conn_url FROM datasource_info;

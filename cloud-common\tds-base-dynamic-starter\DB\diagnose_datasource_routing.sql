-- =====================================================
-- 动态数据源路由问题诊断脚本
-- =====================================================
-- 用于诊断"部分功能走人大金仓，部分功能找MySQL"的问题

-- 1. 检查当前所有数据源配置
SELECT 
    '=== 所有数据源配置 ===' as info_type,
    id, 
    name, 
    driver_class_name, 
    conn_url, 
    service_name,
    type,
    remarks
FROM datasource_info 
ORDER BY service_name, name;

-- 2. 检查MySQL驱动的数据源（问题源头）
SELECT 
    '=== MySQL驱动数据源（需要更新） ===' as info_type,
    id, 
    name, 
    driver_class_name, 
    conn_url, 
    service_name,
    type
FROM datasource_info 
WHERE driver_class_name IN ('com.mysql.cj.jdbc.Driver', 'com.mysql.jdbc.Driver');

-- 3. 检查人大金仓驱动的数据源
SELECT 
    '=== 人大金仓驱动数据源 ===' as info_type,
    id, 
    name, 
    driver_class_name, 
    conn_url, 
    service_name,
    type
FROM datasource_info 
WHERE driver_class_name = 'com.kingbase8.Driver';

-- 4. 检查租户数据源关联情况
SELECT 
    '=== 租户数据源关联 ===' as info_type,
    t.tenantId,
    t.module,
    t.wrflg as read_write_flag,
    d.name as datasource_name,
    d.driver_class_name,
    d.service_name,
    CONCAT(t.tenantId, d.name) as routing_key
FROM tenant_datasource_rel t
LEFT JOIN datasource_info d ON t.datasource_id = d.id
ORDER BY t.tenantId, d.service_name, t.wrflg;

-- 5. 检查特定服务的数据源配置（替换为实际的服务名）
-- SELECT 
--     '=== 特定服务数据源配置 ===' as info_type,
--     t.tenantId,
--     t.wrflg,
--     d.name,
--     d.driver_class_name,
--     d.conn_url,
--     CONCAT(t.tenantId, d.name) as routing_key
-- FROM tenant_datasource_rel t
-- LEFT JOIN datasource_info d ON t.datasource_id = d.id
-- WHERE d.service_name = 'your-service-name'
-- ORDER BY t.tenantId, t.wrflg;

-- 6. 检查读写数据源分布
SELECT 
    '=== 读写数据源分布 ===' as info_type,
    t.wrflg as read_write_flag,
    d.driver_class_name,
    COUNT(*) as count
FROM tenant_datasource_rel t
LEFT JOIN datasource_info d ON t.datasource_id = d.id
GROUP BY t.wrflg, d.driver_class_name
ORDER BY t.wrflg, d.driver_class_name;

-- 7. 检查可能的路由key冲突
SELECT 
    '=== 可能的路由Key冲突 ===' as info_type,
    CONCAT(t.tenantId, d.name) as routing_key,
    COUNT(*) as count,
    GROUP_CONCAT(d.driver_class_name) as driver_types
FROM tenant_datasource_rel t
LEFT JOIN datasource_info d ON t.datasource_id = d.id
GROUP BY CONCAT(t.tenantId, d.name)
HAVING COUNT(*) > 1;

-- =====================================================
-- 诊断结果说明：
-- 
-- 1. 如果看到MySQL驱动的数据源，这就是问题的根源
-- 2. 检查routing_key，这是动态数据源路由使用的key
-- 3. 特别关注wrflg字段：
--    - 'w' = 写数据源（默认路由，无@DS注解时使用）
--    - 'r' = 读数据源
-- 4. 如果写数据源(wrflg='w')仍然是MySQL驱动，
--    那么所有没有@DS注解的方法都会尝试加载MySQL驱动
-- =====================================================

-- =====================================================
-- 修复"部分功能找MySQL"问题的针对性脚本
-- =====================================================
-- 解决：部分功能可以走人大金仓，部分功能还去找MySQL的问题

-- 执行前请先运行 diagnose_datasource_routing.sql 进行诊断

-- 1. 备份数据（重要！）
CREATE TABLE datasource_info_backup_$(date +%Y%m%d) AS SELECT * FROM datasource_info;
CREATE TABLE tenant_datasource_rel_backup_$(date +%Y%m%d) AS SELECT * FROM tenant_datasource_rel;

-- 2. 查看当前问题数据源（执行前确认）
SELECT 
    '=== 即将更新的MySQL数据源 ===' as action,
    id, 
    name, 
    driver_class_name, 
    conn_url, 
    service_name,
    type
FROM datasource_info 
WHERE driver_class_name IN ('com.mysql.cj.jdbc.Driver', 'com.mysql.jdbc.Driver');

-- 3. 查看受影响的租户关联
SELECT 
    '=== 受影响的租户数据源关联 ===' as action,
    t.tenantId,
    t.wrflg,
    d.name,
    d.driver_class_name,
    CONCAT(t.tenantId, d.name) as routing_key
FROM tenant_datasource_rel t
LEFT JOIN datasource_info d ON t.datasource_id = d.id
WHERE d.driver_class_name IN ('com.mysql.cj.jdbc.Driver', 'com.mysql.jdbc.Driver')
ORDER BY t.tenantId, t.wrflg;

-- 4. 核心修复：更新MySQL驱动为人大金仓驱动
UPDATE datasource_info 
SET driver_class_name = 'com.kingbase8.Driver'
WHERE driver_class_name IN ('com.mysql.cj.jdbc.Driver', 'com.mysql.jdbc.Driver');

-- 5. 更新连接URL（根据实际情况调整）
-- 注意：请根据实际的人大金仓服务器配置修改以下语句

-- 方式1：简单替换（如果URL格式相似）
UPDATE datasource_info 
SET conn_url = REPLACE(conn_url, 'jdbc:mysql://', 'jdbc:kingbase8://')
WHERE conn_url LIKE 'jdbc:mysql://%';

-- 方式2：复杂替换（如果需要更改端口或其他参数）
-- UPDATE datasource_info 
-- SET conn_url = CONCAT(
--     'jdbc:kingbase8://',
--     SUBSTRING_INDEX(SUBSTRING_INDEX(conn_url, '://', -1), '/', 1),
--     '/',
--     SUBSTRING_INDEX(conn_url, '/', -1)
-- )
-- WHERE conn_url LIKE 'jdbc:mysql://%';

-- 6. 验证修复结果
SELECT 
    '=== 修复后的数据源配置 ===' as result,
    id, 
    name, 
    driver_class_name, 
    conn_url, 
    service_name,
    type
FROM datasource_info 
ORDER BY service_name, name;

-- 7. 验证租户路由配置
SELECT 
    '=== 修复后的租户路由配置 ===' as result,
    t.tenantId,
    t.wrflg,
    d.name,
    d.driver_class_name,
    CONCAT(t.tenantId, d.name) as routing_key
FROM tenant_datasource_rel t
LEFT JOIN datasource_info d ON t.datasource_id = d.id
ORDER BY t.tenantId, t.wrflg;

-- 8. 检查是否还有MySQL驱动（应该为空）
SELECT 
    '=== 剩余的MySQL驱动数据源（应该为空） ===' as check_result,
    COUNT(*) as mysql_count
FROM datasource_info 
WHERE driver_class_name IN ('com.mysql.cj.jdbc.Driver', 'com.mysql.jdbc.Driver');

-- =====================================================
-- 修复说明：
-- 
-- 1. 此脚本专门解决"部分功能找MySQL"的问题
-- 2. 重点更新写数据源(wrflg='w')，因为默认路由使用写数据源
-- 3. 修复后，无@DS注解的方法将使用人大金仓数据源
-- 4. 有@DS注解的方法继续正常工作
-- 
-- 执行后需要：
-- 1. 重启应用服务
-- 2. 测试各个功能模块
-- 3. 确认不再出现MySQL驱动错误
-- =====================================================

-- 如果需要回滚，使用以下语句：
-- DELETE FROM datasource_info;
-- INSERT INTO datasource_info SELECT * FROM datasource_info_backup_$(date +%Y%m%d);
-- DELETE FROM tenant_datasource_rel;
-- INSERT INTO tenant_datasource_rel SELECT * FROM tenant_datasource_rel_backup_$(date +%Y%m%d);

-- =====================================================
-- 数据源配置迁移脚本：MySQL -> 人大金仓
-- =====================================================
-- 说明：此脚本用于将动态数据源配置从MySQL驱动迁移到人大金仓驱动
-- 执行前请备份数据库！

-- 1. 查看当前使用MySQL驱动的数据源配置
SELECT 
    id, 
    name, 
    driver_class_name, 
    conn_url, 
    service_name,
    type
FROM datasource_info 
WHERE driver_class_name = 'com.mysql.cj.jdbc.Driver'
   OR driver_class_name = 'com.mysql.jdbc.Driver';

-- 2. 备份当前配置（可选，建议执行）
-- CREATE TABLE datasource_info_backup AS SELECT * FROM datasource_info;

-- 3. 更新驱动类名：MySQL -> 人大金仓
UPDATE datasource_info 
SET driver_class_name = 'com.kingbase8.Driver'
WHERE driver_class_name IN ('com.mysql.cj.jdbc.Driver', 'com.mysql.jdbc.Driver');

-- 4. 更新连接URL格式
-- 注意：这里需要根据实际的人大金仓服务器配置进行调整
-- MySQL格式：**************************************
-- 人大金仓格式：******************************************

-- 示例更新（请根据实际情况修改）：
-- UPDATE datasource_info 
-- SET conn_url = REPLACE(conn_url, 'jdbc:mysql://', 'jdbc:kingbase8://')
-- WHERE conn_url LIKE 'jdbc:mysql://%';

-- 如果需要更复杂的URL转换，可以使用类似以下的语句：
-- UPDATE datasource_info 
-- SET conn_url = CONCAT(
--     'jdbc:kingbase8://',
--     SUBSTRING(conn_url, LOCATE('://', conn_url) + 3)
-- )
-- WHERE conn_url LIKE 'jdbc:mysql://%';

-- 5. 验证更新结果
SELECT 
    id, 
    name, 
    driver_class_name, 
    conn_url, 
    service_name,
    type,
    DTSTAMP
FROM datasource_info 
ORDER BY service_name, name;

-- 6. 检查租户数据源关联表
SELECT 
    t.tenantId,
    t.module,
    t.wrflg,
    d.name as datasource_name,
    d.driver_class_name,
    d.service_name
FROM tenant_datasource_rel t
LEFT JOIN datasource_info d ON t.datasource_id = d.id
WHERE d.driver_class_name = 'com.kingbase8.Driver'
ORDER BY t.tenantId, d.service_name;

-- =====================================================
-- 注意事项：
-- 1. 执行前请确保已经安装了人大金仓数据库驱动
-- 2. 请根据实际的人大金仓服务器配置调整连接URL
-- 3. 建议在测试环境先验证配置正确性
-- 4. 执行后需要重启相关应用服务
-- =====================================================

/*
 Navicat Premium Data Transfer

 Source Server         : ***********
 Source Server Type    : MySQL
 Source Server Version : 80019
 Source Host           : ***********:3306
 Source Schema         : qm_common_dynamic

 Target Server Type    : MySQL
 Target Server Version : 80019
 File Encoding         : 65001

 Date: 03/07/2020 16:47:07
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tenant_datasource_rel
-- ----------------------------
DROP TABLE IF EXISTS `tenant_datasource_rel`;
CREATE TABLE `tenant_datasource_rel`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `tenantId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户code',
  `module` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模块名',
  `datasource_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据源id',
  `wrflg` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '读/写标识 ',
  `DTSTAMP` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_dskey`(`tenantId`, `module`, `wrflg`) USING BTREE,
  INDEX `fk_datasourceid`(`datasource_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '多租户跳转关联表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

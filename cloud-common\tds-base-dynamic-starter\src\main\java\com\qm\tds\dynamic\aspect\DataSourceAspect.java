package com.qm.tds.dynamic.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.qm.tds.dynamic.constant.CommonConstant;
import com.qm.tds.dynamic.constant.DataSourceType;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * 数据源切面(只针对项目下的listener包)
 *
 * <AUTHOR>
 * @since 2020/6/30 9:33
 */
@Order(1)
@Component
@Aspect
@Slf4j
@ConditionalOnProperty(prefix = "spring.datasource.dynamic", name = "primary", havingValue = "tenant")
public class DataSourceAspect {

    @Value("${spring.datasource.dynamic.primary}")
    private String primary;

    @Autowired
    private DataSourceSupporter dataSourceSupporter;

    @Around("execution(* com.qm.ep.*.listener..*(..))||execution(* com.qm.tds.*.listener..*(..))")
    public Object doListenerAround(ProceedingJoinPoint jp) throws Throwable {
        String tenantId = getTenantIdFromRequest();
        if (StringUtils.isBlank(tenantId)) {
            tenantId = getTenantIdFromMQ(jp);
        }
        // 处理动态数据源  开启对租户模式
        return doAround(jp, tenantId);
    }

    private Object doAround(ProceedingJoinPoint jp, String tenantId) throws Throwable {
        // 处理动态数据源  开启对租户模式
        if (DataSourceType.TENANT.equals(primary) && StringUtils.isNotBlank(tenantId)) {
            try {
                //多租户处理
                dataSourceSupporter.dataSourceHandle(tenantId);
                //判断是否存在DS注解
                MethodSignature signature = (MethodSignature) jp.getSignature();
                Method method = signature.getMethod();
                DS dsSource = method.getAnnotation(DS.class);
                if (dsSource != null) {
                    DynamicDataSourceContextHolder.push(tenantId + dsSource.value());
                } else {
                    // 如果方法上没有指定数据源，则使用写数据源
                    DynamicDataSourceContextHolder.push(tenantId + DataSourceType.W);
                }
                return jp.proceed();
            } finally {
                // 销毁数据源 在执行方法之后
                DynamicDataSourceContextHolder.poll();
            }
        }
        return jp.proceed();
    }

    /**
     * 从Request中获取租户ID
     *
     * @return 租户ID
     */
    private String getTenantIdFromRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        String tenantId = null;
        if (null != attributes) {
            HttpServletRequest request = attributes.getRequest();
            // 优先获取请求参数中的tenantId(租户信息)值
            tenantId = request.getParameter(CommonConstant.TENANT_ID_PARAM);
            if (StringUtils.isBlank(tenantId)) {
                tenantId = request.getHeader(CommonConstant.TENANT_ID_PARAM);
            }
        }
        return tenantId;
    }

    /**
     * 从MQ中获取租户ID
     *
     * @return 租户ID
     */
    private String getTenantIdFromMQ(ProceedingJoinPoint jp) throws NoSuchFieldException, IllegalAccessException {
        String tenantId = null;
        Object[] args = jp.getArgs();
        for (Object object : args) {
            if (CommonConstant.MESSAGE_STRUCT.equals(object.getClass().getName())) {
                log.debug("MQ接收数据：" + object);
                Class<?> meassageStruct = object.getClass();
                //获取请求信息
                Field field = meassageStruct.getDeclaredField(CommonConstant.REQUEST_INFO);
                field.setAccessible(true);
                //处理请求信息
                try {
                    Object requestInfo = field.get(object);
                    JSONObject json = (JSONObject) JSON.toJSON(requestInfo);
                    tenantId = json.containsKey(CommonConstant.TENANT_ID_PARAM) ? json.getString(CommonConstant.TENANT_ID_PARAM) : "";
                } catch (Exception e) {
                    log.info("---error--"+e.getMessage(), e);
                }
            }
        }
        return tenantId;
    }
}

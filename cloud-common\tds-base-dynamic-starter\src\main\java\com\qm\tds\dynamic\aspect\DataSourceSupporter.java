package com.qm.tds.dynamic.aspect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.creator.DataSourceCreator;
import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.creator.druid.DruidConfig;
import com.baomidou.dynamic.datasource.creator.hikaricp.HikariCpConfig;
import com.baomidou.dynamic.datasource.ds.ItemDataSource;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.dynamic.constant.CommonConstant;
import com.qm.tds.dynamic.domain.dto.TenantDataSourceDTO;
import com.qm.tds.dynamic.domain.vo.TenantDataSourceVO;
import com.qm.tds.dynamic.service.TenantService;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.RedisUtils;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 多数据源 帮助工具类
 *
 * <AUTHOR>
 * @date 2021/3/11 18:25
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "spring.datasource.dynamic", name = "primary", havingValue = "tenant")
public class DataSourceSupporter {

    @Value("${spring.application.name}")
    private String serviceName;

    private final RedisUtils redisUtils;

    private final TenantService tenantService;

    private final I18nUtil i18nUtil;

    /**
     * Druid数据源创建器
     */
    private final DataSourceCreator druidDataSourceCreator;


    /**
     * hikari数据源创建器
     */
    private final DataSourceCreator hikariDataSourceCreator;

    /**
     * 数据源yaml配置信息
     */
    private final DynamicDataSourceProperties properties;

    private final DataSource dataSource;

    public DataSourceSupporter(RedisUtils redisUtils,
                               TenantService tenantService,
                               DataSourceCreator druidDataSourceCreator,
                               DataSourceCreator hikariDataSourceCreator,
                               DynamicDataSourceProperties properties,
                               DataSource dataSource,
                               I18nUtil i18nUtil) {
        this.redisUtils = redisUtils;
        this.tenantService = tenantService;
        this.druidDataSourceCreator = druidDataSourceCreator;
        this.hikariDataSourceCreator = hikariDataSourceCreator;
        this.properties = properties;
        this.dataSource = dataSource;
        this.i18nUtil = i18nUtil;
    }

    /**
     * 处理数据源,这里只缓多租户的数据源信息
     * {@link DynamicRoutingDataSource}存放了所有可跳转数据源
     * {@link DynamicDataSourceContextHolder}按组指定使用哪个(组)数据源
     *
     * <AUTHOR>
     * @date 2020/7/24 8:23
     */
    public void dataSourceHandle(String tenantId) throws IllegalAccessException {
        // 先读取缓存数据
        String key = redisUtils.keyBuilder(CommonConstant.DYNAMIC_TENANT, serviceName, tenantId);
        List<TenantDataSourceVO> list = null;
        boolean redisError = false;
        try {
            list = CollUtil.emptyIfNull((List<TenantDataSourceVO>) redisUtils.get(key));
        } catch (Exception e) {
            redisError = true;
        }
        // 缓存数据为空，从数据库获取数据源
        if (CollUtil.isEmpty(list)) {
            // 获取数据源
            TenantDataSourceDTO tempDTO = new TenantDataSourceDTO();
            tempDTO.setTenantId(tenantId);
            tempDTO.setServiceName(serviceName);
            // 为了多数据源的正确性，如下方法有注解 @DS(DataSourceType.TENANT) 保证多数据源正确性
            list = tenantService.selectDatasourceByTenantId(tempDTO);
            // 如果Redis连接失败，则不存入缓存，每次都从数据库获取数据源
            if (!redisError) {
                redisUtils.set(key, list);
            }
        }
        // 兼容tds做出调整
        DynamicRoutingDataSource routing = DataSourceSupporterCache.getDynamicRoutingDataSource(dataSource);
        // 黑名单数据源处理
        String blackKey = redisUtils.keyBuilder(CommonConstant.DYNAMIC_TENANT, serviceName + CommonConstant.BLACK, tenantId);
        Map<String, Object> blackMap;
        if (!redisError) {
            blackMap = MapUtil.defaultIfEmpty((Map<String, Object>) redisUtils.get(blackKey), MapUtil.newHashMap(8));
        } else {
            blackMap = MapUtil.newHashMap(8);
        }
        // 处理数据源,决定是否加入黑名单
        for (TenantDataSourceVO dsInDb : list) {
            if (blackMap.containsKey(dsInDb.getName())) {
                // 已经在黑名单中，直接跳过。
                continue;
            }
            if (!this.needChangeDataSource(dsInDb, routing)) {
                // 数据源一致，不需要更换数据源，以便节省性能
                continue;
            }
            // 真正将数据源加入到DynamicRoutingDataSource中
            if (!this.addDS2Router(dsInDb, routing)) {
                blackMap.put(dsInDb.getName(), tenantId);
                // 创建数据源失败，放入黑名单缓存，后续不再执行。
                // 存入缓存 60*60
                if (!redisError) {
                    redisUtils.set(blackKey, blackMap, 3600);
                }
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            // 不存在数据源配置,请检查租户信息或数据源配置
            log.info("[-DataSourceSupporter-].dataSourceHandle:tenantId={} not exist", tenantId);
            throw new QmException(i18nUtil.getMessage("qm_common_dynamic中需要配置该服务对应的租户数据源信息"));
        }

    }

    /**
     * 是否需要更换数据源
     *
     * @param dsInDb  数据库中存储的租户数据源信息（dataSourceInDataBase）
     * @param routing 数据源
     * @return true：需要；false：不需要
     */
    private boolean needChangeDataSource(TenantDataSourceVO dsInDb, DynamicRoutingDataSource routing) {
        Map<String, DataSource> routingDS = routing.getDataSources();
        String routerKeyInDb = dsInDb.getTenantId() + dsInDb.getName();
        // 判断是否需要修改(当前数据源router中不包含 传进来的数据源)
        if (!routingDS.containsKey(routerKeyInDb)) {
            return true;
        }
        DataSource realDataSource = routingDS.get(routerKeyInDb);
        // 数据源不相同则需要更换数据源
        // DataSource realDataSource = dsInRouter.getRealDataSource();
        Class<? extends DataSource> type = realDataSource.getClass();
        if (type == null || !type.getName().equals(dsInDb.getType())) {
            return true;
        }
        switch (type.getName()) {
            case CommonConstant.DRYUDDATASOURCE:
                DruidDataSource dsInRouterDruid = (DruidDataSource) realDataSource;
                return !dsInDb.getUsername().equals(dsInRouterDruid.getUsername())
                        || !dsInDb.getPassword().equals(dsInRouterDruid.getPassword())
                        || !dsInDb.getConnUrl().equals(dsInRouterDruid.getUrl())
                        || !dsInDb.getDriverClassName().equals(dsInRouterDruid.getDriverClassName());
            case CommonConstant.HIKARIDATASOURCE:
                HikariDataSource dsInRouterHikari = (HikariDataSource) realDataSource;
                return !dsInDb.getUsername().equals(dsInRouterHikari.getUsername())
                        || !dsInDb.getPassword().equals(dsInRouterHikari.getPassword())
                        || !dsInDb.getConnUrl().equals(dsInRouterHikari.getJdbcUrl())
                        || !dsInDb.getDriverClassName().equals(dsInRouterHikari.getDriverClassName());
            default:
                return false;
        }
    }

    /**
     * 关键方法：添加数据源到router中
     *
     * @param dsInDb  数据库中存储的租户数据源信息（dataSourceInDataBase）
     * @param routing 数据源
     * @return 添加是否成功
     */
    private boolean addDS2Router(TenantDataSourceVO dsInDb, DynamicRoutingDataSource routing) {
        DataSourceProperty dsProperty = new DataSourceProperty();
        try {
            dsProperty.setUrl(dsInDb.getConnUrl());
            dsProperty.setDriverClassName(dsInDb.getDriverClassName());
            dsProperty.setPoolName(dsInDb.getName());
            dsProperty.setUsername(dsInDb.getUsername());
            dsProperty.setPassword(dsInDb.getPassword());
            // 字符串类型type转class
            Class<? extends DataSource> dsInDbTypeClass = DataSourceSupporterCache.getSourceClass(dsInDb.getType());
            if (dsInDbTypeClass != null) {
                dsProperty.setType(dsInDbTypeClass);
            }
            switch (dsInDb.getType()) {
                case CommonConstant.DRUID_TYPE:
                    DruidConfig druid = properties.getDruid();
                    log.debug("[-DataSourceSupporter-].addDS2Router:druid config={}", druid);
                    if (druid != null) {
                        dsProperty.setDruid(druid);
                    }
                    break;
                case CommonConstant.HIKARIDATASOURCE:
                    HikariCpConfig hikari = properties.getHikari();
                    log.debug("[-DataSourceSupporter-].addDS2Router:hikari config={}", hikari);
                    if (hikari != null) {
                        dsProperty.setHikari(hikari);
                    }
                    break;
                default:
            }
            String routingKey = dsInDb.getTenantId() + dsInDb.getName();
            // 使用routingKey作为分级锁
            synchronized (routingKey.intern()) {
                DataSource dataSource = null;
                if (dsInDb.getType().equals(CommonConstant.DRUID_TYPE)) {
                    dataSource = druidDataSourceCreator.createDataSource(dsProperty);
                }
                else if (dsInDb.getType().equals(CommonConstant.HIKARIDATASOURCE)) {
                    dataSource = hikariDataSourceCreator.createDataSource(dsProperty);
                }
                // 先删除
                routing.removeDataSource(routingKey);
                // 后增加
                routing.addDataSource(routingKey, dataSource);
            }
            log.debug("[-CommonAspect-].addDS2Router:={}", routingKey);
        } catch (Exception e) {
            log.error("---error--" + "[-CommonAspect-].addDS2Router:dsInDb={}", dsInDb, e);
            return false;
        }
        return true;
    }

    /**
     * 获取当前工程的多数据源信息
     *
     * @return 当前工程的多数据源信息
     */
    public List<Map<String, String>> getAllDataSource() throws IllegalAccessException {
        DynamicRoutingDataSource dynamicRoutingDataSource = DataSourceSupporterCache.getDynamicRoutingDataSource(this.dataSource);
        Map<String, DataSource> currentDataSources = dynamicRoutingDataSource.getDataSources();
        return currentDataSources.keySet().stream().map(dataSourceKey -> {
            DataSource single = currentDataSources.get(dataSourceKey);
            if (single instanceof ItemDataSource) {
                ItemDataSource item = (ItemDataSource) single;
                DataSource realDataSource = item.getRealDataSource();
                String jdbcUrl;
                String dataSourceType;
                if (realDataSource instanceof DruidDataSource) {
                    jdbcUrl = ((DruidDataSource) realDataSource).getUrl();
                    dataSourceType = "DruidDataSource";
                } else if (realDataSource instanceof HikariDataSource) {
                    jdbcUrl = ((HikariDataSource) realDataSource).getJdbcUrl();
                    dataSourceType = "HikariDataSource";
                } else {
                    // "未知的数据源类型";
                    jdbcUrl = i18nUtil.getMessage("ERR.dyna.DataSourceSupporter.unknowType");
                    dataSourceType = realDataSource.getClass().getSimpleName();
                }
                return MapUtil.builder("name", item.getName())
                        .put("jdbcUrl", jdbcUrl)
                        .put("dataSourceType", dataSourceType)
                        .build();
            }
            return MapUtil.builder("name", dataSourceKey).build();
        }).collect(Collectors.toList());
    }
}

package com.qm.tds.dynamic.aspect;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.util.SpringContextHolder;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.WeakHashMap;

/**
 * DataSourceSupporter通过缓存提升性能的辅助类
 * 为节省性能而做出的设计
 *
 * <AUTHOR>
 * @date 2021/3/18 14:43
 */
@Slf4j
public class DataSourceSupporterCache {

    /**
     * 反射缓存
     */
    private static final WeakHashMap<Class<?>, Field> FIELD_CACHE = new WeakHashMap<>();

    /**
     * 反射缓存
     */
    private static Class<?> TJDS_CACHE = null;

    /**
     * 反射缓存 类缓存池
     */
    private static final Map<String, Class<?>> CLASS_CACHE = new HashMap<>();

    /**
     * 反射缓存 数据源缓存池
     */
    private static final Map<String, Class<? extends DataSource>> DATA_SOURCE_CLASS_CACHE = new HashMap<>();

    /**
     * 反射暴力取出dataSource字段并强转为DynamicRoutingDataSource
     * 因TraceJdbcDataSource的dataSource字段为私有变量
     *
     * @return DynamicRoutingDataSource数据
     * @throws NoSuchFieldException 无此字段
     */
    public static DynamicRoutingDataSource getDynamicRoutingDataSource(DataSource dataSource) throws IllegalAccessException {
        // DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;
        //兼容tds做出调整
        if (TJDS_CACHE == null) {
            TJDS_CACHE = dataSource.getClass();
        }
        //获取请求信息
        Field field = FIELD_CACHE.get(TJDS_CACHE);
        if (field == null) {
            //获取请求信息
            try {
                field = TJDS_CACHE.getDeclaredField("dataSource");
            } catch (NoSuchFieldException e) {
                if (dataSource instanceof DynamicRoutingDataSource) {
                    return (DynamicRoutingDataSource) dataSource;
                }
            }
            field.setAccessible(true);
            FIELD_CACHE.put(TJDS_CACHE, field);
            log.debug("[-DataSourceAspect-].doAround:缓存field={}", FIELD_CACHE);
        }
        return (DynamicRoutingDataSource) field.get(dataSource);
    }

    /**
     * 为减少反射带来的不必要的系统开销，使用简单赋值以提高性能
     *
     * @param type 类全名称字符串
     * @return 当是数据源类时，返回class  当不是数据源类，返回null
     */
    public static Class<? extends DataSource> getSourceClass(String type) {
        Class<? extends DataSource> targetClass = DATA_SOURCE_CLASS_CACHE.get(type);
        // 数据源缓存池中命中，直接返回clazz对象
        if (targetClass != null) {
            return targetClass;
        }
        Class<?> tempClass = CLASS_CACHE.get(type);
        if (tempClass == null) {
            // 类缓存池中未命中，clazz对象放入类缓存池
            try {
                tempClass = Class.forName(type);
            } catch (ClassNotFoundException e) {
                // 为了减少穿透带来的危险，此处向类缓存池中放入了错误的类
                CLASS_CACHE.put(type, Integer.class);
                // 数据库存储的Type字段错误,入参type:
                String message = getMessage("ERR.dyna.DataSourceSupporterCache.typeError");
                throw new QmException(message + type, e);
            }
            CLASS_CACHE.put(type, tempClass);
        }
        if (DataSource.class.isAssignableFrom(tempClass)) {
            // 属于数据源类型 放入数据源缓存池 并返回当前类对象
            //noinspection unchecked
            targetClass = (Class<? extends DataSource>) tempClass;
            DATA_SOURCE_CLASS_CACHE.put(type, targetClass);
            return targetClass;
        } else {
            if (tempClass.equals(Integer.class)) {
                // 穿透屏蔽处的逻辑警告
                // 数据库存储的Type字段错误,入参type:
                String message = getMessage("ERR.dyna.DataSourceSupporterCache.typeError");
                log.info("---error--"+message, type);
            }
            return null;
        }
    }

    public static String getMessage(String key) {
        String message = null;
        try {
            I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
            message = i18nUtil.getMessage(key);
        } catch (Exception e) {
            e.printStackTrace();
            return key;
        }
        return message;
    }
}

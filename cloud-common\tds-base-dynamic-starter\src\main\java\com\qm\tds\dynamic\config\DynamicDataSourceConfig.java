package com.qm.tds.dynamic.config;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor;
import com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationInterceptor;
import com.baomidou.dynamic.datasource.creator.DataSourceCreator;
import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.processor.DsProcessor;
import com.baomidou.dynamic.datasource.provider.DynamicDataSourceProvider;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DruidDynamicDataSourceConfiguration;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.dynamic.datasource.strategy.DynamicDataSourceStrategy;
import com.qm.tds.dynamic.constant.DataSourceType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.Map;

/**
 * 动态数据源配置
 * {@link com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration}
 *
 * <AUTHOR>
 * @see DynamicDataSourceProvider
 * @see DynamicDataSourceStrategy
 * @see DynamicRoutingDataSource
 * @since 2021/3/11 9:37
 */
@Slf4j
@Configuration
@AllArgsConstructor
@EnableConfigurationProperties(DynamicDataSourceProperties.class)
@AutoConfigureBefore(DataSourceAutoConfiguration.class)
@Import(value = {DruidDynamicDataSourceConfiguration.class, DynamicDataSourceCreatorAutoConfiguration.class})
@ConditionalOnProperty(prefix = DynamicDataSourceProperties.PREFIX, name = "enabled", havingValue = "true", matchIfMissing = true)
@MapperScan({"com.qm.tds.dynamic.mapper"})
public class DynamicDataSourceConfig {

    private final DynamicDataSourceProperties properties;

    @Bean
    @ConditionalOnProperty(prefix = "spring.datasource.dynamic", name = "primary", havingValue = "tenant")
    public DynamicDataSourceAnnotationInterceptor dynamicDataSourceInterceptor(DsProcessor dsProcessor) {
        return new TenantDynamicDataSourceInterceptor(properties.getAop().getAllowedPublicOnly(), dsProcessor);
    }
    /**
     * interceptor获取动态数据源并判断和使用
     * 增加了advisor中增加Pointcut配置
     */
    @Bean
    @ConditionalOnProperty(prefix = "spring.datasource.dynamic", name = "primary", havingValue = "tenant")
    public DynamicDataSourceAnnotationAdvisor dynamicDatasourceAnnotationAdvisor(DsProcessor dsProcessor) {
        DynamicDataSourceAnnotationInterceptor interceptor = this.dynamicDataSourceInterceptor(dsProcessor);
        DynamicDataSourceAnnotationAdvisor advisor = new TenantDynamicDataSourceAdvisor(interceptor);
        advisor.setOrder(properties.getAop().getOrder());
        return advisor;
    }



    @Bean
    @ConditionalOnProperty(prefix = "spring.datasource.dynamic", name = "primary", havingValue = "tenant")
    public DynamicDataSourceProvider dynamicDataSourceProvider(DataSourceCreator druidDataSourceCreator, DataSourceCreator hikariDataSourceCreator) {
        DataSourceProperty tenant = new DataSourceProperty();
        // 获取yaml中配置的租户数据源配置datasource
        Map<String, DataSourceProperty> datasourceMap = properties.getDatasource();
        for (Map.Entry<String, DataSourceProperty> entry : datasourceMap.entrySet()) {
            String dsName=entry.getKey();
            DataSourceProperty dataSourceProperty=entry.getValue();
            if (DataSourceType.TENANT.equals(dsName)) {
                tenant = dataSourceProperty;
            }
        }
        // 使用tenant数据源，创建动态数据源提供者代替MP原生的YmlDynamicDataSourceProvider(@ConditionalOnMissingBean)，
        // 目的在于自定义加载数据源的方式
        return new TenantDynamicDataSourceProvider(tenant, properties,druidDataSourceCreator,hikariDataSourceCreator);
    }
}

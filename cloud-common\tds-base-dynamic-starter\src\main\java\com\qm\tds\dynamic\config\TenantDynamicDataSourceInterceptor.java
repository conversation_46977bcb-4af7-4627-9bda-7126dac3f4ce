/**
 * Copyright © 2018 organization baomidou
 * <pre>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * <pre/>
 */
package com.qm.tds.dynamic.config;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationInterceptor;
import com.baomidou.dynamic.datasource.processor.DsProcessor;
import com.baomidou.dynamic.datasource.support.DataSourceClassResolver;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.qm.tds.dynamic.aspect.DataSourceSupporter;
import com.qm.tds.dynamic.constant.CommonConstant;
import com.qm.tds.dynamic.constant.DataSourceType;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInvocation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;

/**
 * Core Interceptor of Dynamic Datasource
 * {@link DynamicDataSourceAnnotationInterceptor}
 *
 * <AUTHOR>
 * @since 2021/3/17 12:20
 */
@Slf4j
public class TenantDynamicDataSourceInterceptor extends DynamicDataSourceAnnotationInterceptor {

    private static final String DYNAMIC_PREFIX = "#";

    private final DataSourceClassResolver dataSourceClassResolver;

    private final DsProcessor dsProcessor;

    @Value("${spring.datasource.dynamic.primary}")
    private String primary;

    @Autowired(required = false)
    private DataSourceSupporter dataSourceSupporter;

    public TenantDynamicDataSourceInterceptor(Boolean allowedPublicOnly, DsProcessor dsProcessor) {
        super(allowedPublicOnly, dsProcessor);
        dataSourceClassResolver = new DataSourceClassResolver(allowedPublicOnly);
        this.dsProcessor = dsProcessor;
    }


    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        try {
            String dsKey = this.determineDatasource(invocation);
            DynamicDataSourceContextHolder.push(dsKey);
            return invocation.proceed();
        } finally {
            DynamicDataSourceContextHolder.poll();
        }
    }

    /**
     * 关键方法-决定数据源的key
     */
    private String determineDatasource(MethodInvocation invocation) throws Throwable {
        Method method = invocation.getMethod();
        String groupKey;
        if (DataSourceType.TENANT.equals(primary)) {
            String tenantId = this.getTenantId();
            // 处理动态数据源  开启对租户模式
            if (StringUtils.isNotBlank(tenantId)) {
                dataSourceSupporter.dataSourceHandle(tenantId);

                // 判断是否存在DS注解
                DS ds = method.getAnnotation(DS.class);
                if (ds != null) {
                    // 这几种DS不区分租户
                    if (ds.value().equals(DataSourceType.TENANT) ||
                            ds.value().equals(DataSourceType.MASTER) ||
                            ds.value().equals(DataSourceType.SLAVE)) {
                        groupKey = determineDatasourceKey(invocation);
                    } else if (ds.value().startsWith("#")) {

                        String key = ds.value();
                        DefaultParameterNameDiscoverer parameterNameDiscoverer= new DefaultParameterNameDiscoverer();
                        SpelExpressionParser parserSpel = new SpelExpressionParser();
                        // 解析 SpEL 表达式
                        Expression expression = parserSpel.parseExpression(key);

                        // 创建一个标准的评估上下文
                        EvaluationContext context = new StandardEvaluationContext();


                        // 获取方法参数值
                        Object[] args = invocation.getArguments();

                        // 获取方法参数名称
                        String[] paramNames = parameterNameDiscoverer.getParameterNames(method);

                        // 将方法参数值设置到评估上下文中
                        for (int i = 0; i < args.length; i++) {
                            context.setVariable(paramNames[i], args[i]);
                        }

                        // 根据 SpEL 表达式计算出结果并转换为字符串
                        String string = expression.getValue(context).toString();
                        log.info("SPEL DataSource Key is [{}]", string);
                        return string;
                    } else {

                        // 获取并修改key为租户+DS值，例如:15w
                        groupKey = tenantId + ds.value();
                    }
                } else {
                    // 添加DS注解并赋值
                    groupKey = tenantId + DataSourceType.W;
                }
            } else {
                groupKey = determineDatasourceKey(invocation);
            }
        } else {
            groupKey = determineDatasourceKey(invocation);
        }
        log.info("Current DataSource Key is [{}]", groupKey);

        return groupKey;
    }


    /**
     * 根据方法上的DS注解获取数据源key，如果不是SPEL就是取的DS的值
     * 未加DS会走到primary对应的数据源
     *
     * @param invocation 方法元数据
     * @return {@link String }
     */
    private String determineDatasourceKey(MethodInvocation invocation) {
        String key = dataSourceClassResolver.findKey(invocation.getMethod(), invocation.getThis(), DS.class);
        return (StringUtils.isNotBlank(key) && key.startsWith(DYNAMIC_PREFIX)) ? dsProcessor.determineDatasource(invocation, key) : key;
    }

    /**
     * 从请求中获取tenantId
     *
     * @return 租户id
     */
    private String getTenantId() {
        String tenantId = null;
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null != attributes) {
            HttpServletRequest request = attributes.getRequest();
            // 优先获取请求参数中的tenantId(租户信息)值
            tenantId = request.getParameter(CommonConstant.TENANT_ID_PARAM);
            if (StringUtils.isBlank(tenantId)) {
                tenantId = request.getHeader(CommonConstant.TENANT_ID_PARAM);
            }
        }
        return tenantId;
    }
}
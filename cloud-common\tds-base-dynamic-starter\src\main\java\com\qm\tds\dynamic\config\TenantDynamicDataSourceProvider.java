package com.qm.tds.dynamic.config;

import cn.hutool.json.JSONUtil;
import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.dynamic.datasource.creator.DataSourceCreator;
import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.provider.DynamicDataSourceProvider;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.dynamic.aspect.DataSourceSupporterCache;
import com.qm.tds.dynamic.constant.CommonConstant;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.support.JdbcUtils;

import javax.sql.DataSource;
import java.sql.*;
import java.util.HashMap;
import java.util.Map;


/**
 * 数据源构建器-从租户库读数据源信息并合并配置文件中的数据源信息，构建数据源Map，key为数据源名称，value为数据源对象
 *
 * <AUTHOR>
 */
@Slf4j
public class TenantDynamicDataSourceProvider implements DynamicDataSourceProvider {

    private final String driverClassName;

    private final String url;

    private final String username;

    private final String password;

    @Value("${spring.application.name}")
    private String applicationName;


    private final DataSourceCreator druidDataSourceCreator;


    private final DataSourceCreator hikariDataSourceCreator;


    private final DynamicDataSourceProperties properties;

    public TenantDynamicDataSourceProvider(DataSourceProperty tenant, DynamicDataSourceProperties properties,
                                           DataSourceCreator druidDataSourceCreator, DataSourceCreator hikariDataSourceCreator) {
        this.driverClassName = tenant.getDriverClassName();
        this.url = tenant.getUrl();
        this.username = tenant.getUsername();
        this.password = tenant.getPassword();
        this.druidDataSourceCreator = druidDataSourceCreator;
        this.hikariDataSourceCreator = hikariDataSourceCreator;
        this.properties = properties;
    }

    @Override
    public Map<String, DataSource> loadDataSources() {
        Connection conn = null;
        Statement stmt = null;
        try {

            if (StringUtils.isNotBlank(driverClassName)) {
                Class.forName(driverClassName);
                log.info("成功加载数据库驱动程序");
            }
            // 链接动态数据源数据库(例如.qm_common_dynamic)
            conn = DriverManager.getConnection(url, username, password);
            log.info("成功连接租户数据源数据库：{}", url);
            stmt = conn.createStatement();

            // 执行查询语句获得数据源参数
            Map<String, DataSourceProperty> dataSourcePropertiesMap = this.executeStmt(stmt);
            // 创建数据源Map
            return this.createDataSourceMap(dataSourcePropertiesMap);

        } catch (Exception e) {
            log.info("---error--" + "数据库连接失败，请检查连接的基本参数url、username、password，确保连接有效性", e);
        } finally {
            JdbcUtils.closeConnection(conn);
            JdbcUtils.closeStatement(stmt);
        }
        return null;
    }


    /**
     * 执行语句获得数据源参数
     *
     * @param statement 语句
     * @return 数据源参数
     * @throws SQLException sql异常
     */
    protected Map<String, DataSourceProperty> executeStmt(Statement statement) throws SQLException {
        Map<String, DataSourceProperty> map = new HashMap<>(32);
        Map<String, DataSourceProperty> datasourceMap = properties.getDatasource();
        try (ResultSet resultSet = statement.executeQuery("select " +
                " a.tenantId, " +
                " a.module, " +
                " a.datasource_id as datasourceId, " +
                " a.wrflg, " +
                " a.id, " +
                " b.name, " +
                " b.conn_url as connUrl, " +
                " b.username, " +
                " b.password, " +
                " b.driver_class_name as driverClassName, " +
                " b.ds_properties as dsProperties, " +
                " b.remarks, " +
                " b.service_name as serviceName, " +
                " b.type " +
                " from tenant_datasource_rel  a " +
                " left join datasource_info b " +
                " on a.datasource_id = b.id " +
                " where service_name = '" + applicationName + "'")) {
            // 解析结果集,遍历每一行
            while (resultSet.next()) {
                String connUrl = resultSet.getString("connUrl");
                String driverClassName = resultSet.getString("driverClassName");
                String name = resultSet.getString("name");
                String username = resultSet.getString("username");
                String password = resultSet.getString("password");
                String type = resultSet.getString("type");
                String tenantId = resultSet.getString("tenantId");
                String poolName = tenantId + name;
                DataSourceProperty dsProperty = new DataSourceProperty();
                dsProperty.setUrl(connUrl);
                dsProperty.setDriverClassName(driverClassName);
                dsProperty.setPoolName(poolName);
                dsProperty.setUsername(username);
                dsProperty.setPassword(password);
                // 字符串类型type转class
                Class<? extends DataSource> dsInDbTypeClass = DataSourceSupporterCache.getSourceClass(type);
                if (dsInDbTypeClass != null) {
                    dsProperty.setType(dsInDbTypeClass);
                }
                // 加载数据连接池配置信息
                switch (type) {

                    case CommonConstant.DRUID_TYPE -> dsProperty.setDruid(properties.getDruid());

                    case CommonConstant.HIKARIDATASOURCE -> dsProperty.setHikari(properties.getHikari());

                    default -> {
                        String message = "ERROR: TenantDynamicDataSourceProvider.unknownDSType:" + type;
                        throw new QmException(message);
                    }
                }
                map.put(poolName, dsProperty);
            }
        }
        // 将yaml中配置的数据源和数据库中配置的数据源合并
        map.putAll(datasourceMap);
        log.info("最终的数据源参数:TenantDynamicDataSourceProvider.executeStmt:map = {}", JSONUtil.toJsonStr(datasourceMap));
        return map;
    }

    /**
     * 构建数据源Map
     *
     * @param dataSourcePropertiesMap 数据源参数
     * @return {@link Map}<{@link String}, {@link DataSource}>
     */
    private Map<String, DataSource> createDataSourceMap(Map<String, DataSourceProperty> dataSourcePropertiesMap) {
        Map<String, DataSource> dataSourceMap = new HashMap<>(dataSourcePropertiesMap.size() * 2);

        for (Map.Entry<String, DataSourceProperty> item : dataSourcePropertiesMap.entrySet()) {
            DataSourceProperty dataSourceProperty = item.getValue();
            String poolName = item.getKey();
            if (StringUtils.isBlank(poolName)) {
                throw new QmException("数据源名称不能为空");
            }
            Class<? extends DataSource> type = dataSourceProperty.getType();
            // 如果type是DruidDataSource，则使用DruidDataSourceCreator创建，如果type是HikariDataSource，则使用HikariDataSourceCreator创建
            DataSource dataSource;
            if (type.equals(DruidDataSource.class)) {
                dataSource = druidDataSourceCreator.createDataSource(dataSourceProperty);
            } else if (type.equals(HikariDataSource.class)) {
                dataSource = hikariDataSourceCreator.createDataSource(dataSourceProperty);
            } else {
                throw new QmException("不支持的数据源类型: " + type.getName());
            }
            dataSourceMap.put(poolName, dataSource);
        }
        log.info("[最终的数据源Map].createDataSourceMap:dataSourceMap={}", JSONUtil.toJsonStr(dataSourceMap));
        return dataSourceMap;
    }
}

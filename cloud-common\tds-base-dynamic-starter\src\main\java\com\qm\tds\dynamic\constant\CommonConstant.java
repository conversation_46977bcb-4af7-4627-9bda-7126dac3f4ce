package com.qm.tds.dynamic.constant;

/**
 * 全局公共常量
 *
 * <AUTHOR>
 * @date 2020/06/29
 */
public final class CommonConstant {

    private CommonConstant() {
    }

    /**
     * 租户id参数
     */
    public static final String TENANT_ID_PARAM = "tenantId";

    /**
     * 请求header中信息
     */
    public static final String REQUEST_INFO = "requestInfo";

    /**
     * 队列消息体class
     */
    public static final String MESSAGE_STRUCT = "com.qm.tds.mq.builder.MessageStruct";



    /**
     * druid数据源类型
     */
    public static final String DRUID_TYPE = "com.alibaba.druid.pool.DruidDataSource";

    /**
     * 黑色标识
     */
    public static final String BLACK = "-BLACK";

    /**
     * redis动态数据源目录
     */
    public static final String DYNAMIC_TENANT = "DYNAMIC-TENAN";

    /**
     * HikariDataSource数据源
     */
    public static final String HIKARIDATASOURCE = "com.zaxxer.hikari.HikariDataSource";

    /**
     * DruidDataSource数据源
     */
    public static final String DRYUDDATASOURCE = "com.alibaba.druid.pool.DruidDataSource";

}

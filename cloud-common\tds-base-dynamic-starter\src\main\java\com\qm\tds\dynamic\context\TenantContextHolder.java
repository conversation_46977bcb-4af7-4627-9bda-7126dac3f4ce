package com.qm.tds.dynamic.context;

import org.springframework.core.NamedInheritableThreadLocal;

/**
 * 租户holder
 *
 * <AUTHOR>
 * @date 2020/6/29
 */
public class TenantContextHolder {
    private TenantContextHolder() {
    }

    /**
     * 支持父子线程之间的数据传递
     */
    private static final ThreadLocal<String> CONTEXT = new NamedInheritableThreadLocal<>("tenantIdHolder");

    public static void setTenant(String tenant) {
        CONTEXT.set(tenant);
    }

    public static String getTenant() {
        return CONTEXT.get();
    }

    public static void clear() {
        CONTEXT.remove();
    }
}

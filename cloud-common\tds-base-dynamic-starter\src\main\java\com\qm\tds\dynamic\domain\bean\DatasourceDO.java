package com.qm.tds.dynamic.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-30
 */
@Data
@TableName("datasource_info")
@Schema(description = "数据表")
public class DatasourceDO implements Serializable {

    private static final long serialVersionUID = -8586013306277179352L;

    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "数据源名称")
    @TableField("name")
    private String name;

    @Schema(description = "数据源连接地址")
    @TableField("conn_url")
    private String connUrl;

    @Schema(description = "数据源账号")
    @TableField("username")
    private String username;

    @Schema(description = "数据源密码")
    @TableField("password")
    private String password;

    @Schema(description = "数据库驱动名")
    @TableField("driver_class_name")
    private String driverClassName;

    @Schema(description = "数据源的其他配置")
    @TableField("ds_properties")
    private String dsProperties;

    @Schema(description = "备注")
    @TableField("remarks")
    private String remarks;

    @Schema(description = "微服务名称")
    @TableField("service_name")
    private String serviceName;

    @Schema(description = "连接池类型")
    @TableField("type")
    private String type;


    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;


}

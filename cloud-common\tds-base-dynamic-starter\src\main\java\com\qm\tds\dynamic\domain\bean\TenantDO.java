package com.qm.tds.dynamic.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 多租户跳转关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-30
 */
@Data
@TableName("tenant_datasource_rel")
@Schema(description = "多租户跳转关联表")
public class TenantDO implements Serializable {

    private static final long serialVersionUID = 3558416626275694867L;

    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "租户code")
    @TableField("tenantId")
    private String tenantId;

    @Schema(description = "模块名")
    @TableField("module")
    private String module;

    @Schema(description = "数据源id")
    @TableField("datasource_id")
    private String datasourceId;

    @Schema(description = "读/写标识 ")
    @TableField("wrflg")
    private String wrflg;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "数据源名称 ")
    @TableField(exist = false)
    private String dataSourceName;

    @Schema(description = "微服务名称 ")
    @TableField(exist = false)
    private String serviceName;

}

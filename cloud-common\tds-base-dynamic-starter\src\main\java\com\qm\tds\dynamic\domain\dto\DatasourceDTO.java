package com.qm.tds.dynamic.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-30
 */
@Schema(description = "数据表")
@Data
public class DatasourceDTO extends JsonParamDto implements Serializable {

    private static final long serialVersionUID = 8752330391125578388L;

    @Schema(description = "主键")
    private String id;
    @Schema(description = "数据源名称")
    private String name;
    @Schema(description = "数据源连接地址")
    private String connUrl;
    @Schema(description = "数据源账号")
    private String username;
    @Schema(description = "数据源密码")
    private String password;
    @Schema(description = "数据库驱动名")
    private String driverClassName;
    @Schema(description = "数据源的其他配置")
    private String dsProperties;
    @Schema(description = "备注")
    private String remarks;
    @Schema(description = "微服务名称")
    private String serviceName;
    @Schema(description = "连接池类型")
    private String type;
    @Schema(description = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;
}
package com.qm.tds.dynamic.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 多租户跳转关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-30
 */
@Schema(description = "多租户跳转关联表")
@Data
public class TenantDTO extends JsonParamDto implements Serializable {

    private static final long serialVersionUID = -8146861030923629832L;

    @Schema(description = "主键")
    private String id;
    @Schema(description = "租户code")
    private String tenantId;
    @Schema(description = "模块名")
    private String module;
    @Schema(description = "数据源id")
    private String datasourceId;
    @Schema(description = "读/写标识 ")
    private String wrflg;
    @Schema(description = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;
    @Schema(description = "微服务名称 ")
    private String serviceName;
}
package com.qm.tds.dynamic.domain.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 查询租户数据源dto
 * @author: Cyl
 * @time: 2020/7/2 10:41
 */
@Schema(description = "查询租户数据源dto")
@Data
public class TenantDataSourceDTO implements Serializable {

    private static final long serialVersionUID = 8993239265904014569L;

    @Schema(description = "租户code")
    private String tenantId;

    @Schema(description = "微服务名称")
    private String serviceName;

}

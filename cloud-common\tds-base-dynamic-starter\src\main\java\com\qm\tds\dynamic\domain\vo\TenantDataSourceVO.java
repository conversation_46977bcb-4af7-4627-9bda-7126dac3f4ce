package com.qm.tds.dynamic.domain.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 查询租户数据源vo
 * @author: Cyl
 * @time: 2020/7/2 10:41
 */
@Schema(description = "查询租户数据源vo")
@Data
public class TenantDataSourceVO implements Serializable {

    private static final long serialVersionUID = -6248896909129118426L;


    @Schema(description = "主键")
    private String id;

    @Schema(description = "租户code")
    private String tenantId;

    @Schema(description = "模块名")
    private String module;

    @Schema(description = "数据源id")
    private String datasourceId;

    @Schema(description = "读/写标识 ")
    private String wrflg;

    @Schema(description = "数据源名称")
    private String name;

    @Schema(description = "数据源连接地址")
    private String connUrl;

    @Schema(description = "数据源账号")
    private String username;

    @Schema(description = "数据源密码")
    private String password;

    @Schema(description = "数据库驱动名")
    private String driverClassName;

    @Schema(description = "数据源的其他配置")
    private String dsProperties;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "微服务名称")
    private String serviceName;

    @Schema(description = "连接池类型")
    private String type;

}

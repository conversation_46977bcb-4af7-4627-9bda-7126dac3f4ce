package com.qm.tds.dynamic.mapper;

import com.qm.tds.api.mp.mapper.QmBaseMapper;
import com.qm.tds.dynamic.domain.bean.TenantDO;
import com.qm.tds.dynamic.domain.dto.TenantDataSourceDTO;
import com.qm.tds.dynamic.domain.vo.TenantDataSourceVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 多租户跳转关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-30
 */
@Repository
public interface TenantMapper extends QmBaseMapper<TenantDO> {

    /**
     * @description: 查询租户数据源信息
     * @author: Cyl
     * @time: 2020/7/2 10:58
     */
    List<TenantDataSourceVO> selectDatasourceByTenantId(TenantDataSourceDTO dataSourceDTO);

    /**
     * 根据数据源ID获取租户信息
     * @param tenantDO
     * @return
     */
    List<TenantDO> selectTenantByDataSourceId(TenantDO tenantDO);
}

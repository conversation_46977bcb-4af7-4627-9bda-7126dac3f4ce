package com.qm.tds.dynamic.service;

import com.qm.tds.api.service.IQmBaseService;
import com.qm.tds.dynamic.domain.bean.TenantDO;
import com.qm.tds.dynamic.domain.dto.TenantDataSourceDTO;
import com.qm.tds.dynamic.domain.vo.TenantDataSourceVO;

import java.util.List;

/**
 * <p>
 * 多租户跳转关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-30
 */
public interface TenantService extends IQmBaseService<TenantDO> {

    /**
     * 查询租户数据源信息
     *
     * <AUTHOR>
     * @since 2020/7/2 10:58
     */
    List<TenantDataSourceVO> selectDatasourceByTenantId(TenantDataSourceDTO dataSourceDTO);

    /**
     * 根据数据源ID获取租户信息
     */
    List<TenantDO> selectTenantByDataSourceId(TenantDO tenantDO);
}

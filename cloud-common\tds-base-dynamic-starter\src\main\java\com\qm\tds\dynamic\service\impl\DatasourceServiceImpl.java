package com.qm.tds.dynamic.service.impl;

import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.dynamic.domain.bean.DatasourceDO;
import com.qm.tds.dynamic.mapper.DatasourceMapper;
import com.qm.tds.dynamic.service.DatasourceService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-30
 */
@Service
public class DatasourceServiceImpl extends QmBaseServiceImpl<DatasourceMapper, DatasourceDO> implements DatasourceService {

}

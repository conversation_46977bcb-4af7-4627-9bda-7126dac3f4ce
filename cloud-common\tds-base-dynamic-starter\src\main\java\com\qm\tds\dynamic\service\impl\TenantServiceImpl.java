package com.qm.tds.dynamic.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.dynamic.domain.bean.TenantDO;
import com.qm.tds.dynamic.domain.dto.TenantDataSourceDTO;
import com.qm.tds.dynamic.domain.vo.TenantDataSourceVO;
import com.qm.tds.dynamic.mapper.TenantMapper;
import com.qm.tds.dynamic.service.TenantService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 多租户跳转关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-30
 */
@Service
public class TenantServiceImpl extends QmBaseServiceImpl<TenantMapper, TenantDO> implements TenantService {

    @Override
    @DS(DataSourceType.TENANT)
    public List<TenantDataSourceVO> selectDatasourceByTenantId(TenantDataSourceDTO dataSourceDTO) {
        return baseMapper.selectDatasourceByTenantId(dataSourceDTO);
    }

    @Override
    @DS(DataSourceType.TENANT)
    public List<TenantDO> selectTenantByDataSourceId(TenantDO tenantDO) {
        return baseMapper.selectTenantByDataSourceId(tenantDO);
    }
}

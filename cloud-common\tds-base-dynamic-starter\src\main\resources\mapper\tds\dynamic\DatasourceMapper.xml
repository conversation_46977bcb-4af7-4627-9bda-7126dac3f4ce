<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.tds.dynamic.mapper.DatasourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.tds.dynamic.domain.bean.DatasourceDO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="connurl" property="connUrl"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="driverclassname" property="driverClassName"/>
        <result column="dsproperties" property="dsProperties"/>
        <result column="remarks" property="remarks"/>
        <result column="servic_ename" property="serviceName"/>
        <result column="type" property="type"/>
        <result column="DTSTAMP" property="dtstamp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    id, 'name', connurl , username, password, driverclassname , dsproperties , remarks, servicename ,'type', DTSTAMP
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
        select
        a.name,
        a.conn_url as connurl,
        a.username,
        a.password,
        a.driver_class_name as driverclassname,
        a.ds_properties as dsproperties,
        a.remarks,
        a.DTSTAMP,
        a.service_name serviceName,
        a.type,
        a.id
        from datasource_info a
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.tds.dynamic.domain.bean.DatasourceDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.tds.dynamic.domain.bean.DatasourceDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.tds.dynamic.domain.bean.DatasourceDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.tds.dynamic.domain.bean.DatasourceDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.tds.dynamic.domain.bean.DatasourceDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.tds.dynamic.domain.bean.DatasourceDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.tds.dynamic.domain.bean.DatasourceDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.tds.dynamic.domain.bean.DatasourceDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.tds.dynamic.domain.bean.DatasourceDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
</mapper>

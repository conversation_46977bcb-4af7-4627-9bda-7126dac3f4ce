<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.tds.dynamic.mapper.TenantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.tds.dynamic.domain.bean.TenantDO">
        <id column="id" property="id"/>
        <result column="tenantId" property="tenantId"/>
        <result column="module" property="module"/>
        <result column="datasource_id" property="datasourceId"/>
        <result column="wrflg" property="wrflg"/>
        <result column="DTSTAMP" property="dtstamp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    id, tenantId, 'module', datasource_id , wrflg, DTSTAMP
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
     select * from (
        select
        a.tenantId,
        a.module,
        a.datasource_id ,
        a.wrflg,
        a.DTSTAMP,
        a.id,
        b.name as dataSourceName,
        b.service_name as serviceName
        from tenant_datasource_rel a
        left join datasource_info b
        on a.datasource_id = b.id
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.tds.dynamic.domain.bean.TenantDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.tds.dynamic.domain.bean.TenantDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.tds.dynamic.domain.bean.TenantDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.tds.dynamic.domain.bean.TenantDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.tds.dynamic.domain.bean.TenantDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.tds.dynamic.domain.bean.TenantDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.tds.dynamic.domain.bean.TenantDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.tds.dynamic.domain.bean.TenantDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.tds.dynamic.domain.bean.TenantDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>


    <select id="selectDatasourceByTenantId" parameterType="com.qm.tds.dynamic.domain.dto.TenantDataSourceDTO"
            resultType="com.qm.tds.dynamic.domain.vo.TenantDataSourceVO">
      select
        a.tenantId,
        a.module,
        a.datasource_id as datasourceId,
        a.wrflg,
        a.id,
        b.name,
        b.conn_url as connUrl,
        b.username,
        b.password,
        b.driver_class_name as driverClassName,
        b.ds_properties as dsProperties,
        b.remarks,
        b.service_name as serviceName,
        b.type
        from tenant_datasource_rel  a
        left join datasource_info b
        on a.datasource_id = b.id
        where a.tenantId = #{tenantId}
        and b.service_name = #{serviceName}
    </select>

    <select id="selectTenantByDataSourceId" parameterType="com.qm.tds.dynamic.domain.bean.TenantDO"
            resultType="com.qm.tds.dynamic.domain.bean.TenantDO">
        <include refid="QuerySQL"/>
        where datasource_id = #{datasourceId}
    </select>
</mapper>

package com.qm.tds.base.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;


@Schema(description = "多语言 MQ DTO")
@Data
public class MultiLanguageMqDTO extends JsonParamDto implements Serializable {

    /**
     * id
     */
    @Schema(description = "主键id")
    private String id;

    /**
     * 代码ID
     */
    @Schema(description = "代码ID")
    private String nmainid;

    /**
     * 语言代码
     */
    @Schema(description = "语言代码")
    private String vlanguagecode;

    /**
     * 文本
     */
    @Schema(description = "文本")
    private String vtext;

    /**
     * 长文本
     */
    @Schema(description = "长文本")
    private String vlongtext;


    /**
     * 时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Schema(description = "时间戳")
    private Timestamp dtstamp;

    /**
     * 代码
     */
    @Schema(description = "代码")
    private String vcode;

    /**
     * 公司id
     */
    @Schema(description = "公司id")
    private String ncompanyid;

    /**
     * 表名
     */
    @Schema(description = "表名")
    private String vtablename;

    /**
     * 描述文本
     */
    @Schema(description = "描述文本")
    private String vdescription;


    @Schema(description = "多语言表名")
    private String vmultitable;

    @Schema(description = "操作类型（删除DELETE，提交SUBMIT）")
    private String operateType;

    @Schema(description = "多语言表中ID集合  （删除时使用）")
    private String ids;

}

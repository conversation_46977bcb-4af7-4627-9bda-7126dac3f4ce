package com.qm.tds.base.service;

import com.alibaba.fastjson.JSONArray;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.IQmBaseService;
import com.qm.tds.base.domain.MultiLanguageTextDO;
import com.qm.tds.base.domain.dto.MultiLanguageTextDTO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 多语言文本 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
public interface IMultiLanguageTextService extends IQmBaseService<MultiLanguageTextDO> {

    boolean saveMultiText(String nMainId, String vLanguageCode, String vCode, String vText);

    boolean saveMultiText(String nMainId, String vLanguageCode, String vCode, String vText, String vLongText);

    boolean saveMultiText(String nMainId, String vLanguageCode, String vCode, String vText, String vLongText, String nCompanyId, String vTableName);


    //2.	业务大表数据多语言---根据表名动态操作表数据---20211210--start--
    //boolean saveMultiText(String vMultiTable);

    //boolean saveMultiText(String vMultiTable, String nMainId, String vLanguageCode, String vCode, String vText);

    boolean saveMultiText(String vMultiTable, String nMainId, String vLanguageCode, String vCode, String vText, String vLongText);

    boolean saveMultiText(String vMultiTable, String nMainId, String vLanguageCode, String vCode, String vText, String vLongText, String nCompanyId, String vTableName);

    //JsonResultVo<MultiLanguageTextDO> saveInfo(String vMultiTable, MultiLanguageTextDO multiLanguageTextDO);

    JsonResultVo deleteByIds(String vMultiTable, String ids);
    JsonResultVo deleteByMap(String vMultiTable, Map map);
    //2.	业务大表数据多语言---根据表名动态操作表数据---20211210--end--

    /**
     * @description: 保存或更新
     * @author: Cyl
     * @time: 2020/6/16 10:52
     */
    JsonResultVo<MultiLanguageTextDO> saveInfo(MultiLanguageTextDO multiLanguageTextDO);

    /**
     * @description: 保存或更新
     * @author: Cyl
     * @time: 2020/6/16 10:52
     */
    JsonResultVo<MultiLanguageTextDO> saveInfo(String vMultiTable,MultiLanguageTextDO multiLanguageTextDO);

    /**
     * @description: 批量删除
     * @author: Cyl
     * @time: 2020/6/16 11:11
     */
    JsonResultVo deleteByIds(String ids);

    /**
     * @description: 根据map 字段删除
     * @author: Cyl
     * @time: 2020/6/16 11:12
     */
    JsonResultVo deleteByMap(Map map);


    /**
     * @description: 分页查询 不传分页信息全部查询
     * @author: Cyl
     * @time: 2020/6/16 12:56
     */
    JsonResultVo<QmPage<MultiLanguageTextDO>> getListByPage(MultiLanguageTextDTO multiLanguageTextDTO);


    JsonResultVo<QmPage<MultiLanguageTextDO>> getListByPage(String vMultiTable,MultiLanguageTextDTO multiLanguageTextDTO);

    List<MultiLanguageTextDO> getLangListByIds(List idarr);

    List<MultiLanguageTextDO> getLangListByIds(String vMultiTable, List idarr);

    JsonResultVo<MultiLanguageTextDO> updateInfo( MultiLanguageTextDO multiLanguageTextDO);
    JsonResultVo<MultiLanguageTextDO> updateInfo(String vMultiTable, MultiLanguageTextDO multiLanguageTextDO);


    JsonResultVo<MultiLanguageTextDO> getLangOne(MultiLanguageTextDO multiLanguageTextDO);
    JsonResultVo<MultiLanguageTextDO> getLangOne(String vMultiTable, MultiLanguageTextDO multiLanguageTextDO);


    boolean saveBatch(List<MultiLanguageTextDO> list);
    boolean saveBatch(String vMultiTable, List<MultiLanguageTextDO> list);
}

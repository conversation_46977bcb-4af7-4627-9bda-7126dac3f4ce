package com.qm.tds.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.base.config.RequestDataHelper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.base.domain.MultiLanguageTextDO;
import com.qm.tds.base.domain.dto.MultiLanguageTextDTO;
import com.qm.tds.base.mapper.MultiLanguageTextMapper;
import com.qm.tds.base.service.IMultiLanguageTextService;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 多语言文本 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
@Slf4j
@Service
public class MultiLanguageTextServiceImpl extends QmBaseServiceImpl<MultiLanguageTextMapper, MultiLanguageTextDO> implements IMultiLanguageTextService {

    @Value("${ep.lang.multiFlag:false}")
    private boolean multiFlag;
    @Autowired
    private I18nUtil i18nUtil;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public boolean saveMultiText(String nMainId, String vLanguageCode, String vCode, String vText) {
        return saveMultiText(nMainId, vLanguageCode, vCode, vText, vText);
    }

    @Override
    public boolean saveMultiText(String nMainId, String vLanguageCode, String vCode, String vText, String vLongText) {
        return saveMultiText(nMainId, vLanguageCode, vCode, vText, vLongText, "", "");
    }

    @Override
    public boolean saveMultiText(String nMainId, String vLanguageCode, String vCode, String vText, String vLongText, String nCompanyId, String vTableName) {
        //查找是否存在数据
        QmQueryWrapper<MultiLanguageTextDO> wrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<MultiLanguageTextDO> wrapperLambda = wrapper.lambda();
        wrapperLambda.eq(MultiLanguageTextDO::getNmainid, nMainId);
        if (multiFlag)
            wrapperLambda.eq(MultiLanguageTextDO::getVlanguagecode, vLanguageCode);
        MultiLanguageTextDO multiText = getOne(wrapper);
        //如果没有查询到数据，则初始化
        if (multiText == null) {
            multiText = new MultiLanguageTextDO();
            multiText.setNmainid(nMainId);
            multiText.setVlanguagecode(vLanguageCode);
            multiText.setNcompanyid(nCompanyId);
            multiText.setVtablename(vTableName);
        }

        //替换新数据
        multiText.setVcode(vCode);
        multiText.setVtext(vText);
        multiText.setVlongtext(vLongText);

        //更新数据
        return saveOrUpdate(multiText);
    }


    @Override
    public JsonResultVo<MultiLanguageTextDO> saveInfo(MultiLanguageTextDO multiLanguageTextDO) {
        JsonResultVo<MultiLanguageTextDO> result = new JsonResultVo<>();
        //校验
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNmainid())) {
            // 保存数据失败,nmainid不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.nmainidNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNmainid())) {
            // 保存数据失败,存在相同数据
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveData");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVlanguagecode())) {
            // 保存数据失败,语言代码不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.languageCodeNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVtext())) {
            // 保存数据失败,文本不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.textNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNcompanyid())) {
            // 保存数据失败,公司id不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.companyIdNull");
            /*result.setMsgErr(message);
            return result;*/
            //由于业务没有传入公司id ,故不判断空返回错误，改为空取登录信息中的公司id---20220111--
            LoginKeyDO loginKey = BootAppUtil.getLoginKey();
            multiLanguageTextDO.setNcompanyid(loginKey.getCompanyId());
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVlongtext())) {
            // 保存数据失败,长文本不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.longTextNull");
            result.setMsgErr(message);
            return result;
        }

        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getId())) {
            //查找是否存在数据
            QmQueryWrapper<MultiLanguageTextDO> wrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<MultiLanguageTextDO> wrapperLambda = wrapper.lambda();
            wrapperLambda.eq(MultiLanguageTextDO::getNmainid, multiLanguageTextDO.getNmainid());
            wrapperLambda.eq(MultiLanguageTextDO::getVlanguagecode, multiLanguageTextDO.getVlanguagecode());
            MultiLanguageTextDO multiText = getOne(wrapper);
            if (null != multiText) {
                // 保存数据失败,存在相同数据
                String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveData");
                result.setMsgErr(message);
                return result;
            }
        }
        //保存数据
        boolean flag = saveOrUpdate(multiLanguageTextDO);
        if (flag) {

            //由于前端维护菜单多语言，没有实时清理菜单的redis。故在这加上--------20220412-----start-----
            if (!BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVtablename())) {
                if("SYSC014".equals(multiLanguageTextDO.getVtablename())){//判断是这张表就删除菜单缓存
                    LoginKeyDO loginKeyDO = BootAppUtil.getLoginKey();
                    // 如果入参为空，则使用默认值PC。
                    final String deviceQ = formatDevice("");
                    //数据存储key
                    String sysKey = redisUtils.keyBuilder("sys", "com.qm.ep.sys.service.impl.MenuServiceImpl");
                    log.info("-----将要删除菜单redis--------sysKey---"+sysKey);
                    redisUtils.delMutil(sysKey);
                }

            }
            //由于前端维护菜单多语言，没有实时清理菜单的redis。故在这加上--------20220412-----end-----

            result.setData(multiLanguageTextDO);
            // 保存成功！
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveSuccess");
            result.setMsg(message);
        } else {
            // 保存失败！
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveFail");
            result.setMsgErr(message);
        }
        return result;
    }
    @Override
    public JsonResultVo<MultiLanguageTextDO> saveInfo(String vMultiTable,MultiLanguageTextDO multiLanguageTextDO) {
        JsonResultVo<MultiLanguageTextDO> result = new JsonResultVo<>();

        //封装表名参数
        RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
            put("name", vMultiTable);
        }});

        //校验
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNmainid())) {
            // 保存数据失败,nmainid不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.nmainidNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNmainid())) {
            // 保存数据失败,存在相同数据
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveData");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVlanguagecode())) {
            // 保存数据失败,语言代码不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.languageCodeNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVtext())) {
            // 保存数据失败,文本不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.textNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNcompanyid())) {
            // 保存数据失败,公司id不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.companyIdNull");
            /*result.setMsgErr(message);
            return result;*/
            //由于业务没有传入公司id ,故不判断空返回错误，改为空取登录信息中的公司id---20220111--
            LoginKeyDO loginKey = BootAppUtil.getLoginKey();
            multiLanguageTextDO.setNcompanyid(loginKey.getCompanyId());
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVlongtext())) {
            // 保存数据失败,长文本不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.longTextNull");
            result.setMsgErr(message);
            return result;
        }

        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getId())) {
            //查找是否存在数据
            QmQueryWrapper<MultiLanguageTextDO> wrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<MultiLanguageTextDO> wrapperLambda = wrapper.lambda();
            wrapperLambda.eq(MultiLanguageTextDO::getNmainid, multiLanguageTextDO.getNmainid());
            wrapperLambda.eq(MultiLanguageTextDO::getVlanguagecode, multiLanguageTextDO.getVlanguagecode());
            MultiLanguageTextDO multiText = getOne(wrapper);
            if (null != multiText) {
                // 保存数据失败,存在相同数据
                String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveData");
                result.setMsgErr(message);
                return result;
            }
        }
        //保存数据
        boolean flag = saveOrUpdate(multiLanguageTextDO);
        if (flag) {
            result.setData(multiLanguageTextDO);
            // 保存成功！
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveSuccess");
            result.setMsg(message);
        } else {
            // 保存失败！
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveFail");
            result.setMsgErr(message);
        }
        return result;
    }

    @Override
    public JsonResultVo deleteByIds(String ids) {
        JsonResultVo result = new JsonResultVo<>();
        // 删除成功
        String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.deleteSuccess");
        result.setMsg(message);
        if (!BootAppUtil.isNullOrEmpty(ids)) {
            List<String> list = Stream.of(ids.split(",")).collect(Collectors.toList());
            boolean flag = removeByIds(list);
            if (!flag) {
                // 删除失败
                String deleteFail = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.deleteFail");
                result.setMsgErr(deleteFail);
            }
        } else {
            // 删除失败！ids不能为空
            String idsNull = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.idsNull");
            result.setMsgErr(idsNull);
        }
        return result;
    }

    @Override
    public JsonResultVo deleteByMap(Map map) {
        JsonResultVo resultObj = new JsonResultVo();
        if (null != map && !map.isEmpty()) {
            boolean flag = removeByMap(map);
            if (flag) {
                // 删除成功！
                String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.deleteSuccess");
                resultObj.setMsg(message);
            } else {
                // 删除失败！请刷新后重试,并检查传入参数是否正确
                String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.paramError");
                resultObj.setMsgErr(message);
            }
        } else {
            // 删除失败！入参不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.paramNull");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    @Override
    public JsonResultVo<QmPage<MultiLanguageTextDO>> getListByPage(MultiLanguageTextDTO multiLanguageTextDTO) {
        JsonResultVo<QmPage<MultiLanguageTextDO>> result = new JsonResultVo<>();
        //定义查询构造器
        QmQueryWrapper<MultiLanguageTextDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<MultiLanguageTextDO> lambdaWrapper = queryWrapper.lambda();
        //nmainid
        if (!BootAppUtil.isNullOrEmpty(multiLanguageTextDTO.getNmainid())) {
            lambdaWrapper.eq(MultiLanguageTextDO::getNmainid, multiLanguageTextDTO.getNmainid());
        }
        //tablename
        if (!BootAppUtil.isNullOrEmpty(multiLanguageTextDTO.getVtablename())) {
            lambdaWrapper.eq(MultiLanguageTextDO::getVtablename, multiLanguageTextDTO.getVtablename());
        }
        //languagecode
        if (!BootAppUtil.isNullOrEmpty(multiLanguageTextDTO.getVlanguagecode())) {
            lambdaWrapper.eq(MultiLanguageTextDO::getVlanguagecode, multiLanguageTextDTO.getVlanguagecode());
        }
        //text
        if (!BootAppUtil.isNullOrEmpty(multiLanguageTextDTO.getVtext())) {
            lambdaWrapper.eq(MultiLanguageTextDO::getVtext, multiLanguageTextDTO.getVtext());
        }
        //description
        if (!BootAppUtil.isNullOrEmpty(multiLanguageTextDTO.getVdescription())) {
            lambdaWrapper.eq(MultiLanguageTextDO::getVdescription, multiLanguageTextDTO.getVdescription());
        }
        //封装分页参数
        JsonParamDto tableParam = new JsonParamDto();
        tableParam.setCurrentPage(multiLanguageTextDTO.getCurrentPage());
        tableParam.setPageSize(multiLanguageTextDTO.getPageSize());
        //定义查询构造器
        QmPage<MultiLanguageTextDO> list = table(queryWrapper, tableParam);
        result.setData(list);
        return result;
    }


   /* @Override
    public boolean saveMultiText(String vMultiTable) {

        System.out.println("-----saveMultiText----1----vMultiTable--"+vMultiTable);
        //IdModTableNameParser.setTableName(vMultiTable);

        //查找是否存在数据
        QmQueryWrapper<MultiLanguageTextDO> wrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<MultiLanguageTextDO> wrapperLambda = wrapper.lambda();
        wrapperLambda.eq(MultiLanguageTextDO::getNmainid, "123");
        //wrapperLambda.eq(MultiLanguageTextDO::getVlanguagecode, vLanguageCode);
        MultiLanguageTextDO multiText = getOne(wrapper);
        return false;
    }*/

    @Override
    public boolean saveMultiText(String vMultiTable, String nMainId, String vLanguageCode, String vCode, String vText, String vLongText) {
        //System.out.println("-----saveMultiText----6----vMultiTable--"+vMultiTable);
        return saveMultiText(vMultiTable, nMainId, vLanguageCode, vCode, vText, vLongText, "", "");
    }

    @Override
    public boolean saveMultiText(String vMultiTable, String nMainId, String vLanguageCode, String vCode, String vText, String vLongText, String nCompanyId, String vTableName) {
        //                                           String nMainId, String vLanguageCode, String vCode, String vText, String vLongText, String nCompanyId, String vTableName
        //设置表名-
        //System.out.println("-----saveMultiText----8----vMultiTable--"+vMultiTable);
        //封装表名参数
        RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
            put("name", vMultiTable);
        }});

        //查找是否存在数据
        QmQueryWrapper<MultiLanguageTextDO> wrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<MultiLanguageTextDO> wrapperLambda = wrapper.lambda();
        wrapperLambda.eq(MultiLanguageTextDO::getNmainid, nMainId);
        //wrapperLambda.eq(MultiLanguageTextDO::getVlanguagecode, vLanguageCode);
        MultiLanguageTextDO multiText = getOne(wrapper);
        //如果没有查询到数据，则初始化
        if (multiText == null) {
            multiText = new MultiLanguageTextDO();
            multiText.setNmainid(nMainId);
            multiText.setVlanguagecode(vLanguageCode);
            multiText.setNcompanyid(nCompanyId);
            multiText.setVtablename(vTableName);
        }

        //替换新数据
        multiText.setVcode(vCode);
        multiText.setVtext(vText);
        multiText.setVlongtext(vLongText);

        //更新数据
        return saveOrUpdate(multiText);
    }

    @Override
    public JsonResultVo<MultiLanguageTextDO> updateInfo(MultiLanguageTextDO multiLanguageTextDO) {


        JsonResultVo<MultiLanguageTextDO> result = new JsonResultVo<>();
        //校验
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNmainid())) {
            // 保存数据失败,nmainid不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.nmainidNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNmainid())) {
            // 保存数据失败,存在相同数据
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveData");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVlanguagecode())) {
            // 保存数据失败,语言代码不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.languageCodeNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVtext())) {
            // 保存数据失败,文本不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.textNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNcompanyid())) {
            // 保存数据失败,公司id不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.companyIdNull");
            /*result.setMsgErr(message);
            return result;*/
            //由于业务没有传入公司id ,故不判断空返回错误，改为空取登录信息中的公司id---20220111--
            LoginKeyDO loginKey = BootAppUtil.getLoginKey();
            multiLanguageTextDO.setNcompanyid(loginKey.getCompanyId());
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVlongtext())) {
            // 保存数据失败,长文本不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.longTextNull");
            result.setMsgErr(message);
            return result;
        }

        //保存数据
        boolean flag = updateById(multiLanguageTextDO);
        if (flag) {
            result.setData(multiLanguageTextDO);
            // 保存成功！
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveSuccess");
            result.setMsg(message);
        } else {
            // 保存失败！
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveFail");
            result.setMsgErr(message);
        }
        return result;
    }

    @Override
    public JsonResultVo<MultiLanguageTextDO> updateInfo(String vMultiTable, MultiLanguageTextDO multiLanguageTextDO) {

        //System.out.println("-----saveInfo------vMultiTable--"+vMultiTable);
        //封装表名参数
        RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
            put("name", vMultiTable);
        }});
        JsonResultVo<MultiLanguageTextDO> result = new JsonResultVo<>();
        //校验
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNmainid())) {
            // 保存数据失败,nmainid不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.nmainidNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNmainid())) {
            // 保存数据失败,存在相同数据
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveData");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVlanguagecode())) {
            // 保存数据失败,语言代码不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.languageCodeNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVtext())) {
            // 保存数据失败,文本不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.textNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNcompanyid())) {
            // 保存数据失败,公司id不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.companyIdNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVlongtext())) {
            // 保存数据失败,长文本不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.longTextNull");
            result.setMsgErr(message);
            return result;
        }

        //保存数据
        boolean flag = updateById(multiLanguageTextDO);
        if (flag) {
            result.setData(multiLanguageTextDO);
            // 保存成功！
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveSuccess");
            result.setMsg(message);
        } else {
            // 保存失败！
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.saveFail");
            result.setMsgErr(message);
        }
        return result;
    }

    @Override
    public JsonResultVo deleteByIds(String vMultiTable, String ids) {
        //System.out.println("-----deleteByIds------vMultiTable--"+vMultiTable);
        //封装表名参数
        RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
            put("name", vMultiTable);
        }});

        JsonResultVo result = new JsonResultVo<>();
        // 删除成功
        String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.deleteSuccess");
        result.setMsg(message);
        if (!BootAppUtil.isNullOrEmpty(ids)) {
            List<String> list = Stream.of(ids.split(",")).collect(Collectors.toList());
            boolean flag = removeByIds(list);
            if (!flag) {
                // 删除失败
                String deleteFail = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.deleteFail");
                result.setMsgErr(deleteFail);
            }
        } else {
            // 删除失败！ids不能为空
            String idsNull = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.idsNull");
            result.setMsgErr(idsNull);
        }
        return result;
    }

    @Override
    public JsonResultVo deleteByMap(String vMultiTable, Map map) {

        //System.out.println("-----deleteByMap------vMultiTable--"+vMultiTable);
        //封装表名参数
        RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
            put("name", vMultiTable);
        }});

        JsonResultVo resultObj = new JsonResultVo();
        //System.out.println("-----deleteByMap------map--"+map);
        if (null != map && !map.isEmpty()) {
            boolean flag = removeByMap(map);
            if (flag) {
                // 删除成功！
                String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.deleteSuccess");
                resultObj.setMsg(message);
            } else {
                // 删除失败！请刷新后重试,并检查传入参数是否正确
                String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.paramError");
                resultObj.setMsgErr(message);
            }
        } else {
            // 删除失败！入参不能为空
            String message = i18nUtil.getMessage("ERR.basemysql.MultiLanguageTextServiceImpl.paramNull");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    @Override
    public JsonResultVo<QmPage<MultiLanguageTextDO>> getListByPage(String vMultiTable, MultiLanguageTextDTO multiLanguageTextDTO) {

        //封装表名参数
        RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
            put("name", vMultiTable);
        }});

        JsonResultVo<QmPage<MultiLanguageTextDO>> result = new JsonResultVo<>();
        //定义查询构造器
        QmQueryWrapper<MultiLanguageTextDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<MultiLanguageTextDO> lambdaWrapper = queryWrapper.lambda();
        //nmainid
        if (!BootAppUtil.isNullOrEmpty(multiLanguageTextDTO.getNmainid())) {
            lambdaWrapper.eq(MultiLanguageTextDO::getNmainid, multiLanguageTextDTO.getNmainid());
        }
        //tablename
        if (!BootAppUtil.isNullOrEmpty(multiLanguageTextDTO.getVtablename())) {
            lambdaWrapper.eq(MultiLanguageTextDO::getVtablename, multiLanguageTextDTO.getVtablename());
        }
        //languagecode
        if (!BootAppUtil.isNullOrEmpty(multiLanguageTextDTO.getVlanguagecode())) {
            lambdaWrapper.eq(MultiLanguageTextDO::getVlanguagecode, multiLanguageTextDTO.getVlanguagecode());
        }
        //text
        if (!BootAppUtil.isNullOrEmpty(multiLanguageTextDTO.getVtext())) {
            lambdaWrapper.eq(MultiLanguageTextDO::getVtext, multiLanguageTextDTO.getVtext());
        }
        //description
        if (!BootAppUtil.isNullOrEmpty(multiLanguageTextDTO.getVdescription())) {
            lambdaWrapper.eq(MultiLanguageTextDO::getVdescription, multiLanguageTextDTO.getVdescription());
        }
        //封装分页参数
        JsonParamDto tableParam = new JsonParamDto();
        tableParam.setCurrentPage(multiLanguageTextDTO.getCurrentPage());
        tableParam.setPageSize(multiLanguageTextDTO.getPageSize());
        //定义查询构造器
        QmPage<MultiLanguageTextDO> list = table(queryWrapper, tableParam);
        result.setData(list);
        return result;
    }

    //根据ids 查询语言信息数据
    @Override
    public List<MultiLanguageTextDO> getLangListByIds(List idarr) {

        //定义查询构造器
        QmQueryWrapper<MultiLanguageTextDO> queryWrapper = new QmQueryWrapper<>();

        //nmainid
        if (!BootAppUtil.isNullOrEmpty(idarr)) {
            queryWrapper.in("id", idarr);
        }
        //定义查询构造器
        List<MultiLanguageTextDO> list = list(queryWrapper);

        return list;
    }

    //根据ids 查询语言信息数据
    @Override
    public List<MultiLanguageTextDO> getLangListByIds(String vMultiTable, List idarr) {

        //封装表名参数
        RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
            put("name", vMultiTable);
        }});

        //定义查询构造器
        QmQueryWrapper<MultiLanguageTextDO> queryWrapper = new QmQueryWrapper<>();
        //nmainid
        if (!BootAppUtil.isNullOrEmpty(idarr)) {
            queryWrapper.in("id", idarr);
        }
        //定义查询构造器
        List<MultiLanguageTextDO> list = list(queryWrapper);

        return list;
    }

    //根据ids 查询语言信息数据
    @Override
    public JsonResultVo<MultiLanguageTextDO> getLangOne(MultiLanguageTextDO multiLanguageTextDO) {
        JsonResultVo resultObj = new JsonResultVo();
        //查找是否存在数据
        QmQueryWrapper<MultiLanguageTextDO> wrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<MultiLanguageTextDO> wrapperLambda = wrapper.lambda();
        wrapperLambda.eq(MultiLanguageTextDO::getNmainid, multiLanguageTextDO.getNmainid());
        wrapperLambda.eq(MultiLanguageTextDO::getVlanguagecode, multiLanguageTextDO.getVlanguagecode());
        MultiLanguageTextDO multiText = getOne(wrapper);
        resultObj.setData(multiText);
        return resultObj;
    }

    //根据ids 查询语言信息数据
    @Override
    public JsonResultVo<MultiLanguageTextDO> getLangOne(String vMultiTable, MultiLanguageTextDO multiLanguageTextDO) {
        JsonResultVo resultObj = new JsonResultVo();
        //封装表名参数
        RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
            put("name", vMultiTable);
        }});

        //查找是否存在数据
        QmQueryWrapper<MultiLanguageTextDO> wrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<MultiLanguageTextDO> wrapperLambda = wrapper.lambda();
        wrapperLambda.eq(MultiLanguageTextDO::getNmainid, multiLanguageTextDO.getNmainid());
        wrapperLambda.eq(MultiLanguageTextDO::getVlanguagecode, multiLanguageTextDO.getVlanguagecode());
        MultiLanguageTextDO multiText = getOne(wrapper);
        resultObj.setData(multiText);
        return resultObj;
    }


    //批量新增
    @Override
    public boolean saveBatch(List<MultiLanguageTextDO> list) {

        return saveBatch(list, list.size());
    }

    //批量新增
    @Override
    public boolean saveBatch(String vMultiTable, List<MultiLanguageTextDO> list) {
        //封装表名参数
        RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
            put("name", vMultiTable);
        }});

        return saveBatch(list, list.size());
    }
    /**
     * 转换设备条件。如果为空则使用PC作为默认值。
     *
     * @param device 设备类型
     * @return 设备类型
     */
    private String formatDevice(String device) {
        String ret;
        if (BootAppUtil.isNullOrEmpty(device)) {
            ret = "PC";
        } else {
            ret = device;
        }
        return ret;
    }
}

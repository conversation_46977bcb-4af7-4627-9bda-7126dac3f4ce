package com.qm.tds.base.controller;

import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.base.domain.MultiLanguageTextDO;
import com.qm.tds.base.domain.dto.MultiLanguageTextDTO;
import com.qm.tds.base.service.IMultiLanguageTextService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <p>
 * 多语言文本 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
@Tag(name = "多语言文本 前端控制器 </p>", description = "<p> 多语言文本 前端控制器 </p>")
@RestController
@RequestMapping("/multiLanguage")
public class MultiLanguageTextController extends BaseController {

    @Autowired
    private IMultiLanguageTextService multiTextService;

    @Operation(summary = "接口", description = "[author:10200571]")
    @PostMapping("/save")
    public JsonResultVo<MultiLanguageTextDO> save(@RequestBody MultiLanguageTextDO multiLanguageTextDO) {
        return multiTextService.saveInfo(multiLanguageTextDO);
    }

    @Operation(summary = "接口", description = "[author:10200571]")
    @GetMapping("/deleteByIds")
    public JsonResultVo deleteById(String ids) {
        return multiTextService.deleteByIds(ids);
    }

    @Operation(summary = "接口", description = "[author:10200571]")
    @PostMapping("/deleteByMap")
    public JsonResultVo deleteByMap(@RequestBody Map map) {
        return multiTextService.deleteByMap(map);
    }

    @Operation(summary = "接口", description = "[author:10200571]")
    @PostMapping("/getListByPage")
    public JsonResultVo<QmPage<MultiLanguageTextDO>> table(@RequestBody MultiLanguageTextDTO multiLanguageTextDTO) {
        return multiTextService.getListByPage(multiLanguageTextDTO);
    }
}

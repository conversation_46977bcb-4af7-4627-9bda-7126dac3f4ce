package com.qm.tds.base.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 多语言文本
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
@Schema(description = "多语言文本 </p>")
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYSC000_M")
public class MultiLanguageTextDO implements Serializable {


    /**
     * S_TEXTID
     */
    @Schema(description = "主键id")
    @TableId("ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 代码ID
     */
    @Schema(description = "代码ID")
    @TableField("NMAINID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long nmainid;

    /**
     * 语言代码
     */
    @Schema(description = "语言代码")
    @TableField("VLANGUAGECODE")
    private String vlanguagecode;

    /**
     * 文本
     */
    @Schema(description = "文本")
    @TableField("VTEXT")
    private String vtext;

    /**
     * 长文本
     */
    @Schema(description = "长文本")
    @TableField("VLONGTEXT")
    private String vlongtext;


    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @Schema(description = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    /**
     * 代码
     */
    @Schema(description = "代码")
    @TableField("VCODE")
    private String vcode;

    /**
     * 公司id
     */
    @Schema(description = "公司id")
    @TableField(value = "NCOMPANYID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ncompanyid;

    /**
     * 表名
     */
    @TableField("VTABLENAME")
    @Schema(description = "表名")
    private String vtablename;

    /**
     * 描述文本
     */
    @TableField("VDESCRIPTION")
    @Schema(description = "描述文本")
    private String vdescription;
}

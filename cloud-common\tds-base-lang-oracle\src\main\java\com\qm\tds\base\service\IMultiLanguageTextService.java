package com.qm.tds.base.service;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.IQmBaseService;
import com.qm.tds.base.domain.MultiLanguageTextDO;
import com.qm.tds.base.domain.dto.MultiLanguageTextDTO;

import java.util.Map;

/**
 * <p>
 * 多语言文本 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
public interface IMultiLanguageTextService extends IQmBaseService<MultiLanguageTextDO> {

    boolean saveMultiText(long nMainId, String vLanguageCode, String vCode, String vText);

    boolean saveMultiText(long nMainId, String vLanguageCode, String vCode, String vText, String vLongText);

    boolean saveMultiText(long nMainId, String vLanguageCode, String vCode, String vText, String vLongText, long nCompanyId, String vTableName);

    /**
     * @description: 保存或更新
     * @author: Cyl
     * @time: 2020/6/16 10:52
     */
    JsonResultVo<MultiLanguageTextDO> saveInfo(MultiLanguageTextDO multiLanguageTextDO);

    /**
     * @description: 批量删除
     * @author: Cyl
     * @time: 2020/6/16 11:11
     */
    JsonResultVo deleteByIds(String ids);

    /**
     * @description: 根据map 字段删除
     * @author: Cyl
     * @time: 2020/6/16 11:12
     */
    JsonResultVo deleteByMap(Map map);


    /**
     * @description: 分页查询 不传分页信息全部查询
     * @author: Cyl
     * @time: 2020/6/16 12:56
     */
    JsonResultVo<QmPage<MultiLanguageTextDO>> getListByPage(MultiLanguageTextDTO multiLanguageTextDTO);
}

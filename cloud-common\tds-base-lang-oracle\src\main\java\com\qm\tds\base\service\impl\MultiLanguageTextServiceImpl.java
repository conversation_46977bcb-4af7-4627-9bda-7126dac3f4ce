package com.qm.tds.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.base.domain.MultiLanguageTextDO;
import com.qm.tds.base.domain.dto.MultiLanguageTextDTO;
import com.qm.tds.base.mapper.MultiLanguageTextMapper;
import com.qm.tds.base.service.IMultiLanguageTextService;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 多语言文本 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
@Service
public class MultiLanguageTextServiceImpl extends QmBaseServiceImpl<MultiLanguageTextMapper, MultiLanguageTextDO> implements IMultiLanguageTextService {
    @Autowired
    private I18nUtil i18nUtil;
    @Override
    public boolean saveMultiText(long nMainId, String vLanguageCode, String vCode, String vText) {
        return saveMultiText(nMainId, vLanguageCode, vCode, vText, vText);
    }

    @Override
    public boolean saveMultiText(long nMainId, String vLanguageCode, String vCode, String vText, String vLongText) {
        return saveMultiText(nMainId, vLanguageCode, vCode, vText, vLongText, 0, "");
    }

    @Override
    public boolean saveMultiText(long nMainId, String vLanguageCode, String vCode, String vText, String vLongText, long nCompanyId, String vTableName) {
        //查找是否存在数据
        QmQueryWrapper<MultiLanguageTextDO> wrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<MultiLanguageTextDO> wrapperLambda = wrapper.lambda();
        wrapperLambda.eq(MultiLanguageTextDO::getNmainid, nMainId);
        //wrapperLambda.eq(MultiLanguageTextDO::getVlanguagecode, vLanguageCode);
        MultiLanguageTextDO multiText = getOne(wrapper);

        //如果没有查询到数据，则初始化
        if (multiText == null) {
            multiText = new MultiLanguageTextDO();
            multiText.setNmainid(nMainId);
            multiText.setVlanguagecode(vLanguageCode);
            multiText.setNcompanyid(nCompanyId);
            multiText.setVtablename(vTableName);
        }

        //替换新数据
        multiText.setVcode(vCode);
        multiText.setVtext(vText);
        multiText.setVlongtext(vLongText);

        //更新数据
        return saveOrUpdate(multiText);
    }


    @Override
    public JsonResultVo<MultiLanguageTextDO> saveInfo(MultiLanguageTextDO multiLanguageTextDO) {
        JsonResultVo<MultiLanguageTextDO> result = new JsonResultVo<>();
        //校验
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNmainid())) {
            // 保存数据失败,nmainid不能为空
            String message = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.nmainidNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNmainid())) {
            // 保存数据失败,存在相同数据
            String message = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.saveData");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVlanguagecode())) {
            // 保存数据失败,语言代码不能为空
            String message = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.languageCodeNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVtext())) {
            // 保存数据失败,文本不能为空
            String message = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.textNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getNcompanyid())) {
            // 保存数据失败,公司id不能为空
            String message = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.companyIdNull");
            result.setMsgErr(message);
            return result;
        }
        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getVlongtext())) {
            // 保存数据失败,长文本不能为空
            String message = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.longTextNull");
            result.setMsgErr(message);
            return result;
        }

        if (BootAppUtil.isNullOrEmpty(multiLanguageTextDO.getId())) {
            //查找是否存在数据
            QmQueryWrapper<MultiLanguageTextDO> wrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<MultiLanguageTextDO> wrapperLambda = wrapper.lambda();
            wrapperLambda.eq(MultiLanguageTextDO::getNmainid, multiLanguageTextDO.getNmainid());
            wrapperLambda.eq(MultiLanguageTextDO::getVlanguagecode, multiLanguageTextDO.getVlanguagecode());
            MultiLanguageTextDO multiText = getOne(wrapper);
            if (null != multiText) {
                // 保存数据失败,存在相同数据
                String message = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.saveData");
                result.setMsgErr(message);
                return result;
            }
        }
        //保存数据
        boolean flag = saveOrUpdate(multiLanguageTextDO);
        if (flag) {
            result.setData(multiLanguageTextDO);
            // 保存成功！
            String message = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.saveSuccess");
            result.setMsg(message);
        } else {
            // 保存失败！
            String message = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.saveFail");
            result.setMsgErr(message);
        }
        return result;
    }

    @Override
    public JsonResultVo deleteByIds(String ids) {
        JsonResultVo result = new JsonResultVo<>();
        // 删除成功
        String message = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.deleteSuccess");
        result.setMsg(message);
        if (!BootAppUtil.isNullOrEmpty(ids)) {
            List<Long> list = Arrays.stream(ids.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            boolean flag = removeByIds(list);
            if (!flag) {
                // 删除失败
                String deleteFail = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.deleteFail");
                result.setMsgErr(deleteFail);
            }
        } else {
            // 删除失败！ids不能为空
            String idsNull = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.idsNull");
            result.setMsgErr(idsNull);
        }
        return result;
    }

    @Override
    public JsonResultVo deleteByMap(Map map) {
        JsonResultVo resultObj = new JsonResultVo();
        if (null != map && map.isEmpty()) {
            boolean flag = removeByMap(map);
            if (flag) {
                // 删除成功！
                String message = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.deleteSuccess");
                resultObj.setMsg(message);
            } else {
                // 删除失败！请刷新后重试,并检查传入参数是否正确
                String message = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.paramError");
                resultObj.setMsgErr(message);
            }
        } else {
            // 删除失败！入参不能为空
            String message = i18nUtil.getMessage("ERR.baseoracle.MultiLanguageTextServiceImpl.paramNull");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    @Override
    public JsonResultVo<QmPage<MultiLanguageTextDO>> getListByPage(MultiLanguageTextDTO multiLanguageTextDTO) {
        JsonResultVo<QmPage<MultiLanguageTextDO>> result = new JsonResultVo<>();
        //定义查询构造器
        QmQueryWrapper<MultiLanguageTextDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<MultiLanguageTextDO> lambdaWrapper = queryWrapper.lambda();
        //nmainid
        if (!BootAppUtil.isNullOrEmpty(multiLanguageTextDTO.getNmainid())) {
            lambdaWrapper.eq(MultiLanguageTextDO::getNmainid, multiLanguageTextDTO.getNmainid());
        }
        //定义查询构造器
        QmPage<MultiLanguageTextDO> list = table(queryWrapper, multiLanguageTextDTO);
        result.setData(list);
        return result;
    }
}

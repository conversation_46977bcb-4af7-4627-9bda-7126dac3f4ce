<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.tds.base.mapper.MultiLanguageTextMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.tds.base.domain.MultiLanguageTextDO">
        <id column="ID" property="id"/>
        <result column="NMAINID" property="nmainid"/>
        <result column="VLANGUAGECODE" property="vlanguagecode"/>
        <result column="VTEXT" property="vtext"/>
        <result column="VLONGTEXT" property="vlongtext"/>
        <result column="DTSTAMP" property="dtstamp"/>
        <result column="VCODE" property="vcode"/>
        <result column="NCOMPANYID" property="ncompanyid"/>
        <result column="VTABLENAME" property="vtablename"/>
        <result column="VDESCRIPTION" property="vdescription"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NMAINID, VLANGUAGECODE, VTEXT, VLONGTEXT, DTSTAMP, VCODE, NCOMPANYID, VTABLENAME, VDESCRIPTION
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
      select * from (
        select
        a.ID,
        a.NMAINID,
        a.VLANGUAGECODE,
        a.VTEXT,
        a.VLONGTEXT,
        a.DTSTAMP,
        a.VCODE,
        a.NCOMPANYID,
        a.VTABLENAME,
        a.VDESCRIPTION
        from SYSC000_M a
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.tds.base.domain.MultiLanguageTextDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.tds.base.domain.MultiLanguageTextDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.tds.base.domain.MultiLanguageTextDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.tds.base.domain.MultiLanguageTextDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.tds.base.domain.MultiLanguageTextDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.tds.base.domain.MultiLanguageTextDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.tds.base.domain.MultiLanguageTextDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.tds.base.domain.MultiLanguageTextDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.tds.base.domain.MultiLanguageTextDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
</mapper>

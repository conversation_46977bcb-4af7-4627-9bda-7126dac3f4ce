package com.qm.tds.lock.core;


import com.google.common.base.Preconditions;
import com.qm.tds.lock.core.strategy.RedissonConfigStrategy;
import com.qm.tds.lock.core.strategy.impl.ClusterRedissonConfigStrategyImpl;
import com.qm.tds.lock.core.strategy.impl.MasterslaveRedissonConfigStrategyImpl;
import com.qm.tds.lock.core.strategy.impl.SentinelRedissonConfigStrategyImpl;
import com.qm.tds.lock.core.strategy.impl.StandaloneRedissonConfigStrategyImpl;
import com.qm.tds.lock.enums.RedisConnectionType;
import com.qm.tds.lock.prop.RedissonProperties;
import com.qm.tds.lock.util.SpringUtils;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.config.Config;


/**
 * Redisson配置管理器，用于初始化的redisson实例
 */
@Slf4j
public class RedissonManager {

    private Config config = new Config();

    private Redisson redisson = null;

    public RedissonManager() {
    }

    public RedissonManager(RedissonProperties redissonProperties) {
        //装配开关
        Boolean enabled = redissonProperties.getEnabled();
        if (enabled) {
            try {
                config = RedissonConfigFactory.getInstance().createConfig(redissonProperties);
                redisson = (Redisson) Redisson.create(config);
            } catch (Exception e) {
                log.info("---error--"+"Redisson初始化错误", e);
            }
        }
    }

    public Redisson getRedisson() {
        return redisson;
    }

    /**
     * Redisson连接方式配置工厂
     * 双重检查锁
     */
    static class RedissonConfigFactory {

        private RedissonConfigFactory() {
        }

        private static volatile RedissonConfigFactory factory = null;

        public static RedissonConfigFactory getInstance() {
            if (factory == null) {
                synchronized (Object.class) {
                    if (factory == null) {
                        factory = new RedissonConfigFactory();
                    }
                }
            }
            return factory;
        }

        /**
         * 根据连接类型創建连接方式的配置
         *
         * @param redissonProperties
         * @return Config
         */
        Config createConfig(RedissonProperties redissonProperties) {
            Preconditions.checkNotNull(redissonProperties);
            // redis地址未配置
            String redisAddrNull = this.getMessage("ERR.baselock.RedissonManager.redisAddrNull");
            Preconditions.checkNotNull(redissonProperties.getAddress(), redisAddrNull);
            RedisConnectionType connectionType = redissonProperties.getType();
            // 声明连接方式
            RedissonConfigStrategy redissonConfigStrategy;
            if (connectionType.equals(RedisConnectionType.SENTINEL)) {
                redissonConfigStrategy = new SentinelRedissonConfigStrategyImpl();
            } else if (connectionType.equals(RedisConnectionType.CLUSTER)) {
                redissonConfigStrategy = new ClusterRedissonConfigStrategyImpl();
            } else if (connectionType.equals(RedisConnectionType.MASTERSLAVE)) {
                redissonConfigStrategy = new MasterslaveRedissonConfigStrategyImpl();
            } else {
                redissonConfigStrategy = new StandaloneRedissonConfigStrategyImpl();
            }
            // 连接方式创建异常
            String connectError = this.getMessage("ERR.baselock.RedissonManager.connectError");
            Preconditions.checkNotNull(redissonConfigStrategy, connectError);

            return redissonConfigStrategy.createRedissonConfig(redissonProperties);
        }

        public String getMessage(String key) {
            String message = null;
            try {
                I18nUtil i18nUtil = SpringUtils.getBean(I18nUtil.class);
                message = i18nUtil.getMessage(key);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (message == null) {
                return key;
            }
            return message;
        }
    }
}

package com.qm.tds.lock.core.strategy.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.qm.tds.lock.core.strategy.RedissonConfigStrategy;
import com.qm.tds.lock.prop.RedissonProperties;
import com.qm.tds.lock.enums.GlobalConstant;
import org.redisson.config.Config;


/**
 * 单机方式Redisson配置
 */
@Slf4j
public class StandaloneRedissonConfigStrategyImpl implements RedissonConfigStrategy {

    @Override
    public Config createRedissonConfig(RedissonProperties redissonProperties) {
        Config config = new Config();
        try {
            String address = redissonProperties.getAddress();
            String password = redissonProperties.getPassword();
            int database = redissonProperties.getDatabase();
            String redisAddr = GlobalConstant.REDIS_CONNECTION_PREFIX + address;
            config.useSingleServer().setAddress(redisAddr);
            config.useSingleServer().setDatabase(database);
            if (StringUtils.isNotBlank(password)) {
                config.useSingleServer().setPassword(password);
            }
            log.info("初始化Redisson单机配置,连接地址:" + address);
        } catch (Exception e) {
            log.info("---error--"+"单机Redisson初始化错误", e);
            e.printStackTrace();
        }
        return config;
    }
}

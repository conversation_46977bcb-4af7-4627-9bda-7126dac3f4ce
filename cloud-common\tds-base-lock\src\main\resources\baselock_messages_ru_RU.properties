#这里填写俄文翻译
ERR.baselock.JLock.getLockFail=Не удалось получить блокировку, повторить попытку позже
ERR.baselock.LockConstant.clickRepeat=Не допускается нажимать повторно
ERR.baselock.DistributedLockHandler.keysNull=Keys не должны быть пустыми
ERR.baselock.RepeatSubmitAspect.commitRepeat=Не допускается отправлять повторно
ERR.baselock.RedissonManager.redisAddrNull=Адрес redis не сконфигурирован
ERR.baselock.RedissonManager.connectError=Произошло исключение при создании метода соединения
MSG.baselock.RedisConnectionType.singletonWay=Метод автономного развертывания
MSG.baselock.RedisConnectionType.sentinelWay=Метод развертывания часового
MSG.baselock.RedisConnectionType.clusterWay=Кластерный метод
MSG.baselock.RedisConnectionType.masterSlaveWay=Способ развертывания «Ведущий-ведомый»

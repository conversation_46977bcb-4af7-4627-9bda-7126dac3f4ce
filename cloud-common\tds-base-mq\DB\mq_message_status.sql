SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for mq_message_status
-- ----------------------------
DROP TABLE IF EXISTS `mq_message_status`;
CREATE TABLE `mq_message_status`  (
  `id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `edit_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `creator` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'admin' COMMENT '创建人',
  `editor` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'admin' COMMENT '操作人',
  `deleted` tinyint(4) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识',
  `current_retry_times` tinyint(4) NOT NULL DEFAULT 0 COMMENT '当前重试次数',
  `max_retry_times` tinyint(4) NOT NULL DEFAULT 0 COMMENT '最大重试次数',
  `queue_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '队列名',
  `exchange_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '交换器名',
  `exchange_type` varchar(8) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '交换类型',
  `routing_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '路由键',
  `business_module` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务中心(微服务名称)',
  `business_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务键',
  `next_schedule_time` datetime(0) NOT NULL COMMENT '下一次调度时间',
  `message_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '消息状态',
  `init_backoff` bigint(20) UNSIGNED NOT NULL DEFAULT 5 COMMENT '退避初始化值',
  `backoff_factor` tinyint(4) NOT NULL DEFAULT 2 COMMENT '退避因子(也就是指数)',
  `DTSTAMP` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_queue_name`(`queue_name`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_next_schedule_time`(`next_schedule_time`) USING BTREE,
  INDEX `idx_business_key`(`business_key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'mq消息表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

package com.qm.tds.base.utils;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.base.constant.MultiTextSyncConstant;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.mq.builder.DefaultDestination;
import com.qm.tds.mq.builder.DefaultTxMessage;
import com.qm.tds.mq.builder.MessageStruct;
import com.qm.tds.mq.constant.ExchangeType;
import com.qm.tds.mq.message.MessageSendStruct;
import com.qm.tds.mq.remote.MqFeignRemote;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 多语言MQ同步生产消息
 *
 * <AUTHOR>
 * @Date 2020/8/28$ 10:14$
 **/
@Slf4j
@Component
public class MultiTextSyncUtil {

    @Value("${spring.application.name}")
    private String serviceName;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private AmqpAdmin amqpAdmin;
    @Autowired
    private MqFeignRemote mqFeignRemote;
    @Autowired
    private I18nUtil i18nUtil;
    /**
     * private static final ConcurrentMap<String, Boolean> QUEUE_ALREADY_DECLARE = new ConcurrentHashMap<>();
     */

    public boolean sync(LoginKeyDO loginKey, String nMainId, String vLanguageCode, String vCode, String vText, String nCompanyId, String vTableName) {
        boolean flag = true;
        // 消息内容
        Map map = new HashMap<>();
        map.put("nMainId", nMainId);
        map.put("vLanguageCode", vLanguageCode);
        map.put("vCode", vCode);
        map.put("vText", vText);
        map.put("nCompanyId", nCompanyId);
        map.put("vTableName", vTableName);
        // 消息体对象
        MessageSendStruct messageSendStruct = new MessageSendStruct(
                DefaultTxMessage.builder()
                        //业务主键
                        .businessKey(UUID.randomUUID().toString())
                        //微服务名称
                        .businessModule(serviceName)
                        //发送内容
                        .content(MessageStruct.builder()
                                //请求头信息
                                .requestInfo(loginKey)
                                //发送需要处理的内容
                                .message(map)
                                //微服务名称
                                .serviceName(serviceName).build())
                        .build(),
                //不是订阅模式（直接、广播、订阅） 不传入交换机名称、路由键  只有订阅模式才有交换机
                DefaultDestination.builder()
                        //交换机名称 供应商基础信息
                        .exchangeName(MultiTextSyncConstant.RABBIT_MULTI_SYNC_EXCHANGE)
                        //队列名称
                        .queueName("")
                        //路由键
                        .routingKey("")
                        //交换机类型
                        .exchangeType(ExchangeType.FANOUT)
                        .build());
        try {
            JsonResultVo mqRet = mqFeignRemote.sendRabbitMq(messageSendStruct);
            if (mqRet.getCode() != HttpServletResponse.SC_OK) {
                // 消息分发失败。
                String message = i18nUtil.getMessage("ERR.basemq.MultiTextSyncUtil.sendFail");
                throw new QmException(message + mqRet.getMsg());
            }
        } catch (Exception e) {
            flag = false;
        }
        return flag;
    }


    public boolean sync(LoginKeyDO loginKey, String nMainId, String vLanguageCode, String vCode, String vText, String nCompanyId, String vTableName, String vlongtext) {
        boolean flag = true;
        // 消息内容
        Map map = new HashMap<>();
        map.put("nMainId", nMainId);
        map.put("vLanguageCode", vLanguageCode);
        map.put("vCode", vCode);
        map.put("vText", vText);
        map.put("nCompanyId", nCompanyId);
        map.put("vTableName", vTableName);
        map.put("vlongtext", vlongtext);
        // 消息体对象
        MessageSendStruct messageSendStruct = new MessageSendStruct(
                DefaultTxMessage.builder()
                        //业务主键
                        .businessKey(UUID.randomUUID().toString())
                        //微服务名称
                        .businessModule(serviceName)
                        //发送内容
                        .content(MessageStruct.builder()
                                //请求头信息
                                .requestInfo(loginKey)
                                //发送需要处理的内容
                                .message(map)
                                //微服务名称
                                .serviceName(serviceName).build())
                        .build(),
                //不是订阅模式（直接、广播、订阅） 不传入交换机名称、路由键  只有订阅模式才有交换机
                DefaultDestination.builder()
                        //交换机名称 供应商基础信息
                        .exchangeName(MultiTextSyncConstant.RABBIT_MULTI_SYNC_EXCHANGE)
                        //队列名称
                        .queueName("")
                        //路由键
                        .routingKey("")
                        //交换机类型
                        .exchangeType(ExchangeType.FANOUT)
                        .build());
        try {
            JsonResultVo mqRet = mqFeignRemote.sendRabbitMq(messageSendStruct);
            if (mqRet.getCode() != HttpServletResponse.SC_OK) {
                // 消息分发失败。
                String message = i18nUtil.getMessage("ERR.basemq.MultiTextSyncUtil.sendFail");
                throw new QmException(message + mqRet.getMsg());
            }
        } catch (Exception e) {
            flag = false;
        }
        return flag;
    }
    /**
     *
     * @param loginKey
     * @param nMainId
     * @param vLanguageCode
     * @param vCode
     * @param vText
     * @param nCompanyId
     * @param vTableName
     * @param vmultitable  多语言表名
     * @param operateType  操作类型（删除DELETE，提交SUBMIT）
     * @param ids  多语言表中ID集合  （删除时使用）
     * @return
     */
    public boolean sync(LoginKeyDO loginKey, String nMainId, String vLanguageCode, String vCode, String vText, String nCompanyId, String vTableName,String vlongtext,
                        String vmultitable ,String operateType ,String ids) {
        boolean flag = true;
        // 消息内容
        Map map = new HashMap<>();
        map.put("nMainId", nMainId);
        map.put("vLanguageCode", vLanguageCode);
        map.put("vCode", vCode);
        map.put("vText", vText);
        map.put("nCompanyId", nCompanyId);
        map.put("vTableName", vTableName);
        map.put("vlongtext", vlongtext);
        map.put("vmultitable", vmultitable);
        map.put("operateType", operateType);
        map.put("ids", ids);
        // 消息体对象
        MessageSendStruct messageSendStruct = new MessageSendStruct(
                DefaultTxMessage.builder()
                        //业务主键
                        .businessKey(UUID.randomUUID().toString())
                        //微服务名称
                        .businessModule(serviceName)
                        //发送内容
                        .content(MessageStruct.builder()
                                //请求头信息
                                .requestInfo(loginKey)
                                //发送需要处理的内容
                                .message(map)
                                //微服务名称
                                .serviceName(serviceName).build())
                        .build(),
                //不是订阅模式（直接、广播、订阅） 不传入交换机名称、路由键  只有订阅模式才有交换机
                DefaultDestination.builder()
                        //交换机名称 供应商基础信息
                        .exchangeName(MultiTextSyncConstant.RABBIT_MULTI_SYNC_EXCHANGE)
                        //队列名称
                        .queueName("")
                        //路由键
                        .routingKey("")
                        //交换机类型
                        .exchangeType(ExchangeType.FANOUT)
                        .build());
        try {
            JsonResultVo mqRet = mqFeignRemote.sendRabbitMq(messageSendStruct);
            if (mqRet.getCode() != HttpServletResponse.SC_OK) {
                // 消息分发失败。
                String message = i18nUtil.getMessage("ERR.basemq.MultiTextSyncUtil.sendFail");
                throw new QmException(message + mqRet.getMsg());
            }
        } catch (Exception e) {
            flag = false;
        }
        return flag;
    }
}

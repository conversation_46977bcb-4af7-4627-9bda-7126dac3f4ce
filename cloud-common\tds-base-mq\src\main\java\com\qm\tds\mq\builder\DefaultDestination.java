package com.qm.tds.mq.builder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.qm.tds.mq.builder.service.Destination;
import com.qm.tds.mq.constant.ExchangeType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 消息队列builder类
 * @since 2020/7/13 15:50
 */
@Builder
@Data
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"})
@Schema(description = "消息结构对象")
public class DefaultDestination implements Destination, Serializable {

    private static final long serialVersionUID = -1587120316129481511L;

    @Schema(description = "交换机名称" )
    private ExchangeType exchangeType;
    @Schema(description = "队列名称" )
    private String queueName;
    @Schema(description = "路由键" )
    private String exchangeName;
    @Schema(description = "交换机类型" )
    private String routingKey;
    @Schema(description = "参数" )
    private Map<String, Object> arguments; // NOSONAR
    private String expire;

    @Override
    public ExchangeType exchangeType() {
        return exchangeType;
    }

    @Override
    public String queueName() {
        return queueName;
    }

    @Override
    public String expire() {
        return expire;
    }

    @Override
    public String exchangeName() {
        return exchangeName;
    }

    @Override
    public String routingKey() {
        return routingKey;
    }

    @Override
    public Map<String, Object> arguments() {
        return arguments;
    }
}

package com.qm.tds.mq.builder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.qm.tds.mq.builder.service.TxMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 事务消息接口builder类
 * @since 2020/7/13 15:50
 */
@Builder
@Data
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"})
@Schema(description = "事务消息接口builder类")
public class DefaultTxMessage implements TxMessage, Serializable {

    private static final long serialVersionUID = -1891207206228856598L;
    @Schema(description = "微服务名称" )
    private String businessModule;
    @Schema(description = "业务主键" )
    private String businessKey;
    @Schema(description = "发送内容" )
    private MessageStruct content;

    @Override
    public String businessModule() {
        return businessModule;
    }

    @Override
    public String businessKey() {
        return businessKey;
    }

    @Override
    public MessageStruct content() {
        return content;
    }
}

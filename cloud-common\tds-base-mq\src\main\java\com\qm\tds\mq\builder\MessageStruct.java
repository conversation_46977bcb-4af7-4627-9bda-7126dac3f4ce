package com.qm.tds.mq.builder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description: 消息体
 * @author: cyl
 * @date: 2020-07-13 18:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "消息结构对象")
public class MessageStruct implements Serializable {

    private static final long serialVersionUID = 392365881428311040L;
    @Schema(description = "请求信息" )
    private Object requestInfo; //NOSONAR
    @Schema(description = "发送消息体" )
    private Object message; //NOSONAR
    @Schema(description = "微服务名称" )
    private String serviceName;
}

package com.qm.tds.mq.function;

import org.springframework.transaction.annotation.Transactional;

/**
 * 匿名函数方法
 *
 * @param <T> 参数
 * @param <R> 返回类型
 */
@FunctionalInterface
public interface QueueListenerFunction<T, R> {

    /**
     * 用于业务处理消息的方法快
     * 所有业务逻辑全部由该方法负责完成
     * 默认添加 @Transactional rollbackFor为Exception类型的异常，进行事务回滚
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    R handler(T param);
}

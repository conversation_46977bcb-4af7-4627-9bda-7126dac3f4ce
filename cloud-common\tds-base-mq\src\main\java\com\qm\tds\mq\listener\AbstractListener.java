package com.qm.tds.mq.listener;

import com.alibaba.fastjson.JSON;
import com.qm.tds.api.util.SpringContextHolder;
import com.qm.tds.mq.function.QueueListenerFunction;
import com.qm.tds.util.I18nUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/21
 */
@Slf4j
public abstract class AbstractListener {

    @Value("${spring.application.name}")
    private String serviceName;
    @Autowired
    private I18nUtil i18nUtil;
    // public static final String M1 = "广播队列1，手动ACK，接收消息：{}";
    // public static final String M2 = "加入死信异常";
    // public static final String M3 = "确认信息异常";

    /**
     * 消费业务消息队列  默认添加了 @Transactional事务注解
     * 正确消费消息： channel.basicAck(deliveryTag, false);
     * 消息消费异常： channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
     * 使用方法：
     * <p>
     * 调用函数：
     * // class类 extends AbstractListener
     * // 方法默认添加了事务注解，以及消息确认机制，开发者只需关注具体的业务代码即可
     * // 返回类型根据具体的 return 进行返回，如返 返回类型是List类型，只需要List  list = 函数 即可
     * executeConsumeQueue(messageStruct, message, channel, (MessageStruct struct) -> {
     * //编写业务代码
     * return null;
     * });
     *
     * @param entity   具体需要执行的参数
     * @param message  org.springframework.amqp.core.Message
     * @param channel  com.rabbitmq.client.Channel
     * @param function com.qm.tds.mq.function.QueueListenerFunction   匿名函数方法
     * @param <T>      参数泛型
     * @param <R>      返回值泛型
     * @return 返回值会根据泛型 R 进行动态修改
     */
    @Transactional(rollbackFor = Exception.class)
    public <T, R> R executeConsumeQueue(T entity, Message message, Channel channel, QueueListenerFunction<T, R> function) throws IOException {
        log.info(serviceName + ",接收到消息参数是：{}", JSON.toJSONString(entity));
        final long deliveryTag = message.getMessageProperties().getDeliveryTag();
        R res = null;
        try {
            res = function.handler(entity);
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            // 确认信息异常
            String confirmError = this.getMessage("ERR.basemq.AbstractListener.confirmError");
            log.info("---error--"+serviceName + confirmError, e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException e1) {
                // 加入死信异常
                String addDeadError = this.getMessage("ERR.basemq.AbstractListener.addDeadError");
                log.info("---error--"+serviceName + addDeadError, e);
                throw e1;
            }
            throw e;
        }
        return res;
    }

    private String getMessage(String key) {
        String message = null;
        try {
            message = i18nUtil.getMessage(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (message == null) {
            try {
                I18nUtil i18nInner = SpringContextHolder.getBean(I18nUtil.class);
                message = i18nInner.getMessage(key);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (message == null) {
            return key;
        }
        return message;
    }
}

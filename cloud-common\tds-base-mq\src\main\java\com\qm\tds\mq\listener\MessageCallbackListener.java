package com.qm.tds.mq.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ReturnedMessage;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Slf4j
@RequiredArgsConstructor
@Component
public class MessageCallbackListener implements RabbitTemplate.ConfirmCallback, RabbitTemplate.ReturnsCallback {

    @Value("${spring.application.name}")
    private String serviceName;

    /**
     * 确认后回调
     */
    @Override
    public void confirm(CorrelationData correlationData,
                        boolean ack,
                        String cause) {
        if (!ack) {
            log.info("SERVICE_NAME({}),消息发送失败:correlationData({}),ack({}),cause({})", serviceName, correlationData, ack, cause);
        } else {
            log.info("SERVICE_NAME({}),消息发送成功:correlationData({}),ack({}),cause({}),serviceName({})", serviceName, correlationData, ack, cause);
        }
    }

    /**
     * 失败后return回调
     */
    @Override
    public void returnedMessage(ReturnedMessage returnedMessage) {
        log.info("---error--"+"SERVICE_NAME({}),消息丢失:exchange({}),route({}),replyCode({}),replyText({}),serviceName({},message:{}", serviceName, returnedMessage.getExchange(), returnedMessage.getRoutingKey(), returnedMessage.getReplyCode(), returnedMessage.getReplyText(), returnedMessage.getMessage());

    }
}
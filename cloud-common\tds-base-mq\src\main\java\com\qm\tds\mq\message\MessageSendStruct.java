package com.qm.tds.mq.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.qm.tds.mq.builder.DefaultDestination;
import com.qm.tds.mq.builder.DefaultTxMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description: 发送消息对象
 * @author: Cyl
 * @time: 2020/7/14 9:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "发送消息对象")
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"})
public class MessageSendStruct implements Serializable {
    private static final long serialVersionUID = 1582649982446249784L;
    @Schema(description = "业务消息内容信息" )
    private DefaultTxMessage defaultTxMessage;
    @Schema(description = "消息队列信息" )
    private DefaultDestination defaultDestination;
}

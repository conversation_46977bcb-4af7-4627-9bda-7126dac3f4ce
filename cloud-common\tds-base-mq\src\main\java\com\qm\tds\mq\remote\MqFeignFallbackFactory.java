package com.qm.tds.mq.remote;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class MqFeignFallbackFactory implements FallbackFactory<MqFeignRemote> {

    @Override
    public MqFeignRemote create(Throwable throwable) {
        MqFeignRemoteHystrix feignRemoteHystrix = new MqFeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}

package com.qm.tds.mq.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.mq.message.MessageSendStruct;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "common-service-mq", fallbackFactory = MqFeignFallbackFactory.class)
public interface MqFeignRemote {

    /**
     * 发送rabbitmq 消息
     */
    @PostMapping("/message/send")
    JsonResultVo sendRabbitMq(@RequestBody MessageSendStruct messageSendStruct);

}


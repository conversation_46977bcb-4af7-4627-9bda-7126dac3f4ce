package com.qm.tds.mq.remote;


import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.mq.message.MessageSendStruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class MqFeignRemoteHystrix extends QmRemoteHystrix<MqFeignRemote> implements MqFeignRemote {

    @Override
    public JsonResultVo sendRabbitMq(MessageSendStruct messageSendStruct) {
        return getResult();
    }
}

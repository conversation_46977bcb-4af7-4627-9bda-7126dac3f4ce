package com.qm.tds.mq.utils;

import java.time.LocalDateTime;

/**
 * @description: LocalDateTime 工具类
 * @author: Cyl
 * @time: 2020/7/13 22:30
 */
public class LocalDateTimeUtils {
    private LocalDateTimeUtils() {
    }

    public static final LocalDateTime END = LocalDateTime.of(2999, 1, 1, 0, 0, 0);

    /**
     * 退避基准值
     */
    public static final long DEFAULT_INIT_BACKOFF = 10L;
    /**
     * 退避指数
     */
    public static final int DEFAULT_BACKOFF_FACTOR = 2;
    /**
     * 重试次数
     */
    public static final int DEFAULT_MAX_RETRY_TIMES = 5;

    /**
     * 计算下一次执行时间
     *
     * @param base          基础时间
     * @param initBackoff   退避基准值
     * @param backoffFactor 退避指数
     * @param round         轮数
     * @return LocalDateTime
     */
    public static LocalDateTime calculateNextScheduleTime(LocalDateTime base,
                                                          long initBackoff,
                                                          long backoffFactor,
                                                          long round) {
        double delta = initBackoff * Math.pow(backoffFactor, round);
        return base.plusSeconds((long) delta);
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.qm.tds</groupId>
        <artifactId>tds-base-service-parent</artifactId>
        <version>7.1.0-rebate-SNAPSHOT</version>
    </parent>

    <groupId>com.qm.tds</groupId>
    <artifactId>tds-base-tds-file</artifactId>
    <version>7.1.0-rebate-SNAPSHOT</version>
    <name>tds-base-tds-file</name>
    <description>tds-base-tds-file</description>

    <dependencies>
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-common</artifactId>
        </dependency>
        <!--xml解析依赖-->
        <dependency>
            <groupId>javax.xml.soap</groupId>
            <artifactId>javax.xml.soap-api</artifactId>
            <version>1.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.messaging.saaj</groupId>
            <artifactId>saaj-impl</artifactId>
            <version>3.0.2</version>
        </dependency>
        <dependency>
            <groupId>jakarta.xml.soap</groupId>
            <artifactId>jakarta.xml.soap-api</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.1</version>
        </dependency>

        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>4.0.5</version>
        </dependency>
        <dependency>
            <groupId>jakarta.activation</groupId>
            <artifactId>jakarta.activation-api</artifactId>
            <version>2.1.2</version> <!-- 请使用与你的项目兼容的最新版本 -->
        </dependency>
        <!--<dependency>-->
        <!--    <groupId>javax.xml.ws</groupId>-->
        <!--    <artifactId>jaxws-api</artifactId>-->
        <!--    <version>2.3.1</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>jakarta.xml.ws</groupId>
            <artifactId>jakarta.xml.ws-api</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>4.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>4.0.3</version>
        </dependency>
        <!--<dependency>-->
        <!--    <groupId>org.jcodec</groupId>-->
        <!--    <artifactId>jcodec</artifactId>-->
        <!--    <version>${jcodec.version}</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--    <groupId>org.jcodec</groupId>-->
        <!--    <artifactId>jcodec-javase</artifactId>-->
        <!--    <version>${javase.version}</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>jaxws-rt</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>rt</artifactId>
            <version>4.0.0</version>
        </dependency>


    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <target>17</target>
                    <source>17</source>
                    <encoding>UTF-8</encoding>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 上传jar包至私有maven仓库-->
    <distributionManagement>
        <repository>
            <id>dayu-maven-releases</id>
            <url>https://devops-nexus.faw.cn/repository/bp4655969726-releases/</url>
        </repository>
        <snapshotRepository>
            <id>dayu-maven-snapshots</id>
            <url>https://devops-nexus.faw.cn/repository/bp4655969726-snapshots/</url>
            <uniqueVersion>true</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

</project>

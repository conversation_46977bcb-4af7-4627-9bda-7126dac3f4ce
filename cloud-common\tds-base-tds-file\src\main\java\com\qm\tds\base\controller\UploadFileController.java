package com.qm.tds.base.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.bean.UploadFileDO;
import com.qm.tds.base.domain.dto.UploadFileDTO;
import com.qm.tds.base.service.UploadFileService;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Tag(name = "附件信息存储表； </p>", description = "附件信息存储表")
@RestController
@RequestMapping("/uploadFile")
public class UploadFileController extends BaseController {
    @Resource
    private UploadFileService uploadFileService;
    @Resource
    private I18nUtil i18nUtil;
    /**
     * 使用系统默认的保存/修改 方法
     */
    @Operation(summary = "使用系统默认的保存/修改 方法", description = "[author:10200571]")
    @PostMapping("/save")
    public JsonResultVo<UploadFileDO> save(@RequestBody UploadFileDO tempDO) {
        JsonResultVo<UploadFileDO> resultObj = new JsonResultVo<>();
        boolean flag = uploadFileService.saveOrUpdate(tempDO);
        if (flag) {
            resultObj.setData(tempDO);
        } else {
            String message = i18nUtil.getMessage("ERR.base common.common.saveFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的id删除数据
     */
    @Operation(summary = "根据传入的id删除数据", description = "[author:10200571]")
    @PostMapping("/deleteById")
    public JsonResultVo<String> deleteById(@RequestBody UploadFileDO tempDO) {
        JsonResultVo<String> resultObj = new JsonResultVo<>();
        boolean flag = uploadFileService.removeById(tempDO.getId());
        if (flag) {
            String message = i18nUtil.getMessage("ERR.base common.common.delSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.base common.common.delFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的map删除信息
     */
    @Operation(summary = "根据传入的map删除信息", description = "[author:10200571]")
    @PostMapping("/deleteByMap")
    public JsonResultVo<String> deleteByMap(@RequestBody String requestJson) {
        JsonResultVo<String> resultObj = new JsonResultVo<>();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> map;
        try {
            map = objectMapper.readValue(requestJson, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            String message = i18nUtil.getMessage("ERR.base common.common.operateFail");
            resultObj.setMsgErr(message + ": " + e.getMessage());
            return resultObj;
        }
        boolean flag = uploadFileService.removeByMap(map);
        if (flag) {
            String message = i18nUtil.getMessage("ERR.base common.common.operateSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.base common.common.operateFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @Operation(summary = "根据传入的实体信息进行查询", description = "[author:10200571]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<UploadFileDO>> table(@RequestBody UploadFileDTO tempDTO) {
        //定义查询构造器
        QmQueryWrapper<UploadFileDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<UploadFileDO> lambdaWrapper = queryWrapper.lambda();
        //id
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getId())) {
            lambdaWrapper.eq(UploadFileDO::getId, tempDTO.getId());
        }
        //附件信息的描述
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVdsc())) {
            lambdaWrapper.eq(UploadFileDO::getVdsc, tempDTO.getVdsc());
        }
        //附件的文件名
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVfilename())) {
            lambdaWrapper.eq(UploadFileDO::getVfilename, tempDTO.getVfilename());
        }
        //附件的文件类型
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVtype())) {
            lambdaWrapper.eq(UploadFileDO::getVtype, tempDTO.getVtype());
        }
        //附件地址或链接
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVaddr())) {
            lambdaWrapper.eq(UploadFileDO::getVaddr, tempDTO.getVaddr());
        }
        //浏览器识别文件类型
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVcontenttype())) {
            lambdaWrapper.eq(UploadFileDO::getVcontenttype, tempDTO.getVcontenttype());
        }
        //时间戳
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getDtstamp())) {
            lambdaWrapper.eq(UploadFileDO::getDtstamp, tempDTO.getDtstamp());
        }
        //查询数据，使用table函数。
        QmPage<UploadFileDO> list = uploadFileService.table(queryWrapper, tempDTO);
        JsonResultVo<QmPage<UploadFileDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    /**
     * 查询数据，使用Map进行查找数据集合。
     */
    @Operation(summary = "查询数据，使用Map进行查找数据集合。", description = "[author:10200571]")
    @PostMapping("/tableByMap")
    public JsonResultVo<QmPage<UploadFileDO>> tableByMap(@RequestBody String requestJson) {
        JsonResultVo<QmPage<UploadFileDO>> resultObj = new JsonResultVo<>();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> map;
        try {
            map = objectMapper.readValue(requestJson, new TypeReference<Map<String, String>>() {});
        } catch (JsonProcessingException e) {
            String message = i18nUtil.getMessage("ERR.base common.common.operateFail");
            resultObj.setMsgErr(message + ": " + e.getMessage());
            return resultObj;
        }
    
        QmQueryWrapper<UploadFileDO> qmQueryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<UploadFileDO> lambdaWrapper = qmQueryWrapper.lambda();
        // id
        if (!BootAppUtil.isNullOrEmpty(map.get("ID"))) {
            lambdaWrapper.eq(UploadFileDO::getId, map.get("ID"));
        }
        // 附件信息的描述
        if (!BootAppUtil.isNullOrEmpty(map.get("VDSC"))) {
            lambdaWrapper.eq(UploadFileDO::getVdsc, map.get("VDSC"));
        }
        // 附件的文件名
        if (!BootAppUtil.isNullOrEmpty(map.get("VFILENAME"))) {
            lambdaWrapper.eq(UploadFileDO::getVfilename, map.get("VFILENAME"));
        }
        // 附件的文件类型
        if (!BootAppUtil.isNullOrEmpty(map.get("VTYPE"))) {
            lambdaWrapper.eq(UploadFileDO::getVtype, map.get("VTYPE"));
        }
        // 附件地址或链接
        if (!BootAppUtil.isNullOrEmpty(map.get("VADDR"))) {
            lambdaWrapper.eq(UploadFileDO::getVaddr, map.get("VADDR"));
        }
        //浏览器识别文件类型
        if (!BootAppUtil.isNullOrEmpty(map.get("VCONTENTTYPE"))) {
            lambdaWrapper.eq(UploadFileDO::getVcontenttype, map.get("VCONTENTTYPE"));
        }
        // 时间戳
        if (!BootAppUtil.isNullOrEmpty(map.get("DTSTAMP"))) {
            lambdaWrapper.eq(UploadFileDO::getDtstamp, map.get("DTSTAMP"));
        }
        //获取分页信息
        JsonParamDto jsonParamDto = getParaFromMap(map);
        //查询数据，使用table函数。
        QmPage<UploadFileDO> list = uploadFileService.table(qmQueryWrapper, jsonParamDto);
        resultObj.setData(list);
        return resultObj;
    }

    /**
     * 上传文件
     */
    @Operation(summary = "上传文件", description = "[author:10200571]")
    @PostMapping("/upload")
    public JsonResultVo<UploadFileDO> upload(MultipartHttpServletRequest request) throws IOException {
        JsonResultVo<UploadFileDO> resultObj = new JsonResultVo<>();
        String vwatermark = request.getParameter("vwatermark");
        UploadFileDO uploadFileDO = uploadFileService.upload(request,vwatermark);
        if (!BootAppUtil.isNullOrEmpty(uploadFileDO)) {
            resultObj.setData(uploadFileDO);
            String message = i18nUtil.getMessage("ERR.base common.common.uploadSuccess");
            resultObj.setMsg(message);
        } else {
            String message = i18nUtil.getMessage("ERR.base common.common.uploadFail");
            resultObj.setMsgErr(message);
        }
        return resultObj;
    }

    /**
     * 上传文件
     */
    @Operation(summary = "上传文件", description = "[author:10200571]")
    @GetMapping("/download")
    public void download(HttpServletRequest req, HttpServletResponse res) {
        uploadFileService.download(req, res);
    }
}

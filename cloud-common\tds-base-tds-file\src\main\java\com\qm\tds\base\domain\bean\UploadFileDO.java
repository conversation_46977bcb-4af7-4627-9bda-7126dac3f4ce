package com.qm.tds.base.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 附件信息存储表；
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-14
 */
@Schema(description = "附件信息存储表； </p>")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sysb200")
public class UploadFileDO implements Serializable {


    @Schema(description = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "附件信息的描述")
    @TableField("VDSC")
    private String vdsc;

    @Schema(description = "附件的文件名")
    @TableField("VFILENAME")
    private String vfilename;

    @Schema(description = "附件的文件类型")
    @TableField("VTYPE")
    private String vtype;

    @Schema(description = "附件地址或链接")
    @TableField("VADDR")
    private String vaddr;

    @Schema(description = "文件流化后浏览器识别类型")
    @TableField("VCONTENTTYPE")
    private String vcontenttype;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

}

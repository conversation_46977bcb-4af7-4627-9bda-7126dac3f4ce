package com.qm.tds.base.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <p>
 * 附件信息存储表；
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-14
 */
@Schema(description = "文件UploadFileDTO对象")
@Data
public class UploadFileDTO extends JsonParamDto {

    @Schema(description = "主键id")
    private String id;
    @Schema(description = "附件信息的描述")
    private String vdsc;
    @Schema(description = "附件的文件名")
    private String vfilename;
    @Schema(description = "附件的文件类型")
    private String vtype;
    @Schema(description = "附件地址或链接")
    private String vaddr;
    @Schema(description = "浏览器识别文件类型")
    private String vcontenttype;
    @Schema(description = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;
    @Schema(description = "事务码")
    private String vtranscode;
    @Schema(description = "序号")
    private String vseq;
}

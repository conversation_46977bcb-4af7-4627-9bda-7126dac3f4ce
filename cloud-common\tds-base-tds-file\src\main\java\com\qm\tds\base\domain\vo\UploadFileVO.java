package com.qm.tds.base.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 附件信息存储表；
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-14
 */
@Schema(description = "文件UploadFileVO对象")
@Data
public class UploadFileVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "附件信息的描述")
    private String vdsc;

    @Schema(description = "附件的文件名")
    private String vfilename;

    @Schema(description = "附件的文件类型")
    private String vtype;

    @Schema(description = "附件地址或链接")
    private String vaddr;

    @Schema(description = "浏览器识别文件类型")
    private String vcontenttype;

    @Schema(description = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @Schema(description = "事务码")
    private String vtranscode;

    @Schema(description = "序号")
    private String vseq;

    @Schema(description = "文件类型PIC图片来自sysc082中的vfiletype字段")
    private String vfiletype;

    @Schema(description = "第三方附件地址")
    private String vitffulfilename;

    @Schema(description = "哪个业务场景使用，公司+业务类型唯一")
    private String vbustype;
}

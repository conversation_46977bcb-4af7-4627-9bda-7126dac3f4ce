package com.qm.tds.base.file;

import cn.hutool.core.io.FileUtil;
import com.qm.tds.base.domain.MultipartFileDecorator;
import com.qm.tds.util.ImageUtil;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jcodec.api.FrameGrab;
import org.jcodec.common.io.FileChannelWrapper;
import org.jcodec.common.io.NIOUtils;
import org.jcodec.common.model.Picture;
import org.jcodec.scale.AWTUtil;
import org.springframework.util.StreamUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;

/**
 * 抽象的文件处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/20
 */
@Slf4j
public abstract class AbstractFileOperator {

    protected void cutPhotoFromVedio(HttpServletResponse response, String filePath, int width, int height) {
        byte[] fileByte = getFileByte(filePath);
        String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
        MultipartFileDecorator sFile = new MultipartFileDecorator(fileByte, fileName, fileName);

        File file = multipartFileToFile(sFile);
        try (ByteArrayOutputStream bs = new ByteArrayOutputStream();
             ImageOutputStream imOut = ImageIO.createImageOutputStream(bs);
             FileChannelWrapper fileChannelWrapper = NIOUtils.readableChannel(file);) {

            double startSec = 1.632;
            int frameCount = 10;
            BufferedImage bi = null;
            FrameGrab grab = FrameGrab.createFrameGrab(fileChannelWrapper);
            grab.seekToSecondPrecise(startSec);

            ByteArrayInputStream inputStream = null;
            for (int i = 0; i < frameCount; i++) {
                Picture picture = grab.getNativeFrame();
                System.out.println(picture.getWidth() + "x" + picture.getHeight() + " " + picture.getColor());
                bi = AWTUtil.toBufferedImage(picture);
                ImageIO.write(bi, "jpg", imOut);
                inputStream = new ByteArrayInputStream(bs.toByteArray());
            }
            if (inputStream != null) {
                ImageUtil imageUtil = new ImageUtil();
                response.setContentType("image/jpeg");
                InputStream smallInputStream1 = imageUtil.thumbanailImage(inputStream, "jpg", width, height);
                ServletOutputStream outputStream = response.getOutputStream();
                StreamUtils.copy(smallInputStream1, outputStream);
            }
        } catch (Exception e) {
            log.info("---error--"+e.getMessage());
        }
        delteTempFile(file);
    }

    public abstract byte[] getFileByte(String filePath);

    private File multipartFileToFile(MultipartFile file) {
        File toFile = null;
        if (file == null || file.isEmpty()) {
            return null;
        }
        try (InputStream ins = file.getInputStream()) {
            toFile = FileUtil.writeFromStream(ins, file.getOriginalFilename());
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
        }
        return toFile;
    }

    /**
     * 删除本地临时文件
     *
     * @param file
     */
    private void delteTempFile(File file) {
        if (file != null) {
            File del = new File(file.toURI());
            del.delete();
            file.delete();
        }
    }
}

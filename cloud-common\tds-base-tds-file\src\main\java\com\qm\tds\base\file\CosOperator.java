package com.qm.tds.base.file;

import com.qcloud.cos.model.COSObjectInputStream;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.base.domain.vo.UploadFileVO;
import com.qm.tds.util.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * cos文件操作类
 *
 * <AUTHOR>
 * @date 2021/3/3 10:26
 */
@Slf4j
@Component
@Lazy
@ConditionalOnExpression("${qm.base.upload.store-type:1} == 2")
public class CosOperator extends AbstractFileOperator implements FileOperator {
    @Autowired
    private COSUtils cosUtils;
    @Autowired
    private ImageUtil imageUtil;
    @Autowired
    private I18nUtil i18nUtil;
    /**
     * {@inheritDoc}
     * 腾讯COS服务下载文件流
     * ly add 20201104
     */
    @Override
    public boolean downloadFile(HttpServletResponse response, String thumbnailFlag, int widthInt, int heighInt, UploadFileVO uploadFilevO) {
        String filethumbnailImageName = "";
        String filePath = uploadFilevO.getVaddr().substring(0, uploadFilevO.getVaddr().lastIndexOf(FILE_SEPARATOR));
        String fileName = uploadFilevO.getVaddr().substring(uploadFilevO.getVaddr().lastIndexOf(FILE_SEPARATOR) + 1);
        if (!BootAppUtil.isNullOrEmpty(thumbnailFlag) && THUMBNAIL_SMALL.equals(thumbnailFlag)) {
            filethumbnailImageName = fileName.substring(0, fileName.lastIndexOf(".") - 1)
                    + "_" + widthInt + "_" + heighInt
                    + fileName.substring(fileName.lastIndexOf("."));
        }
        try {
            if (!BootAppUtil.isNullOrEmpty(thumbnailFlag) && "normal".equals(thumbnailFlag)) {
                return this.downloadFile(response, uploadFilevO.getVaddr());
            }
            if (!StringUtils.isEmpty(uploadFilevO.getVcontenttype()) && uploadFilevO.getVcontenttype().contains("video") && THUMBNAIL_SMALL.equals(thumbnailFlag)) {
                cutPhotoFromVedio(response, filePath + fileName,widthInt,heighInt);
                return true;
            }

            if (!BootAppUtil.isNullOrEmpty(thumbnailFlag) && THUMBNAIL_SMALL.equals(thumbnailFlag)) {
                this.downloadFile(response, filePath, filethumbnailImageName, fileName, widthInt, heighInt, uploadFilevO);
                return true;
            }
        } catch (Exception e) {
            log.info("---error--"+"下载出错", e);
            log.info("---error-vaddr-", uploadFilevO.getVaddr());
            throw new QmException("文件下载操作错误，请查看日志;", e);
        }
        return false;
    }


    /**
     * 腾讯COS服务下载文件流
     * ly add 20201104
     * {@inheritDoc}
     */
    @Override
    public boolean downloadFile(HttpServletResponse response, String vaddr) {
        String filePath = vaddr.substring(0, vaddr.lastIndexOf(FILE_SEPARATOR));
        String fileName = vaddr.substring(vaddr.lastIndexOf(FILE_SEPARATOR) + 1);
        try (COSObjectInputStream cosObjectInputStream = cosUtils.downLoadStream(vaddr)) {
            StreamUtils.copy(cosObjectInputStream, response.getOutputStream());
        } catch (IOException e) {
            log.info("---error--"+e.getMessage(), e);
            log.info("--downloadFile-error-vaddr-", vaddr);
            String message = i18nUtil.getMessage("ERR.basecommon.CosOperator.cosFileioError");
            throw new QmException(message, e);
        }
        return true;
    }

    /**
     * 腾讯COS上传文件方法(原cosUploadFile方法)
     * ly 20201103 add
     * {@inheritDoc}
     */
    @Override
    public String uploadFile(String basePath, String fileSaveName, MultipartFile multipartFile) {
        //开始封装路径
        String filePath;
        FtpUtil ftpUtil = new FtpUtil();
        //文件路径
        if (!BootAppUtil.isNullOrEmpty(ftpUtil.getFloder())) {
            filePath = ftpUtil.getFloder() + FILE_SEPARATOR + basePath;
        } else {
            filePath = basePath;
        }
        try {
            boolean flag = cosUtils.uploadFile(filePath + FILE_SEPARATOR + fileSaveName, multipartFile);
            return flag ? filePath : "";
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
            log.info("--uploadFile-error-vaddr-", filePath);
            String message = i18nUtil.getMessage("ERR.basecommon.CosOperator.cosUploadFail");
            throw new QmException(message, e);
        }
    }

    /**
     * 腾讯COS服务下载文件流 缩略图
     * ly add 20201104
     */
    private void downloadFile(HttpServletResponse response, String filePath, String fileName, String fileNormalName, int width, int heigh, UploadFileVO uploadFilevO) throws IOException {
        String key = filePath + fileName;
        String keyNormal = filePath + fileNormalName;
        if (!BootAppUtil.isNullOrEmpty(uploadFilevO)
                && "PIC".equalsIgnoreCase(uploadFilevO.getVfiletype())) {
            boolean flag = cosUtils.doesObjectExist(key);
            //如果缩略图文件不存在在腾讯云服务器上那么，下载原图缩放后放入到云端并下载到前端
            if (!flag) {
                this.getthumbanailImageCosInputStream(key, keyNormal, width, heigh, uploadFilevO);
            }
            try (COSObjectInputStream cosObjectInputStream = cosUtils.downLoadStream(key)) {
                //获得回传res中是流位置

                response.setContentLengthLong(cosObjectInputStream.available());
                response.addHeader("Content-Range", "bytes 0-" + cosObjectInputStream.available() + "/" + cosObjectInputStream.available());

                StreamUtils.copy(cosObjectInputStream, response.getOutputStream());

            } catch (Exception e) {
                log.info("---error--"+e.getMessage(), e);
                log.info("--downloadFile-error-keyNormal-", keyNormal);
                String message = i18nUtil.getMessage("ERR.basecommon.CosOperator.cosFileioError");
                throw new QmException(message, e);
            }
        }
    }

    /**
     * 下载原图之后缩放，上传后再下载提供输入流。
     */
    private Boolean getthumbanailImageCosInputStream(String key, String keyNormal, int width, int height, UploadFileVO uploadFilevO) {
        Boolean result = false;
        try (COSObjectInputStream cosObjectInputStream = cosUtils.downLoadStream(keyNormal)) {
            if (cosObjectInputStream != null) {
                try (InputStream is = imageUtil.thumbanailImage(cosObjectInputStream, uploadFilevO.getVcontenttype(), width, height)) {
                    //获得InputStream长度
                    byte[] bytes = StreamUtils.copyToByteArray(is);
                    long size = bytes.length;
                    if (size > 0L) {
                        result = cosUtils.uploadFile(key, size, uploadFilevO.getVcontenttype(), is);
                    }
                }
            }
        } catch (Exception e) {
            log.info("---error--"+"下载缩略图出错", e);
        }
        return result;
    }

    @Override
    public byte[] getFileByte(String filePath) {
        try (COSObjectInputStream cosObjectInputStream = cosUtils.downLoadStream(filePath);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream();) {
            StreamUtils.copy(cosObjectInputStream, outputStream);
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new byte[0];
    }
}

package com.qm.tds.base.file;

import com.qm.tds.base.domain.vo.UploadFileVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 文件操作接口
 *
 * <AUTHOR>
 * @date 2021/3/3 10:25
 */
public interface FileOperator {
    String FILE_SEPARATOR = "/"; //NOSONAR
    /**
     * 缩略图类型。小尺寸。
     */
    String THUMBNAIL_SMALL = "small"; //NOSONAR
    /**
     * 缩略图类型。常规尺寸。
     */
    String THUMBNAIL_NORMAL = "normal"; //NOSONAR

    /**
     * 下载文件方法
     *
     * @param response      响应
     * @param thumbnailFlag 压缩标识
     * @param width         压缩宽度
     * @param height        压缩高度
     * @param uploadFileVO  文件信息
     * @return 下载是否成功
     */
    boolean downloadFile(HttpServletResponse response, String thumbnailFlag, int width, int height, UploadFileVO uploadFileVO);

    /**
     * @param response 响应
     * @param vaddr    文件全路径
     * @return 下载是否成功
     * @throws IOException 如果发生I/O异常
     */
    boolean downloadFile(HttpServletResponse response, String vaddr) throws IOException;

    /**
     * @param basePath      上传路径
     * @param fileSaveName  文件存储名
     * @param multipartFile 文件内容
     * @return 文件所在路径
     */
    String uploadFile(String basePath, String fileSaveName, MultipartFile multipartFile);
}

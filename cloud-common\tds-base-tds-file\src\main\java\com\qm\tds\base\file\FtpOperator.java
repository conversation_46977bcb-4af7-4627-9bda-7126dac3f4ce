package com.qm.tds.base.file;

import com.qm.tds.api.exception.QmException;
import com.qm.tds.base.domain.vo.UploadFileVO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.FtpUtil;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.ImageUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * Ftp文件操作类
 *
 * <AUTHOR>
 * @date 2021/3/3 10:40
 */
@Slf4j
@Component
@Lazy
@ConditionalOnExpression("${qm.base.upload.store-type:1} == 1")
public class FtpOperator extends AbstractFileOperator implements FileOperator {
    @Autowired
    private ImageUtil imageUtil;
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 原ftpdownloadCenter方法
     * <p>
     * {@inheritDoc}
     */
    @Override
    public boolean downloadFile(HttpServletResponse response, String thumbnailFlag, int widthInt, int heighInt, UploadFileVO uploadFileVO) {
        Boolean result = false;
        try {
            // fullPathNameAndSuffix=文件路径+文件名+文件后缀名
            String fullPathNameAndSuffix = uploadFileVO.getVaddr();
            // fullPath=文件路径
            String fullPath = fullPathNameAndSuffix.substring(0, fullPathNameAndSuffix.lastIndexOf(FILE_SEPARATOR));
            // nameAndSuffix=文件名+文件后缀名
            String nameAndSuffix = fullPathNameAndSuffix.substring(fullPathNameAndSuffix.lastIndexOf(FILE_SEPARATOR) + 1);
            String fileName = nameAndSuffix.substring(0, nameAndSuffix.lastIndexOf("."));
            String suffix = nameAndSuffix.substring(nameAndSuffix.lastIndexOf("."));
            switch (thumbnailFlag) {
                case THUMBNAIL_SMALL:

                    if (!StringUtils.isEmpty(uploadFileVO.getVcontenttype()) && uploadFileVO.getVcontenttype().contains("video") && THUMBNAIL_SMALL.equals(thumbnailFlag)) {
                        cutPhotoFromVedio(response, fullPathNameAndSuffix, widthInt, heighInt);
                        break;
                    }

                    String smallImageName = fileName + "_" + widthInt + "_" + heighInt + suffix;
                    String smallImagePathName = fullPath + FILE_SEPARATOR + smallImageName;
                    result = this.ftpdownload(response, smallImagePathName, fullPathNameAndSuffix, widthInt, heighInt, uploadFileVO);

                    break;
                case THUMBNAIL_NORMAL:
                    result = this.ftpdownload(response, fullPathNameAndSuffix);
                    break;
                default:
            }
        } catch (QmException e) {
            throw e;
        } catch (Exception e) {
            log.info("---error--"+"下载出错", e);
            throw new QmException("文件下载操作错误，请查看日志;", e);
        }
        return result;
    }

    /**
     * FTD下载文件原方法封装
     * ly add 20201126
     * {@inheritDoc}
     */
    @Override
    public boolean downloadFile(HttpServletResponse response, String vaddr) {
        boolean result = false;
        FtpUtil ftpUtil = new FtpUtil();
        try {
            // 获取FTP客户端
            FTPClient ftpClient = ftpUtil.getFTPClient();
            byte[] b = ftpUtil.getFtpFileByte(ftpClient, vaddr);
            if (ArrayUtils.isEmpty(b)) {
                log.info("下载文件在FTP服务器中不存在[" + vaddr + "]");
            }
            // 获得回传res中是流位置 文件流写入response
            StreamUtils.copy(b, response.getOutputStream());
            result = true;
        } catch (IOException e) {
            log.info("---error--"+e.getMessage(), e);
        }
        return result;
    }

    /**
     * FTP上传文件方法(原ftpUploadFile方法)
     * ly 20201103 add
     * {@inheritDoc}
     */
    @Override
    public String uploadFile(String basePath, String fileSaveName, MultipartFile multipartFile) {
        String filePath;
        FtpUtil ftpUtil = new FtpUtil();
        if (!BootAppUtil.isNullOrEmpty(ftpUtil.getFloder())) {
            filePath = ftpUtil.getFloder() + FILE_SEPARATOR + basePath;
        } else {
            filePath = basePath;
        }
        log.debug("文件路径为：{}", filePath);
        try {
            log.debug("获取FTP客户端...");
            FTPClient ftpClient = ftpUtil.getFTPClient();
            log.debug("上传文件[size:{}][{}]..."
                    , multipartFile.getSize()
                    , multipartFile.getBytes().length);
            boolean flag = ftpUtil.uploadFile(ftpClient, filePath, fileSaveName, multipartFile.getInputStream());
            return flag ? filePath : "";
        } catch (IOException e) {
            log.info("---error--"+e.getMessage(), e);
            String message = i18nUtil.getMessage("ERR.basecommon.FtpOperator.ftpCauseError");
            throw new QmException(message, e);
        } catch (Exception e) {
            String message = i18nUtil.getMessage("ERR.basecommon.UploadFileServiceImpl.fileUploadFail");
            log.info("---error--"+e.getMessage(), e);
            throw new QmException(message, e);
        }
    }

    /**
     * FTD下载文件原方法封装
     * ly add 20201126
     */
    private Boolean ftpdownload(HttpServletResponse response, String allfileName) throws IOException {
        Boolean result = false;
        FtpUtil ftpUtil = new FtpUtil();
        // 获取FTP客户端
        FTPClient ftpClient = ftpUtil.getFTPClient();
        try {
            byte[] b = ftpUtil.getFtpFileByte(ftpClient, allfileName);
            if (ArrayUtils.isEmpty(b)) {
                log.info("---error--"+"下载文件在FTP服务器中不存在[" + allfileName + "]");
                String message = i18nUtil.getMessage("ERR.basecommon.FtpOperator.ftpNotFoundFile");
                throw new QmException(message);
            }
            response.setContentLengthLong(b.length);
            //
            response.addHeader("Content-Range", "bytes 0-" + b.length + "/" + b.length);
            // 获得回传res中是流位置 文件流写入response
            StreamUtils.copy(b, response.getOutputStream());

        } catch (IOException e) {
            log.info("---error--"+e.getMessage(), e);
        }
        return result;
    }

    /**
     * FTD下载文件原方法封装下载缩略图
     * ly add 20201126
     */
    private Boolean ftpdownload(HttpServletResponse response, String smallImagePathName, String fullPathNameAndSuffix, int width, int heigh, UploadFileVO uploadFilevO) throws IOException {
        String filePath = smallImagePathName.substring(0, smallImagePathName.lastIndexOf(FILE_SEPARATOR));
        String fileName = smallImagePathName.substring(smallImagePathName.lastIndexOf(FILE_SEPARATOR) + 1);
        boolean result = false;
        FtpUtil ftpUtil = new FtpUtil();
        //是图片格式那么就给出缩略图否则就不处理给null
        if (uploadFilevO != null
                && !BootAppUtil.isNullOrEmpty(uploadFilevO.getVfiletype())
                && "PIC".equalsIgnoreCase(uploadFilevO.getVfiletype())) {
            //是图片格式那么就出缩略图，否则不出缩略图
            // ftpUtil.getFTPClient()获取FTP客户端
            FtpUtil ftpUtil1 = new FtpUtil();
            boolean flag = ftpUtil1.seacrhFileInFTP(ftpUtil1.getFTPClient(), filePath, fileName);
            if (!flag) {
                this.getthumbanailImageInputStream(smallImagePathName, fullPathNameAndSuffix, ImageUtil.getSuffix(fileName), width, heigh);
            }
            // ftpUtil.getFTPClient()获取FTP客户端
            FtpUtil ftpUtil2 = new FtpUtil();
            byte[] b = ftpUtil2.getFtpFileByte(ftpUtil2.getFTPClient(), smallImagePathName);
            if (ArrayUtils.isEmpty(b)) {
                log.info("---error--"+"下载文件在FTP上不存在");
                String message = i18nUtil.getMessage("ERR.basecommon.FtpOperator.ftpNotFoundFile");
                throw new QmException(message);
            }
            // 通过response中获取ServletOutputStream输出流
            StreamUtils.copy(b, response.getOutputStream());
            result = true;
        } else {
            log.info("---error--"+"uploadFileVO为空或者uploadFileVO.vfiletype为空");
        }
        return result;
    }

    /**
     * 下载原图之后缩放，上传后再下载提供输入流。
     * ly
     * 20201126
     */
    private Boolean getthumbanailImageInputStream(String allfileName, String allfileNormalName, String fix, int width, int height) throws IOException {
        boolean result = false;
        FtpUtil ftpUtil = new FtpUtil();
        // 获取FTP客户端
        FTPClient ftpClient = ftpUtil.getFTPClient();
        String filePath = allfileName.substring(0, allfileName.lastIndexOf(FILE_SEPARATOR));
        String fileName = allfileName.substring(allfileName.lastIndexOf(FILE_SEPARATOR) + 1);

        FTPClient ftpClientUpload = ftpUtil.getFTPClient();
        byte[] isb = ftpUtil.getFtpFileByte(ftpClient, allfileNormalName);
        if (ArrayUtils.isNotEmpty(isb)) {
            // ByteArrayInputStream属于内存流，无需关闭刷新等操作
            InputStream isNormal = new ByteArrayInputStream(isb);
            InputStream is = imageUtil.thumbanailImage(isNormal, fix, width, height);
            result = ftpUtil.uploadFile(ftpClientUpload, filePath, fileName, is);
        } else {
            log.info("下载文件FTP服务器不存在");
        }
        return result;
    }

    @Override
    public byte[] getFileByte(String filePath) {
        FtpUtil ftpUtil = new FtpUtil();
        // 获取FTP客户端
        try {
            FTPClient ftpClient = ftpUtil.getFTPClient();
            byte[] b = ftpUtil.getFtpFileByte(ftpClient, filePath);
            return b;
        } catch (IOException e) {
            log.info("---error--"+e.getMessage(), e);
        }
        return new byte[0];
    }
}

package com.qm.tds.base.file;

import com.obs.services.ObsClient;
import com.obs.services.model.ObsObject;
import com.qcloud.cos.utils.IOUtils;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.base.domain.vo.UploadFileVO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.ImageUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * Ftp文件操作类
 *
 * <AUTHOR>
 * @date 2022/7/19 10:40
 */
@Slf4j
@Component
@Lazy
@ConditionalOnExpression("${qm.base.upload.store-type:1} == 4")
public class ObsOperator extends AbstractFileOperator implements FileOperator {

    @Autowired
    private ImageUtil imageUtil;

    @Value("${qm.base.upload.cos-secretId:null}")
    private String AK;

    @Value("${qm.base.upload.cos-secretKey:null}")
    private String SK;

    @Value("${qm.base.upload.cos-regionName:null}")
    private String ENDPOINT;

    @Value("${qm.base.upload.cos-bucket:null}")
    private String BUCKET_NAME;

    @Value("${qm.base.upload.bucket-path:null}")
    private String BUCKET_PATH;

    private static ObsClient obsClient;

    public ObsClient getObsClient(){
        if (null==obsClient){
            obsClient = new ObsClient(AK, SK, ENDPOINT);
        }
        return obsClient;
    }

    @Override
    public boolean downloadFile(HttpServletResponse response, String vaddr)  {
        boolean result = false;
        try {
            ObsObject obsObject = getObsClient().getObject(BUCKET_NAME, BUCKET_PATH+FILE_SEPARATOR+vaddr);
            if(obsObject!=null){
                InputStream input = obsObject.getObjectContent();
                if (input != null) {
                    byte[] b = IOUtils.toByteArray(input);
                    response.setContentLengthLong(b.length);
                    response.addHeader("Content-Range", "bytes 0-" + b.length + "/" + b.length);
                    StreamUtils.copy(b, response.getOutputStream());
                    result = true;
                } else {
                    log.info("---error--"+"下载文件不在OBS服务器上[" + vaddr + "]");
                }
            }
        } catch (Exception e) {
            log.info("---error--"+"downloadFile-异常ObsException:" + e.getMessage());
        }
        return result;
    }

    @Override
    public boolean downloadFile(HttpServletResponse response, String thumbnailFlag, int widthInt, int heighInt, UploadFileVO uploadFileVO) {
        Boolean result = false;
        try {
            // fullPathNameAndSuffix=文件路径+文件名+文件后缀名
            String fullPathNameAndSuffix = uploadFileVO.getVaddr();
            if(fullPathNameAndSuffix.indexOf('.')>-1){
                switch (thumbnailFlag) {
                    case THUMBNAIL_SMALL:
                        // fullPath=文件路径
                        String fullPath = fullPathNameAndSuffix.substring(0, fullPathNameAndSuffix.lastIndexOf(FILE_SEPARATOR));
                        // nameAndSuffix=文件名+文件后缀名
                        String nameAndSuffix = fullPathNameAndSuffix.substring(fullPathNameAndSuffix.lastIndexOf(FILE_SEPARATOR) + 1);
                        String fileName = nameAndSuffix.substring(0, nameAndSuffix.lastIndexOf("."));
                        String suffix = nameAndSuffix.substring(nameAndSuffix.lastIndexOf("."));
                        if (!StringUtils.isEmpty(uploadFileVO.getVcontenttype()) && uploadFileVO.getVcontenttype().contains("video") && THUMBNAIL_SMALL.equals(thumbnailFlag)) {
                            cutPhotoFromVedio(response, fullPathNameAndSuffix, widthInt, heighInt);
                            break;
                        }
                        String smallImageName = fileName + "_" + widthInt + "_" + heighInt + suffix;
                        String smallImagePathName = fullPath + FILE_SEPARATOR + smallImageName;
                        result = this.obsDownload(response, smallImagePathName, fullPathNameAndSuffix, widthInt, heighInt, uploadFileVO);
                        break;
                    case THUMBNAIL_NORMAL:
                        result = this.downloadFile(response, fullPathNameAndSuffix);
                        break;
                    default:
                }
            }else {
                result = this.downloadFile(response, fullPathNameAndSuffix);
            }
        } catch (QmException e) {
            throw e;
        } catch (Exception e) {
            log.info("---error--"+"下载出错", e);
            throw new QmException("文件下载操作错误，请查看日志;", e);
        }
        return result;
    }

    private Boolean obsDownload(HttpServletResponse response, String smallImagePathName, String fullPathNameAndSuffix, int width, int heigh, UploadFileVO uploadFilevO) throws IOException {
        String fileName = smallImagePathName.substring(smallImagePathName.lastIndexOf(FILE_SEPARATOR) + 1);
        boolean result = false;
        try{
            //是图片格式那么就给出缩略图否则就不处理给null
            if (uploadFilevO != null
                    && !BootAppUtil.isNullOrEmpty(uploadFilevO.getVfiletype())
                    && "PIC".equalsIgnoreCase(uploadFilevO.getVfiletype())) {
                //是图片格式那么就出缩略图，否则不出缩略图
                boolean flag = getObsClient().doesObjectExist(BUCKET_NAME, BUCKET_PATH+FILE_SEPARATOR+smallImagePathName);
                if (!flag) {
                    this.getthumbanailImageInputStream(smallImagePathName, fullPathNameAndSuffix, ImageUtil.getSuffix(fileName), width, heigh);
                }
                ObsObject obsObject = getObsClient().getObject(BUCKET_NAME,  BUCKET_PATH+FILE_SEPARATOR+smallImagePathName);
                InputStream input = obsObject.getObjectContent();
                if (input != null) {
                    byte[] b = IOUtils.toByteArray(input);
                    if (ArrayUtils.isEmpty(b)) {
                        throw new QmException("下载文件在OBS上不存在");
                    }
                    response.setContentLengthLong(b.length);
                    response.addHeader("Content-Range", "bytes 0-" + b.length + "/" + b.length);
                    StreamUtils.copy(b, response.getOutputStream());
                    result = true;
                } else {
                    log.info("---error--"+"下载文件不在OBS服务器上[" + smallImagePathName + "]");
                }
            } else {
                log.info("---error--"+"uploadFileVO为空或者uploadFileVO.vfiletype为空");
            }
        }catch (Exception e) {
            log.info("---error--"+"obsDownload下载文件异常Exception:" + e.getMessage());
        }
        return result;
    }

    private Boolean getthumbanailImageInputStream(String allfileName, String allfileNormalName, String fix, int width, int height) throws IOException {
        boolean result = false;
        try{
            ObsObject obsObject = getObsClient().getObject(BUCKET_NAME,  BUCKET_PATH+FILE_SEPARATOR+allfileNormalName);
            InputStream input = obsObject.getObjectContent();
            if (input != null) {
                byte[] isb = IOUtils.toByteArray(input);
                if (ArrayUtils.isNotEmpty(isb)) {
                    // ByteArrayInputStream属于内存流，无需关闭刷新等操作
                    InputStream isNormal = new ByteArrayInputStream(isb);
                    InputStream is = imageUtil.thumbanailImage(isNormal, fix, width, height);
                    getObsClient().putObject(BUCKET_NAME,  BUCKET_PATH+FILE_SEPARATOR+allfileName, is);
                    result = true;
                } else {
                    log.info("---error--"+"下载文件不在OBS服务器上[" + allfileNormalName + "]");
                }
            } else {
                log.info("---error--"+"下载文件不在OBS服务器上[" + allfileNormalName + "]");
            }
        }catch (Exception e) {
            log.info("---error--"+"getthumbanailImageInputStream下载文件异常Exception:" + e.getMessage());
        }
        return result;
    }

    @Override
    public String uploadFile(String basePath, String fileSaveName, MultipartFile multipartFile) {
        try {
            InputStream inputStream = multipartFile.getInputStream();
            getObsClient().putObject(BUCKET_NAME, BUCKET_PATH+FILE_SEPARATOR+basePath+FILE_SEPARATOR+fileSaveName, inputStream);
        } catch (Exception e) {
            log.info("---error--"+"OBS上传文件报错Exception:" + e.getMessage());
        }
        return basePath;
    }

    @Override
    public byte[] getFileByte(String filePath) {
        try {
            ObsObject obsObject = getObsClient().getObject(BUCKET_NAME, BUCKET_PATH+FILE_SEPARATOR+filePath);
            InputStream input = obsObject.getObjectContent();
            return IOUtils.toByteArray(input);
        } catch (IOException e) {
            log.info("---error--"+e.getMessage(), e);
        }
        return new byte[0];
    }
}

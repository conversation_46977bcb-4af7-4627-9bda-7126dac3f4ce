package com.qm.tds.base.file;

import com.qm.tds.api.exception.QmException;
import com.qm.tds.base.domain.vo.UploadFileVO;
import com.qm.tds.base.remote.TDSFileService;
import com.qm.tds.base.remote.TDSFileServiceSoap;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.ImageUtil;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * Tds文件操作类
 *
 * <AUTHOR>
 * @date 2021/3/3 10:53
 */
@Slf4j
@Component
@ConditionalOnExpression("${qm.base.upload.store-type:1} == 3")
public class TdsOperator extends AbstractFileOperator implements FileOperator {
    private TDSFileServiceSoap webService;
    @Autowired
    private I18nUtil i18nUtil;
    @Value("${qm.base.upload.tdsv2.url}")
    private String fileServiceUrl;

    /**
     * 初始化webService方法
     */
    @PostConstruct
    public void init() {
        // 初始化WebService对象
        if (BootAppUtil.isNullOrEmpty(fileServiceUrl)) {
            String message = i18nUtil.getMessage("ERR.basecommon.TdsOperator.fileServiceUrlNull");
            throw new QmException(message);
        } else {
            try {
                TDSFileService service = new TDSFileService(new URL(fileServiceUrl));
                webService = service.getTDSFileServiceSoap();
            } catch (MalformedURLException e) {
                String message = i18nUtil.getMessage("ERR.basecommon.TdsOperator.serviceUrlIncorrect");
                throw new QmException(message + e.getMessage(), e);
            } catch (Exception e) {
                String message = i18nUtil.getMessage("ERR.basecommon.TdsOperator.soapServiceCreateFail");
                throw new QmException(message + fileServiceUrl + "]！" + e.getMessage(), e);
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean downloadFile(HttpServletResponse response, String thumbnailFlag, int width, int height, UploadFileVO uploadFileVO) {
        try {
            String vaddr = uploadFileVO.getVaddr();
            switch (thumbnailFlag) {
                case THUMBNAIL_SMALL:
                    String basePath = vaddr.substring(0, vaddr.lastIndexOf("/") + 1);
                    String fileName = vaddr.substring(vaddr.lastIndexOf("/") + 1);
                    String fileThumbnailImageName = fileName.substring(0, fileName.lastIndexOf(".") - 1)
                            + "_" + width + "_" + height
                            + fileName.substring(fileName.lastIndexOf("."));
                    String allFileNormalname = basePath + fileThumbnailImageName;

                    if (!StringUtils.isEmpty(uploadFileVO.getVcontenttype()) && uploadFileVO.getVcontenttype().contains("video")) {
                        cutPhotoFromVedio(response, vaddr, width, height);
                        break;
                    }
                    // 下载缩略图
                    byte[] fileData = webService.getAccessoriesByName(allFileNormalname);
                    if (ArrayUtils.isEmpty(fileData)) {
                        // 下载原始图片
                        byte[] oldFileData = webService.getAccessoriesByName(vaddr);
                        if (oldFileData != null && oldFileData.length > 0) {
                            InputStream isNormal = new ByteArrayInputStream(oldFileData);
                            ImageUtil imageUtil = new ImageUtil();
                            // 服务器上没有对应文件，生成一个缩略图
                            String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
                            fileData = imageUtil.thumbanailImageBytes(isNormal, suffix, width, height, false);
                        }
                        log.debug("开始上传TDSv2文件[{}][{}][{}]"
                                , basePath
                                , fileThumbnailImageName
                                , (fileData == null ? "null" : String.valueOf(fileData.length)));
                        webService.writeFile(basePath, fileData, fileThumbnailImageName);
                    }
                    // 输出缩略图
                    StreamUtils.copy(fileData, response.getOutputStream());
                    break;
                case THUMBNAIL_NORMAL:
                default:
                    // 下载原始图片
                    return this.downloadFile(response, vaddr);
            }
        } catch (Exception e) {
            log.info("---error--"+"下载出错", e);
            throw new QmException("文件下载操作错误，请查看日志;", e);
        }
        return false;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean downloadFile(HttpServletResponse response, String vaddr) {
        try {
            byte[] fileData = webService.getAccessoriesByName(vaddr);
            if (ArrayUtils.isNotEmpty(fileData)) {
                response.setContentLengthLong(fileData.length);
                response.addHeader("Content-Range", "bytes 0-" + fileData.length + "/" + fileData.length);
                StreamUtils.copy(fileData, response.getOutputStream());
                return true;
            } else {
                log.info("下载文件在服务器中不存在[{}]", vaddr);
                return false;
            }
        } catch (IOException e) {
            log.info("---error--"+"下载文件异常！" + e.getMessage(), e);
            return false;
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String uploadFile(String basePath, String fileSaveName, MultipartFile multipartFile) {
        try {
            byte[] bytes = multipartFile.getBytes();
            log.debug("开始上传TDSv2文件[size:{}][bytes.lenght:{}][basePath:{}][fileSaveName:{}]..."
                    , multipartFile.getSize()
                    , bytes.length
                    , basePath
                    , fileSaveName);
            webService.writeFile(basePath, bytes, fileSaveName);
            return basePath;
        } catch (Exception e) {
            log.info("---error--"+"上传文件失败！" + e.getMessage(), e);
            return "";
        }
    }

    @Override
    public byte[] getFileByte(String filePath) {
        return webService.getAccessoriesByName(filePath);
    }
}

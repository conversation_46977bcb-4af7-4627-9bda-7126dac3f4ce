
package com.qm.tds.base.remote;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "type",
    "filename"
})
@XmlRootElement(name = "DeleteFile")
public class DeleteFile {

    protected String type;
    protected String filename;

    public String getType() {
        return type;
    }

    public void setType(String value) {
        this.type = value;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String value) {
        this.filename = value;
    }

}

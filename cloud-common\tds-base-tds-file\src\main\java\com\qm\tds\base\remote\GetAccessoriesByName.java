
package com.qm.tds.base.remote;


import javax.xml.bind.annotation.*;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "fileName"
})
@XmlRootElement(name = "GetAccessoriesByName")
public class GetAccessoriesByName {

    @XmlElement(name = "_fileName")
    protected String fileName;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String value) {
        this.fileName = value;
    }

}

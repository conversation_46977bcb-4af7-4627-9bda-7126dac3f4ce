
package com.qm.tds.base.remote;


import javax.xml.bind.annotation.*;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "getAccessoriesByNameResult"
})
@XmlRootElement(name = "GetAccessoriesByNameResponse")
public class GetAccessoriesByNameResponse {

    @XmlElement(name = "GetAccessoriesByNameResult")
    protected byte[] getAccessoriesByNameResult;

    public byte[] getGetAccessoriesByNameResult() {
        return getAccessoriesByNameResult;
    }

    public void setGetAccessoriesByNameResult(byte[] value) {
        this.getAccessoriesByNameResult = value;
    }

}


package com.qm.tds.base.remote;


import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlRegistry;

/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the com.qm.tds.base.remote.tdsfile package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 */
@XmlRegistry
@NoArgsConstructor
public class ObjectFactory {

    /**
     * Create an instance of {@link Write }
     */
    public Write createWrite() {
        return new Write();
    }

    /**
     * Create an instance of {@link WriteFileResponse }
     */
    public WriteFileResponse createWriteFileResponse() {
        return new WriteFileResponse();
    }

    /**
     * Create an instance of {@link WriteFile }
     */
    public WriteFile createWriteFile() {
        return new WriteFile();
    }

    /**
     * Create an instance of {@link DeleteFileResponse }
     */
    public DeleteFileResponse createDeleteFileResponse() {
        return new DeleteFileResponse();
    }

    /**
     * Create an instance of {@link WriteResponse }
     */
    public WriteResponse createWriteResponse() {
        return new WriteResponse();
    }

    /**
     * Create an instance of {@link GetAccessoriesByName }
     */
    public GetAccessoriesByName createGetAccessoriesByName() {
        return new GetAccessoriesByName();
    }

    /**
     * Create an instance of {@link GetAccessoriesByNameResponse }
     */
    public GetAccessoriesByNameResponse createGetAccessoriesByNameResponse() {
        return new GetAccessoriesByNameResponse();
    }

    /**
     * Create an instance of {@link HelloWorldResponse }
     */
    public HelloWorldResponse createHelloWorldResponse() {
        return new HelloWorldResponse();
    }

    /**
     * Create an instance of {@link HelloWorld }
     */
    public HelloWorld createHelloWorld() {
        return new HelloWorld();
    }

    /**
     * Create an instance of {@link DeleteFile }
     */
    public DeleteFile createDeleteFile() {
        return new DeleteFile();
    }

}

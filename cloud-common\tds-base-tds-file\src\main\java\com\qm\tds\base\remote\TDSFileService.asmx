<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="HelloWorld">
        <s:complexType />
      </s:element>
      <s:element name="HelloWorldResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="HelloWorldResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Write">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="value" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="filename" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="WriteResponse">
        <s:complexType />
      </s:element>
      <s:element name="WriteFile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="type" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="value" type="s:base64Binary" />
            <s:element minOccurs="0" maxOccurs="1" name="filename" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="WriteFileResponse">
        <s:complexType />
      </s:element>
      <s:element name="GetAccessoriesByName">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="_fileName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAccessoriesByNameResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAccessoriesByNameResult" type="s:base64Binary" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteFile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="type" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="filename" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteFileResponse">
        <s:complexType />
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="HelloWorldSoapIn">
    <wsdl:part name="parameters" element="tns:HelloWorld" />
  </wsdl:message>
  <wsdl:message name="HelloWorldSoapOut">
    <wsdl:part name="parameters" element="tns:HelloWorldResponse" />
  </wsdl:message>
  <wsdl:message name="WriteSoapIn">
    <wsdl:part name="parameters" element="tns:Write" />
  </wsdl:message>
  <wsdl:message name="WriteSoapOut">
    <wsdl:part name="parameters" element="tns:WriteResponse" />
  </wsdl:message>
  <wsdl:message name="WriteFileSoapIn">
    <wsdl:part name="parameters" element="tns:WriteFile" />
  </wsdl:message>
  <wsdl:message name="WriteFileSoapOut">
    <wsdl:part name="parameters" element="tns:WriteFileResponse" />
  </wsdl:message>
  <wsdl:message name="GetAccessoriesByNameSoapIn">
    <wsdl:part name="parameters" element="tns:GetAccessoriesByName" />
  </wsdl:message>
  <wsdl:message name="GetAccessoriesByNameSoapOut">
    <wsdl:part name="parameters" element="tns:GetAccessoriesByNameResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteFileSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteFile" />
  </wsdl:message>
  <wsdl:message name="DeleteFileSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteFileResponse" />
  </wsdl:message>
  <wsdl:portType name="TDSFileServiceSoap">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldSoapIn" />
      <wsdl:output message="tns:HelloWorldSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Write">
      <wsdl:input message="tns:WriteSoapIn" />
      <wsdl:output message="tns:WriteSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="WriteFile">
      <wsdl:input message="tns:WriteFileSoapIn" />
      <wsdl:output message="tns:WriteFileSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetAccessoriesByName">
      <wsdl:input message="tns:GetAccessoriesByNameSoapIn" />
      <wsdl:output message="tns:GetAccessoriesByNameSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteFile">
      <wsdl:input message="tns:DeleteFileSoapIn" />
      <wsdl:output message="tns:DeleteFileSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="TDSFileServiceSoap" type="tns:TDSFileServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Write">
      <soap:operation soapAction="http://tempuri.org/Write" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="WriteFile">
      <soap:operation soapAction="http://tempuri.org/WriteFile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccessoriesByName">
      <soap:operation soapAction="http://tempuri.org/GetAccessoriesByName" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteFile">
      <soap:operation soapAction="http://tempuri.org/DeleteFile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="TDSFileServiceSoap12" type="tns:TDSFileServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap12:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Write">
      <soap12:operation soapAction="http://tempuri.org/Write" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="WriteFile">
      <soap12:operation soapAction="http://tempuri.org/WriteFile" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccessoriesByName">
      <soap12:operation soapAction="http://tempuri.org/GetAccessoriesByName" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteFile">
      <soap12:operation soapAction="http://tempuri.org/DeleteFile" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="TDSFileService">
    <wsdl:port name="TDSFileServiceSoap" binding="tns:TDSFileServiceSoap">
      <soap:address location="http://************/tdsfileservice/TDSFileService.asmx" />
    </wsdl:port>
    <wsdl:port name="TDSFileServiceSoap12" binding="tns:TDSFileServiceSoap12">
      <soap12:address location="http://************/tdsfileservice/TDSFileService.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
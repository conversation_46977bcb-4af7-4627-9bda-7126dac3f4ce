
package com.qm.tds.base.remote;


import jakarta.xml.ws.*;

import javax.xml.namespace.QName;
import java.net.MalformedURLException;
import java.net.URL;
/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 */
@WebServiceClient(name = "TDSFileService", targetNamespace = "http://tempuri.org/", wsdlLocation = "http://jfapp.qm.cn/tdsfileservice/TDSFileService.asmx?wsdl")
public class TDSFileService extends Service {

    private static final URL TDSFILESERVICE_WSDL_LOCATION;
    private static final WebServiceException TDSFILESERVICE_EXCEPTION;
    private static final String NAMESPACE = "http://tempuri.org/";
    private static final QName TDSFILESERVICE_QNAME = new QName(NAMESPACE, "TDSFileService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://jfapp.qm.cn/tdsfileservice/TDSFileService.asmx?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        TDSFILESERVICE_WSDL_LOCATION = url;
        TDSFILESERVICE_EXCEPTION = e;
    }

    public TDSFileService() {
        super(__getWsdlLocation(), TDSFILESERVICE_QNAME);
    }

    public TDSFileService(WebServiceFeature... features) {
        super(__getWsdlLocation(), TDSFILESERVICE_QNAME, features);
    }

    public TDSFileService(URL wsdlLocation) {
        super(wsdlLocation, TDSFILESERVICE_QNAME);
    }

    public TDSFileService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, TDSFILESERVICE_QNAME, features);
    }

    public TDSFileService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public TDSFileService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * @return returns TDSFileServiceSoap
     */
    @WebEndpoint(name = "TDSFileServiceSoap")
    public TDSFileServiceSoap getTDSFileServiceSoap() {
        return super.getPort(new QName(NAMESPACE, "TDSFileServiceSoap"), TDSFileServiceSoap.class);
    }

    /**
     * @param features A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return returns TDSFileServiceSoap
     */
    @WebEndpoint(name = "TDSFileServiceSoap")
    public TDSFileServiceSoap getTDSFileServiceSoap(WebServiceFeature... features) {
        return super.getPort(new QName(NAMESPACE, "TDSFileServiceSoap"), TDSFileServiceSoap.class, features);
    }

    /**
     * @return returns TDSFileServiceSoap
     */
    @WebEndpoint(name = "TDSFileServiceSoap12")
    public TDSFileServiceSoap getTDSFileServiceSoap12() {
        return super.getPort(new QName(NAMESPACE, "TDSFileServiceSoap12"), TDSFileServiceSoap.class);
    }

    /**
     * @param features A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return returns TDSFileServiceSoap
     */
    @WebEndpoint(name = "TDSFileServiceSoap12")
    public TDSFileServiceSoap getTDSFileServiceSoap12(WebServiceFeature... features) {
        return super.getPort(new QName(NAMESPACE, "TDSFileServiceSoap12"), TDSFileServiceSoap.class, features);
    }

    private static URL __getWsdlLocation() {
        if (TDSFILESERVICE_EXCEPTION != null) {
            throw TDSFILESERVICE_EXCEPTION;
        }
        return TDSFILESERVICE_WSDL_LOCATION;
    }

}

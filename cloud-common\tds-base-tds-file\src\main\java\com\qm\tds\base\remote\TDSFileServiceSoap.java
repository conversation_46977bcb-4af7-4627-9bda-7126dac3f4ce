
package com.qm.tds.base.remote;


import jakarta.jws.WebMethod;
import jakarta.jws.WebParam;
import jakarta.jws.WebResult;
import jakarta.jws.WebService;
import jakarta.xml.ws.RequestWrapper;
import jakarta.xml.ws.ResponseWrapper;

import javax.xml.bind.annotation.XmlSeeAlso;



/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "TDSFileServiceSoap", targetNamespace = "http://tempuri.org/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface TDSFileServiceSoap {


    /**
     * 
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "HelloWorld", action = "http://tempuri.org/HelloWorld")
    @WebResult(name = "HelloWorldResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "HelloWorld", targetNamespace = "http://tempuri.org/", className = "com.qm.tds.base.remote.tdsfile.HelloWorld")
    @ResponseWrapper(localName = "HelloWorldResponse", targetNamespace = "http://tempuri.org/", className = "com.qm.tds.base.remote.tdsfile.HelloWorldResponse")
    public String helloWorld();

    /**
     * 
     * @param filename
     * @param value
     */
    @WebMethod(operationName = "Write", action = "http://tempuri.org/Write")
    @RequestWrapper(localName = "Write", targetNamespace = "http://tempuri.org/", className = "com.qm.tds.base.remote.tdsfile.Write")
    @ResponseWrapper(localName = "WriteResponse", targetNamespace = "http://tempuri.org/", className = "com.qm.tds.base.remote.tdsfile.WriteResponse")
    public void write(
        @WebParam(name = "value", targetNamespace = "http://tempuri.org/")
        String value,
        @WebParam(name = "filename", targetNamespace = "http://tempuri.org/")
        String filename);

    /**
     * 
     * @param filename
     * @param type
     * @param value
     */
    @WebMethod(operationName = "WriteFile", action = "http://tempuri.org/WriteFile")
    @RequestWrapper(localName = "WriteFile", targetNamespace = "http://tempuri.org/", className = "com.qm.tds.base.remote.tdsfile.WriteFile")
    @ResponseWrapper(localName = "WriteFileResponse", targetNamespace = "http://tempuri.org/", className = "com.qm.tds.base.remote.tdsfile.WriteFileResponse")
    public void writeFile(
        @WebParam(name = "type", targetNamespace = "http://tempuri.org/")
        String type,
        @WebParam(name = "value", targetNamespace = "http://tempuri.org/")
        byte[] value,
        @WebParam(name = "filename", targetNamespace = "http://tempuri.org/")
        String filename);

    /**
     * 
     * @param fileName
     * @return
     *     returns byte[]
     */
    @WebMethod(operationName = "GetAccessoriesByName", action = "http://tempuri.org/GetAccessoriesByName")
    @WebResult(name = "GetAccessoriesByNameResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "GetAccessoriesByName", targetNamespace = "http://tempuri.org/", className = "com.qm.tds.base.remote.tdsfile.GetAccessoriesByName")
    @ResponseWrapper(localName = "GetAccessoriesByNameResponse", targetNamespace = "http://tempuri.org/", className = "com.qm.tds.base.remote.tdsfile.GetAccessoriesByNameResponse")
    public byte[] getAccessoriesByName(
        @WebParam(name = "_fileName", targetNamespace = "http://tempuri.org/")
        String fileName);

    /**
     * 
     * @param filename
     * @param type
     */
    @WebMethod(operationName = "DeleteFile", action = "http://tempuri.org/DeleteFile")
    @RequestWrapper(localName = "DeleteFile", targetNamespace = "http://tempuri.org/", className = "com.qm.tds.base.remote.tdsfile.DeleteFile")
    @ResponseWrapper(localName = "DeleteFileResponse", targetNamespace = "http://tempuri.org/", className = "com.qm.tds.base.remote.tdsfile.DeleteFileResponse")
    public void deleteFile(
        @WebParam(name = "type", targetNamespace = "http://tempuri.org/")
        String type,
        @WebParam(name = "filename", targetNamespace = "http://tempuri.org/")
        String filename);

}

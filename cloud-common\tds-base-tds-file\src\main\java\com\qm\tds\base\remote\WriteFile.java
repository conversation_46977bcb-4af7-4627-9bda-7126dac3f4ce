
package com.qm.tds.base.remote;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "type",
    "value",
    "filename"
})
@XmlRootElement(name = "WriteFile")
public class WriteFile {

    protected String type;
    protected byte[] value;
    protected String filename;

    public String getType() {
        return type;
    }

    public void setType(String value) {
        this.type = value;
    }

    public byte[] getValue() {
        return value;
    }

    public void setValue(byte[] value) {
        this.value = value;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String value) {
        this.filename = value;
    }

}

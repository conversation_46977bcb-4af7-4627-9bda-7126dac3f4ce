package com.qm.tds.base.service;

import com.qm.tds.api.service.IQmBaseService;
import com.qm.tds.base.domain.bean.UploadFileDO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 附件信息存储表； 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-14
 */
public interface UploadFileService extends IQmBaseService<UploadFileDO> {


    /**
     * @return
     * @description 上传文件
     * <AUTHOR>
     * @date 2020/7/14 15:09
     */
    UploadFileDO upload(MultipartHttpServletRequest request, String sysc080Vwatermark) throws IOException;

    /**
     * @return
     * @description 上传文件
     * <AUTHOR>
     * @date 2020/7/14 15:09
     */
    UploadFileDO upload(MultipartHttpServletRequest request, String sysc080Vwatermark, List<String> limitFileFormat) throws IOException;

    /**
     * 上传附件
     *
     * @param multipartFile 附件信息
     * @param busType       业务类型。区分文件夹
     * @param compress      对图片是否压缩的标识符。1压缩，其他不压缩
     * @return 附件信息
     */
    UploadFileDO upload(MultipartFile multipartFile, String busType, String compress);

    /**
     * 上传附件
     *
     * @param multipartFile  附件信息
     * @param busType        业务类型。区分文件夹
     * @param compress       对图片是否压缩的标识符。1压缩，其他不压缩
     * @param businessFolder 文件路径（不包括FTP、腾讯COS自带的跟路径）
     * @return 附件信息
     * @see #upload(MultipartFile, String, String)
     */
    UploadFileDO upload(MultipartFile multipartFile, String busType, String compress, String businessFolder, String watermark, String sysc080Vwatermark);

    /**
     * 上传附件
     *
     * @param multipartFile  附件信息
     * @param busType        业务类型。区分文件夹
     * @param compress       对图片是否压缩的标识符。1压缩，其他不压缩
     * @param businessFolder 文件路径（不包括FTP、腾讯COS自带的跟路径）
     * @return 附件信息
     * @see #upload(MultipartFile, String, String)
     */
    UploadFileDO upload(MultipartFile multipartFile, String busType, String compress, String businessFolder, String watermark, String sysc080Vwatermark, List<String> limitFileFormat);

    /**
     * @description 下载文件
     * <AUTHOR>
     * @date 2020/7/15 9:56
     */
    boolean download(HttpServletRequest req, HttpServletResponse res);
}

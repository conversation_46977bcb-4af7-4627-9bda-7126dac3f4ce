package com.qm.tds.base.service.impl;


import com.google.common.collect.Lists;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.base.domain.MultipartFileDecorator;
import com.qm.tds.base.domain.dto.UploadFileDTO;
import com.qm.tds.base.mapper.UploadFileMapper;
import com.qm.tds.base.domain.bean.UploadFileDO;
import com.qm.tds.base.domain.vo.UploadFileVO;
import com.qm.tds.base.file.FileOperator;
import com.qm.tds.base.service.UploadFileService;
import com.qm.tds.util.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 附件信息存储表； 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @description 2020-11-05 ly 增加腾讯云文件读取方式
 * @since 2020-07-14
 */
@Service
@Lazy
@Slf4j
public class UploadFileServiceImpl extends QmBaseServiceImpl<UploadFileMapper, UploadFileDO> implements UploadFileService {
    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    private FileOperator fileOperator;
    @Autowired(required = false)
    private RestTemplate restTemplate;
    @Autowired(required = false)
    private UploadFileMapper uploadFileMapper;

    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private ImageUtil imageUtil;

    private String defaultContentType = "application/octet-stream";
    private String responseHeaderText = "Content-Disposition";
    private String responseFilename = "attachment;filename=";
    private String wwwChartType = "ISO8859-1";
    private String localFileName = "filename";

    private static final String BUSINESS_TYPE = "filemanager/shift";
    private static final String FILE_SEPARATOR = "/";
    /**
     * 注意此处配置文件不能使用yml格式的list
     * 具体正确写法如下：
     * qm.base.upload.limit-format: aaa,bbb,ccc
     */
    @Value("#{'${qm.base.upload.limit-format:jpg,jpeg,png,jfif}'.split(',')}")
    public List<String> imageFormat;
    @Value("${qm.base.upload.size-limit:2048}")
    public int sizeLimit;
    @Value("${qm.base.upload.size-limit-MB:100}")
    public int sizeLimitMB;

    @Override
    public UploadFileDO upload(MultipartHttpServletRequest request, String sysc080Vwatermark) throws IOException {
        return upload(request, sysc080Vwatermark, Lists.newArrayList());
    }

    @Override
    public UploadFileDO upload(MultipartHttpServletRequest request, String sysc080Vwatermark, List<String> limitFileFormat) throws IOException {
        UploadFileDO uploadFileDO = new UploadFileDO();
        // 文件路径（不包括FTP、腾讯COS自带的跟路径）ly add 20201103
        String busType = request.getParameter("busType");
        // 业务类型区分文件夹
        String businessFolder = request.getParameter("businessType");
        // 对图片是否压缩的标识符
        String compress = request.getParameter("compress");
        String watermark = request.getParameter("watermark");
        Iterator<String> fileNameIterator = request.getFileNames();

        MultipartFile multipartFile;
        while (fileNameIterator.hasNext()) {
            multipartFile = request.getFile(fileNameIterator.next());
            if (multipartFile == null) {
                // 如果没有附件则不作处理
                continue;
            }
            uploadFileDO = upload(multipartFile, busType, compress, businessFolder, watermark, sysc080Vwatermark, limitFileFormat);
        }
        return uploadFileDO;
    }

    /**
     * 上传附件
     *
     * @param multipartFile 附件信息
     * @param busType       业务类型。区分文件夹
     * @param compress      对图片是否压缩的标识符。1压缩，其他不压缩
     * @return 附件信息
     */
    @Override
    public UploadFileDO upload(MultipartFile multipartFile, String busType, String compress) {
        return this.upload(multipartFile, busType, compress, "business", null, null);
    }

    /**
     * 上传附件
     *
     * @param multipartFile  附件信息
     * @param busType        业务类型。区分文件夹
     * @param compress       对图片是否压缩的标识符。1压缩，其他不压缩
     * @param businessFolder 文件路径（不包括FTP、腾讯COS自带的跟路径）
     * @return 附件信息
     */
    @Override
    public UploadFileDO upload(MultipartFile multipartFile, String busType, String compress, String businessFolder, String watermark, String sysc080Vwatermark) {
        return upload(multipartFile, busType, compress, businessFolder, watermark, sysc080Vwatermark, Lists.newArrayList());
    }

    /**
     * 上传附件
     *
     * @param multipartFile  附件信息
     * @param busType        业务类型。区分文件夹
     * @param compress       对图片是否压缩的标识符。1压缩，其他不压缩
     * @param businessFolder 文件路径（不包括FTP、腾讯COS自带的跟路径）
     * @return 附件信息
     */
    @Override
    public UploadFileDO upload(MultipartFile multipartFile, String busType, String compress, String businessFolder, String watermark, String sysc080Vwatermark, List<String> limitFileFormat) {
        UploadFileDO uploadFileDO = new UploadFileDO();

        String oldFileName = multipartFile == null ? "" : multipartFile.getOriginalFilename();
        log.debug("原始文件名：{}", oldFileName);
        String suffix = oldFileName == null ? "" : oldFileName.substring(oldFileName.lastIndexOf(".") + 1);
        String upperCaseSuffix = suffix.toUpperCase();

        if (!CollectionUtils.isEmpty(limitFileFormat)) {
            limitFileFormat = limitFileFormat.stream().map(String::toUpperCase).collect(Collectors.toList());
            if (!limitFileFormat.contains(upperCaseSuffix)) {
                String message = i18nUtil.getMessage("ERR.basecommon.UploadFileServiceImpl.fileTypeError");
                throw new QmException(message + String.join(",", limitFileFormat));
            }
        }

        // 上传的文件夹，判断是否配置根目录，去上传业务类型，年，月日，组成文件夹
        String filePath = "";
        // 文件名称
        String newFileName = RandomUtils.getRandomID();
        String fileSaveName = newFileName + "." + suffix;
        // 上传的文件夹：businessType/busType/yyyy/MMdd组成文件夹
        String basePath = this.getBasePath(busType, businessFolder);
        try {
            filePath = this.uploadFileCenter(basePath, fileSaveName, multipartFile, compress, watermark, sysc080Vwatermark);
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
            String message = i18nUtil.getMessage("ERR.basecommon.UploadFileServiceImpl.fileUploadFail");
            throw new QmException(message, e);
        }
        // 上传成功，保存附件表信息
        uploadFileDO.setVfilename(oldFileName);
        uploadFileDO.setVtype(suffix.toUpperCase());
        uploadFileDO.setVaddr(filePath + FILE_SEPARATOR + fileSaveName);
        if (multipartFile != null) {
            uploadFileDO.setVcontenttype(multipartFile.getContentType()+";charset=utf-8");
        }
        /**
         * 将sysb200与sysb080的处理逻辑合并
         * if (!BootAppUtil.isNullOrEmpty(businessFolder) && "ExcelManager".equals(businessFolder)) {
         *     this.saveOrUpdate(uploadFileDO);
         * }
         */
        return uploadFileDO;
    }

    /**
     * 上传的文件夹：businessType/busType/yyyy/MMdd组成文件夹
     *
     * @param busType        busType
     * @param businessFolder businessFolder
     * @return 文件夹路径
     */
    private String getBasePath(String busType, String businessFolder) {
        String[] dateSplit = DateFormatUtils.format(new Date(), "yyyy;MMdd").split(";");
        return businessFolder + FILE_SEPARATOR
                + (BootAppUtil.isNullOrEmpty(busType) ? "" : busType + FILE_SEPARATOR)
                + dateSplit[0] + FILE_SEPARATOR + dateSplit[1];
    }

    /**
     * 上传文件
     *
     * @param basePath     路径
     * @param fileSaveName 名
     * @param mpf          文件
     * @param compress     表达是否需要压缩的字符串
     *                     如果为0则不启用压缩
     *                     如果为null、空、1，启用压缩
     *                     其他值不启用压缩
     * @return 文件路径
     */
    private String uploadFileCenter(String basePath, String fileSaveName, MultipartFile mpf, String compress, String watermark, String sysc080Vwatermark) {
        String result = "";
        try {
            MultipartFile mpfDTO;
            String suffix = ImageUtil.getSuffix(mpf.getOriginalFilename());
            // 图片压缩条件：
            // 1、当文件后缀名为"JPG", "JPEG", "PNG"
            // 2、并且compress参数为空或 1 的时候
            boolean needCompress = imageFormat.contains(suffix.toLowerCase())
                    && (StringUtils.isBlank(compress) || "1".equals(compress.trim()));
            long size = mpf.getSize() / 1024;

            //如果图片大小小于 512k 则不进行压缩
            if (needCompress && size > 512) {
                byte[] newFileByte = ImageUtil.compressImg(mpf.getBytes(), sizeLimit, suffix);
                // 自定义装饰器MultipartFileDecorator
                mpfDTO = new MultipartFileDecorator(mpf, newFileByte);
            } else {
                mpfDTO = mpf;
            }

            mpfDTO = watermark(mpfDTO, watermark, sysc080Vwatermark);

            log.debug("开始上传[{}][{}]...", basePath, fileSaveName);
            result = fileOperator.uploadFile(basePath, fileSaveName, mpfDTO);
            log.debug("上传结束[{}][{}][{}]...", basePath, fileSaveName, result);
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
            String message = i18nUtil.getMessage("ERR.basecommon.UploadFileServiceImpl.fileUploadFail");
            throw new QmException(message, e);
        }
        return result;
    }


    private MultipartFile watermark(MultipartFile multipartFile, String watermark, String sysc080Vwatermark) {
        String suffix = ImageUtil.getSuffix(multipartFile.getOriginalFilename());
        boolean images = imageFormat.contains(suffix.toLowerCase());
        if (images && StringUtils.isNotEmpty(watermark)) {
            try {
                if (StringUtils.isNotBlank(sysc080Vwatermark)) {
                    Integer num = Integer.valueOf(sysc080Vwatermark);
                    if (num == 1) {
                        return WatermarkFactory.getWatermarkUtils(suffix).bottomRightCorner(multipartFile, watermark);
                    }
                    if (num == 2) {
                        return WatermarkFactory.getWatermarkUtils(suffix).fullScreen(multipartFile, watermark);
                    }
                }
            } catch (NumberFormatException | IOException e) {
                log.info("---error--"+e.getMessage(), e);
            }
        }
        return multipartFile;
    }

    @Override
    public boolean download(HttpServletRequest req, HttpServletResponse response) {
        String fileId = req.getParameter("fileId");
        String businessType = req.getParameter("businessType");
        String seq = req.getParameter("seq");
        String transCode = req.getParameter("transCode");

        UploadFileVO uploadFileVo;
        // 查询文件信息,如果不是excel文件管理则查询进行区分
        if ("ExcelManager".equals(businessType)) {
            UploadFileDTO uploadFileDTO = new UploadFileDTO();
            if (!BootAppUtil.isNullOrEmpty(fileId)) {
                uploadFileDTO.setId(fileId);
            }
            if (!BootAppUtil.isNullOrEmpty(seq)) {
                uploadFileDTO.setVseq(seq);
            }
            if (!BootAppUtil.isNullOrEmpty(transCode)) {
                uploadFileDTO.setVtranscode(transCode);
            }
            uploadFileVo = baseMapper.selectExcelFile(uploadFileDTO);
        } else {
            UploadFileDO uploadFileDO = this.getById(fileId);
            uploadFileVo = this.convert(uploadFileDO);
        }
        if (BootAppUtil.isNullOrEmpty(uploadFileVo)) {
            return false;
        }
        /*相应头的处理,清空response中的输出流*/
        response.reset();
        //设置Content-Type头
        this.responseHeaderDowload(response, uploadFileVo, businessType);

        //新的下载分支开始 ly 20201104 add
        try {
            fileOperator.downloadFile(response, uploadFileVo.getVaddr());
        } catch (Exception e) {
            //            文件下载操作错误，请查看日志！
            String message = i18nUtil.getMessage("ERR.basecommon.UploadFileServiceImpl.downloadError");
            log.info("---error--"+message, e);
            throw new QmException(message, e);
        }
        return true;
    }

    private UploadFileVO convert(UploadFileDO uploadFileDO) {
        UploadFileVO uploadFilevO = new UploadFileVO();
        if (!BootAppUtil.isNullOrEmpty(uploadFileDO)) {
            if (!BootAppUtil.isNullOrEmpty(uploadFileDO.getId())) {
                uploadFilevO.setId(uploadFileDO.getId());
            }
            if (!BootAppUtil.isNullOrEmpty(uploadFileDO.getVdsc())) {
                uploadFilevO.setVdsc(uploadFileDO.getVdsc());
            }
            if (!BootAppUtil.isNullOrEmpty(uploadFileDO.getVfilename())) {
                uploadFilevO.setVfilename(uploadFileDO.getVfilename());
            }
            if (!BootAppUtil.isNullOrEmpty(uploadFileDO.getVtype())) {
                uploadFilevO.setVtype(uploadFileDO.getVtype());
            }
            if (!BootAppUtil.isNullOrEmpty(uploadFileDO.getVaddr())) {
                uploadFilevO.setVaddr(uploadFileDO.getVaddr());
            }
            if (!BootAppUtil.isNullOrEmpty(uploadFileDO.getVcontenttype())) {
                uploadFilevO.setVcontenttype(uploadFileDO.getVcontenttype());
            }
        }
        return uploadFilevO;
    }

    /**
     * download 方法专用response的Header赋值过程
     */
    private void responseHeaderDowload(HttpServletResponse response, UploadFileVO uploadFilevO, String businessType) {
        String utf8 = StandardCharsets.UTF_8.name();
        try {
            String contentType = StringUtils.defaultIfBlank(uploadFilevO.getVcontenttype(), defaultContentType);
            response.setContentType(contentType);
            //设置Content-Disposition头 以附件形式解析
            if ("AppFile".equals(businessType) || defaultContentType.equals(contentType) || "ExcelManager".equals(businessType)) {
                if (!BootAppUtil.isNullOrEmpty(uploadFilevO.getVfilename())) {
                    response.addHeader(responseHeaderText, responseFilename + new String(uploadFilevO.getVfilename().getBytes(utf8), wwwChartType));
                    response.addHeader(localFileName, URLEncoder.encode(uploadFilevO.getVfilename(), utf8));
                }
            }
        } catch (Exception e) {
            //            文件下载操作错误，请查看日志！
            String message = i18nUtil.getMessage("ERR.basecommon.UploadFileServiceImpl.downloadError");
            log.info("---error--"+message, e);
            throw new QmException(message, e);
        }
    }

    /**
     * 下载附件使用的download方法
     * fileId为文件id 在上层download方法中生效
     * downloadFlag 为1时候就是下载模式，没有值或者0为正常打开模式，图片默认打开，文件类型下载。在本服务中生效
     * thumbnailFlag 为缩略图标识normal是原图，small是缩略图。在本服务中有效
     * width 缩略图宽。在本服务中生效
     * heigh 缩略图高。在本服务中生效
     *
     * <AUTHOR>
     * <AUTHOR>
     * @since 20201113
     * @since 2021/4/14
     */
    public boolean downloadSuper(HttpServletRequest req, HttpServletResponse response, UploadFileVO uploadFileVO) {
        if (uploadFileVO == null) {
            return false;
        }
        String downloadFlag = req.getParameter("downloadFlag");
        String thumbnailFlag = req.getParameter("thumbnailFlag");
        String widthStr = req.getParameter("width");
        String heightStr = req.getParameter("height");
        /*相应头的处理,清空response中的输出流*/
        response.reset();
        //设置response的header根据入参
        this.responseIntHeader(response, downloadFlag, uploadFileVO);

        //如果是缩略图方式那么需要重新拼接fileName规则为：服务器文件名字+"_"+width+“_”+heigh+图片后缀名
        int width = 200;
        int heigh = 200;
        if (FileOperator.THUMBNAIL_SMALL.equals(thumbnailFlag)) {
            width = BootAppUtil.isNullOrEmpty(widthStr) ? width : Integer.parseInt(widthStr);
            heigh = BootAppUtil.isNullOrEmpty(heightStr) ? heigh : Integer.parseInt(heightStr);
            String suffix = getSuffixByUploadFileVO(uploadFileVO);
            boolean needCompress = imageFormat.contains(suffix.toLowerCase());
            if (("0".equals(downloadFlag) || null == downloadFlag) && !needCompress && !response.getContentType().contains("video")) {
                byte[] thumbnail = ImageUtil.getThumbnail(suffix, width, heigh);
                response.setHeader("Accept-Ranges", "");
                response.setHeader("Content-Length", "");
                response.setContentType("image/jpeg");
                try {
                    StreamUtils.copy(thumbnail, response.getOutputStream());
                } catch (IOException e) {
                    log.info("---error--"+"输出文件名缩略图失败！" + e.getMessage(), e);
                }
                return true;
            }
        }
        //新的下载分支开始 ly 20201104 add
        try {
            if (StringUtils.defaultIfBlank(uploadFileVO.getVaddr(), "").startsWith("http")) {
                return restDownload(response, thumbnailFlag, width, heigh, uploadFileVO);
            } else {
                this.shiftFile(uploadFileVO);
                return fileOperator.downloadFile(response, thumbnailFlag, width, heigh, uploadFileVO);
            }
        } catch (QmException e) {
            throw e;
        } catch (Exception e) {
            //            文件下载操作错误，请查看日志！
            String message = i18nUtil.getMessage("ERR.basecommon.UploadFileServiceImpl.downloadError");
            log.info("---error--"+message, e);
            throw new QmException(message, e);
        }
    }

    private String getSuffixByUploadFileVO(UploadFileVO uploadFileVO) {
        /**
         * 先从vtype字段中获取文件后缀名；
         * 如果vtype字段为空则从addr中取（即URL中取）；
         * 如果addr字段为空，则返回空值
         */
        String suffix = "";
        if (StringUtils.isNotEmpty(uploadFileVO.getVtype())) {
            suffix = uploadFileVO.getVtype();
        } else if (StringUtils.isNotEmpty(uploadFileVO.getVaddr())) {
            String fullPathNameAndSuffix = uploadFileVO.getVaddr();
            // nameAndSuffix=文件名+文件后缀名
            String nameAndSuffix = fullPathNameAndSuffix.substring(fullPathNameAndSuffix.lastIndexOf(FILE_SEPARATOR) + 1);
            // 如果nameAndSuffix中没有后缀名，则返回空
            if (nameAndSuffix.lastIndexOf(".") < 0) {
                suffix = "";
            } else {
                suffix = nameAndSuffix.substring(nameAndSuffix.lastIndexOf(".") + 1, nameAndSuffix.length());
            }
        }
        return suffix;
    }

    private boolean restDownload(HttpServletResponse response, String thumbnailFlag, int width, int heigh, UploadFileVO uploadFileVO) throws IOException {
        //1、下载图片
        ResponseEntity<byte[]> forEntity = restTemplate.getForEntity(uploadFileVO.getVaddr(), byte[].class);
        byte[] fileBody = forEntity.getBody();
        if (ArrayUtils.isEmpty(fileBody)) {
            // 文件地址有误，文件下载失败！url
            String message = i18nUtil.getMessage("ERR.basecommon.UploadFileServiceImpl.urlWrong");
            throw new QmException(message + "=" + uploadFileVO.getVaddr());
        }
        //2、压缩图片
        if (FileOperator.THUMBNAIL_SMALL.equals(thumbnailFlag)) {
            assert fileBody != null;
            ByteArrayInputStream bais = new ByteArrayInputStream(fileBody);
            String suffix = ImageUtil.getSuffix(uploadFileVO.getVfilename());
            InputStream inputStream = imageUtil.thumbanailImage(bais, suffix, width, heigh);
            //3、返回结果
            int byteNum = StreamUtils.copy(inputStream, response.getOutputStream());
            return byteNum != 0;
        } else {
            //3、返回结果
            assert fileBody != null;
            StreamUtils.copy(fileBody, response.getOutputStream());
            return true;
        }
    }

    /**
     * downloadSuper 方法专用response的Header赋值过程
     */
    private void responseIntHeader(HttpServletResponse response, String downloadFlag, UploadFileVO uploadFilevO) {
        String utf8 = StandardCharsets.UTF_8.name();
        String tempDownloadFlag = StringUtils.defaultIfBlank(downloadFlag, "0");
        // 设置请求头的contentType
        String contentType = StringUtils.defaultIfBlank(uploadFilevO.getVcontenttype(), defaultContentType);
        response.setContentType(contentType);
        if (contentType.contains("video")) {
            response.addHeader("Accept-Ranges", "bytes");
        }
        /*
         * downloadFlag
         *  为1时：附件下载模式
         *  为0时：正常打开模式（图片和PDF默认打开，文件类型下载）
         */
        if (("1".equals(tempDownloadFlag) && !BootAppUtil.isNullOrEmpty(uploadFilevO.getVfilename()))) {
            //设置Content-Disposition头 以附件形式解析
            try {
                response.addHeader(responseHeaderText, responseFilename + new String(uploadFilevO.getVfilename().getBytes(utf8), wwwChartType));
                response.addHeader(localFileName, URLEncoder.encode(uploadFilevO.getVfilename(), utf8));
            } catch (UnsupportedEncodingException e) {
                String message = i18nUtil.getMessage("ERR.basecommon.UploadFileServiceImpl.responseHeaderException");
                log.info("---error--"+message, e);
                throw new QmException(message, e);
            }
        }
    }

    /**
     * 判断 uploadFileVO 中 vaddr 和 vitffulfilename 字段
     * 若 vaddr 无值 并且 vitffulfilename 有值
     * 则 将第三方附件下载到本地再下载
     *
     * @param uploadFileVO 附件信息
     */
    private void shiftFile(UploadFileVO uploadFileVO) {
        if (BootAppUtil.isnotNullOrEmpty(uploadFileVO.getVaddr())
                || BootAppUtil.isNullOrEmpty(uploadFileVO.getVitffulfilename())) {
            return;
        }
        String url = uploadFileVO.getVitffulfilename();
        if (restTemplate == null) {
            log.info("[-UploadFileServiceImpl-].shiftFile:restTemplate为空");
            restTemplate = new RestTemplate();
        }
        String name = uploadFileVO.getVfilename();
        String contentType = uploadFileVO.getVcontenttype();
        String busType = uploadFileVO.getVbustype();
        // 上传的文件夹：filemanager/shift/busType/yyyy/MMdd组成文件夹
        String basePath = this.getBasePath(busType, BUSINESS_TYPE);
        ResponseEntity<byte[]> fileBytes;
        try {
            fileBytes = restTemplate.getForEntity(url, byte[].class);
        } catch (RestClientException e) {
            log.info("---error--"+"[-UploadFileServiceImpl-].shiftFile:第三方附件下载失败", e);
            return;
        }
        // 文件名称
        String savename = RandomUtils.getRandomID() + "." + StringUtils.defaultIfBlank(uploadFileVO.getVtype(), ImageUtil.SUFFIX_DEFAULT).toLowerCase();
        MultipartFile multipartFile = new MultipartFileDecorator(fileBytes.getBody(), savename, name, contentType);
        try {
            this.uploadFileCenter(basePath, savename, multipartFile, null, null, null);
        } catch (Exception e) {
            log.info("---error--"+"[-UploadFileServiceImpl-].shiftFile:第三方附件转存失败", e);
            return;
        }
        UploadFileDO uploadFileDO = new UploadFileDO();
        uploadFileDO.setId(uploadFileVO.getId());
        uploadFileDO.setVaddr(basePath + FILE_SEPARATOR + savename);
        boolean b;
        try {
            b = uploadFileMapper.update080(uploadFileDO);
            if (!b) {
                log.info("---error--"+"[-UploadFileServiceImpl-].shiftFile:本地存储失败={}", uploadFileDO);
                return;
            }
            uploadFileVO.setVaddr(basePath + FILE_SEPARATOR + savename);
        } catch (Exception e) {
            log.info("---error--"+"[-UploadFileServiceImpl-].shiftFile:本地存储失败", e);
        }
    }
}



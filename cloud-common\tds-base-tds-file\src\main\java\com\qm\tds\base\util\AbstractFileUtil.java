package com.qm.tds.base.util;

import com.qm.tds.api.exception.QmException;
import com.qm.tds.base.domain.vo.UploadFileVO;
import com.qm.tds.util.ImageUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.util.StreamUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 文件上传下载的公用类库基类
 */
@Slf4j
@Deprecated
public abstract class AbstractFileUtil {

    /**
     * 缩略图类型。小尺寸。
     */
    protected static final String THUMBNAIL_SMALL = "small";
    /**
     * 缩略图类型。常规尺寸。
     */
    protected static final String THUMBNAIL_NORMAL = "normal";

    /**
     * 上传
     *
     * @param basePath     文件路径
     * @param fileSaveName 存储的文件名
     * @param data         文件数据
     * @return 保存的文件路径
     */
    public abstract String uploadFile(String basePath, String fileSaveName, byte[] data);

    /**
     * 上传
     *
     * @param basePath      文件路径
     * @param fileSaveName  存储的文件名
     * @param multipartFile 文件对象
     * @return 保存的文件路径
     */
    public String uploadFile(String basePath, String fileSaveName, MultipartFile multipartFile) {
        try {
            log.debug("上传文件[size:{}][{}][{}][{}]..."
                    , multipartFile.getSize()
                    , multipartFile.getBytes().length
                    , basePath
                    , fileSaveName);
            return uploadFile(basePath, fileSaveName, multipartFile.getBytes());
        } catch (Exception e) {
            log.info("---error--"+"上传文件失败！" + e.getMessage(), e);
            return "";
        }
    }

    /**
     * 下载文件数据
     *
     * @param allfileName 文件路径
     * @return
     */
    public abstract byte[] downloadFile(String allfileName);

    /**
     * 下载文件并写入到Response输出流中
     *
     * @param response    Response流
     * @param allfileName 文件名称
     * @return 下载是否成功
     */
    public Boolean downloadFile(HttpServletResponse response, String allfileName) {
        try {
            byte[] fileData = this.downloadFile(allfileName);
            if (ArrayUtils.isNotEmpty(fileData)) {
                StreamUtils.copy(fileData, response.getOutputStream());
                return true;
            } else {
                log.info("下载文件在服务器中不存在[{}]", allfileName);
                return false;
            }
        } catch (IOException e) {
            log.info("---error--"+"下载文件异常！" + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 下载图片并写入到Response输出流中
     *
     * @param response      Response流
     * @param thumbnailFlag 图片类型。small、normal
     * @param width         缩略图图片宽度
     * @param heigh         缩略图图片高度
     * @param uploadFilevO  附件信息
     * @return 下载是否成功
     */
    public Boolean downloadFile(HttpServletResponse response, String thumbnailFlag, int width, int heigh, UploadFileVO uploadFilevO) {
        try {
            String vaddr = uploadFilevO.getVaddr();
            switch (thumbnailFlag) {
                case THUMBNAIL_SMALL:
                    String basePath = vaddr.substring(0, vaddr.lastIndexOf("/") + 1);
                    String fileName = vaddr.substring(vaddr.lastIndexOf("/") + 1);
                    String fileThumbnailImageName = fileName.substring(0, fileName.lastIndexOf(".") - 1)
                            + "_" + width + "_" + heigh
                            + fileName.substring(fileName.lastIndexOf("."));
                    String allFileNormalname = basePath + fileThumbnailImageName;
                    // 下载缩略图
                    byte[] fileData = this.downloadFile(allFileNormalname);
                    if (ArrayUtils.isEmpty(fileData)) {
                        // 下载原始图片
                        byte[] oldFileData = this.downloadFile(vaddr);
                        // 服务器上没有对应文件，生成一个缩略图
                        fileData = this.createThumbanailImage(oldFileData, uploadFilevO.getVcontenttype(), width, heigh);
                        this.uploadFile(basePath, fileThumbnailImageName, fileData);
                    }

                    // 输出缩略图
                    StreamUtils.copy(fileData, response.getOutputStream());
                    break;
                case THUMBNAIL_NORMAL:
                default:
                    // 下载原始图片
                    return this.downloadFile(response, vaddr);
            }
        } catch (Exception e) {
            log.info("---error--"+"下载出错", e);
            throw new QmException("文件下载操作错误，请查看日志;", e);
        }
        return false;
    }

    /**
     * 将文件输出到Reponse流中
     *
     * @param response
     * @param fileData 文件数据
     * @throws IOException
     */
    protected void outputFile(HttpServletResponse response, byte[] fileData) throws IOException {
        //获得回传res中是流位置
        if (ArrayUtils.isNotEmpty(fileData)) {
            //将来文件流写入response中
            StreamUtils.copy(fileData, response.getOutputStream());
        }
    }

    /**
     * 生成缩略图
     *
     * @param imageData 原始图片数据
     * @param fileType  图片后缀名
     * @param width     缩略图宽度
     * @param height    缩略图高度
     * @return 缩略图数据
     */
    protected byte[] createThumbanailImage(byte[] imageData, String fileType, int width, int height) {
        byte[] thumbanaiData = null;
        if (imageData != null && imageData.length > 0) {
            InputStream isNormal = new ByteArrayInputStream(imageData);
            ImageUtil imageUtil = new ImageUtil();
            thumbanaiData = imageUtil.thumbanailImageBytes(isNormal, fileType, width, height, false);
        }
        return thumbanaiData;
    }
}

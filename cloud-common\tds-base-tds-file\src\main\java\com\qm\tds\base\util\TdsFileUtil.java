package com.qm.tds.base.util;

import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.util.SpringContextHolder;
import com.qm.tds.base.remote.TDSFileService;
import com.qm.tds.base.remote.TDSFileServiceSoap;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.support.WebApplicationContextUtils;

import java.net.MalformedURLException;
import java.net.URL;

/**
 * TDSv2附件服务器帮助类
 */
@Slf4j
@Deprecated
public class TdsFileUtil extends AbstractFileUtil {

    private TDSFileServiceSoap webService;

    public TdsFileUtil() {
        init();
    }

    /**
     * 初始化信息
     */
    private void init() {
        if (webService == null) {
            String url = null;
            // 获取参数配置
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(request.getServletContext());
                Environment env = ac.getBean(Environment.class);

                url = env.getProperty("qm.base.upload.tdsv2.url");
            }

            // 初始化WebService对象
            if (BootAppUtil.isNullOrEmpty(url)) {
                I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
                String message = i18nUtil.getMessage("ERR.basecommon.TdsOperator.fileServiceUrlNull");
                throw new QmException(message);
            } else {
                try {
                    URL wsdlLocation = new URL(url);
                    TDSFileService service = new TDSFileService(wsdlLocation);
                    webService = service.getTDSFileServiceSoap();
                } catch (MalformedURLException e) {
                    I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
                    String message = i18nUtil.getMessage("ERR.basecommon.TdsOperator.serviceUrlIncorrect");
                    throw new QmException(message + e.getMessage(), e);
                } catch (Exception e) {
                    I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
                    String message = i18nUtil.getMessage("ERR.basecommon.TdsOperator.soapServiceCreateFail");
                    throw new QmException(message + url + "]！" + e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 上传
     *
     * @param basePath     文件路径
     * @param fileSaveName 存储的文件名
     * @param data         文件数据
     * @return 保存的文件路径
     */
    @Override
    public String uploadFile(String basePath, String fileSaveName, byte[] data) {
        log.debug("开始上传TDSv2文件[{}][{}][{}]"
                , basePath
                , fileSaveName
                , (data == null ? "null" : String.valueOf(data.length)));
        this.webService.writeFile(basePath, data, fileSaveName);
        return basePath;
    }

    /**
     * 下载文件数据
     *
     * @param allfileName 文件路径
     * @return
     */
    @Override
    public byte[] downloadFile(String allfileName) {
        log.debug("开始下载TDSv2文件[{}]", allfileName);
        return this.webService.getAccessoriesByName(allfileName);
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.tds.base.mapper.UploadFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.tds.base.domain.bean.UploadFileDO">
        <result column="ID" property="id"/>
        <result column="VDSC" property="vdsc"/>
        <result column="VFILENAME" property="vfilename"/>
        <result column="VTYPE" property="vtype"/>
        <result column="VADDR" property="vaddr"/>
        <result column="VCONTENTTYPE" property="vcontenttype"/>
        <result column="DTSTAMP" property="dtstamp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VDSC, VFILENAME, VTYPE, VADDR, VCONTENTTYPE, DTSTAMP
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select *
        from (
                 select a.ID,
                        a.VDSC,
                        a.VFILENAME,
                        a.VTYPE,
                        a.VADDR,
                        a.VCONTENTTYPE,
                        a.DTSTAMP
                 from sysb200 a
             ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.tds.base.domain.bean.UploadFileDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.tds.base.domain.bean.UploadFileDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.tds.base.domain.bean.UploadFileDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.tds.base.domain.bean.UploadFileDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.tds.base.domain.bean.UploadFileDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.tds.base.domain.bean.UploadFileDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.tds.base.domain.bean.UploadFileDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.tds.base.domain.bean.UploadFileDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.tds.base.domain.bean.UploadFileDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>

    <select id="selectExcelFile" parameterType="com.qm.tds.base.domain.dto.UploadFileDTO"
            resultType="com.qm.tds.base.domain.vo.UploadFileVO">
        SELECT
        a.ID,
        a.VREMARK as VDSC,
        a.VFILENAME,
        a.VEXTENSION as VTYPE,
        a.VADDR,
        a.VCONTENTTYPE,
        a.DTSTAMP
        FROM
        sysb080 a
        inner JOIN sysc024 b ON a.id = b.vfileid
        <where>
            <choose>
                <when test="id != null">
                    AND a.id = #{id, jdbcType=VARCHAR}
                </when>
                <otherwise>
                    <if test="vtranscode != null">
                        AND b.VTRANSCODE = #{vtranscode, jdbcType=VARCHAR}
                    </if>
                    <choose>
                        <when test="vseq != null">
                            AND b.VSEQ = #{vseq, jdbcType=VARCHAR}
                        </when>
                        <otherwise>
                            AND b.VSEQ = ""
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
        </where>
        ORDER BY a.DTSTAMP DESC
        LIMIT 1
    </select>

    <update id="update080">
        update sysb080 a
        set a.VADDR=#{vaddr}
        where a.id = #{id}
    </update>
</mapper>

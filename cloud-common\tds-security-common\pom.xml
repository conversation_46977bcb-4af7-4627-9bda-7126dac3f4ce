<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.qm.tds</groupId>
        <artifactId>tds-base-service-parent</artifactId>
        <version>7.1.0-rebate-SNAPSHOT</version>
    </parent>

    <groupId>com.qm.tds</groupId>
    <artifactId>tds-security-common</artifactId>
    <version>7.1.0-rebate-SNAPSHOT</version>
    <name>tds-security-common</name>
    <description>TDS产品安全中心基础类库</description>
    <packaging>jar</packaging>
    <properties>
        <!--<oauth2-version>2.3.3.RELEASE</oauth2-version>
        <jwt-version>1.0.9.RELEASE</jwt-version>-->
    </properties>
    <dependencies>
        <!--Spring  security-->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
        </dependency>
        <!--SpringBoot的Redis支持-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-jwt</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <target>17</target>
                    <source>17</source>
                    <encoding>UTF-8</encoding>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 上传jar包至私有maven仓库-->
    <distributionManagement>
        <repository>
            <id>dayu-maven-releases</id>
            <url>https://devops-nexus.faw.cn/repository/bp4655969726-releases/</url>
        </repository>
        <snapshotRepository>
            <id>dayu-maven-snapshots</id>
            <url>https://devops-nexus.faw.cn/repository/bp4655969726-snapshots/</url>
            <uniqueVersion>true</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

</project>

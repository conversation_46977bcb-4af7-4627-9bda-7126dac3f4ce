package com.qm.tds.security.common.config;

import com.qm.tds.security.common.service.JwtTokenStoreService;
import com.qm.tds.security.common.service.RedisAuthenticationCodeServices;
import com.qm.tds.security.common.service.RedisTokenStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.code.AuthorizationCodeServices;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.KeyStoreKeyFactory;

import java.security.KeyPair;

/**
 * @description: token存储配置
 * @author: Cyl
 * @time: 2020/8/26 14:07
 */
@Configuration
public class TokenStoreConfig {

    private final RedisConnectionFactory redisConnectionFactory;

    @Autowired(required = false)
    private ClientDetailsService clientDetailsService = null;

    @Autowired
    public TokenStoreConfig(RedisConnectionFactory redisConnectionFactory) {
        this.redisConnectionFactory = redisConnectionFactory;
    }

    @Bean
    public TokenStore redisTokenStore() {
        RedisTokenStoreService tokenStoreService = new RedisTokenStoreService(redisConnectionFactory, clientDetailsService);
        tokenStoreService.setPrefix("user-token:");
        return tokenStoreService;
    }

    //@Bean
    public TokenStore jwtTokenStore() {
        return new JwtTokenStoreService(accessTokenConverter());
    }


    @Bean
    public JwtAccessTokenConverter accessTokenConverter() {
        JwtAccessTokenConverter jwtAccessTokenConverter = new JwtAccessTokenConverter();
        jwtAccessTokenConverter.setKeyPair(keyPair());
        return jwtAccessTokenConverter;
    }

    @Bean
    public KeyPair keyPair() {
        //从classpath下的证书中获取秘钥对
        KeyStoreKeyFactory keyStoreKeyFactory = new KeyStoreKeyFactory(new ClassPathResource("jwt.jks"), "123456".toCharArray());
        return keyStoreKeyFactory.getKeyPair("jwt", "123456".toCharArray());
    }

    @Bean
    public AuthorizationCodeServices authorizationCodeServices() {
        return new RedisAuthenticationCodeServices(redisConnectionFactory);
    }


}

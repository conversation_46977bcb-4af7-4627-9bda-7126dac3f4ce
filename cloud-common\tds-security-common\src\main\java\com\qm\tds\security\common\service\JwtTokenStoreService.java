package com.qm.tds.security.common.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;

import java.util.Date;

/**
 * @description: JWT 处理续签
 * @author: Cyl
 * @time: 2020/8/31 9:18
 */
@Slf4j
public class JwtTokenStoreService extends JwtTokenStore {

    public JwtTokenStoreService(JwtAccessTokenConverter jwtTokenEnhancer) {
        super(jwtTokenEnhancer);
        /**
         * jwtAccessTokenConverter = jwtTokenEnhancer;
         */
    }

    @Override
    public OAuth2Authentication readAuthentication(OAuth2AccessToken token) {
        OAuth2Authentication result = readAuthentication(token.getValue());
        if (result != null) {
            // 如果token没有失效  更新AccessToken过期时间
            DefaultOAuth2AccessToken oAuth2AccessToken = (DefaultOAuth2AccessToken) token;
            //重新设置过期时间
            int validitySeconds = getAccessTokenValiditySeconds();
            if (validitySeconds > 0) {
                oAuth2AccessToken.setExpiration(new Date(System.currentTimeMillis() + (validitySeconds * 1000L)));
            }
            /**
             * setJwtStore(token, result);
             */
        }
        return result;
    }

    /**
     * private final JwtAccessTokenConverter jwtAccessTokenConverter;
     * private JsonParser objectMapper = JsonParserFactory.create();
     * <p>
     * protected void setJwtStore(OAuth2AccessToken token, OAuth2Authentication authentication) {
     * Map<String, Object> claims = objectMapper.parseMap(JwtHelper.decode(token.getValue()).getClaims());
     * token = jwtAccessTokenConverter.enhance(token, authentication);
     * log.info("解密:" + claims);
     * }
     */

    /**
     * 获取token有效期。
     * 默认2小时。
     *
     * @return token有效期
     */
    protected int getAccessTokenValiditySeconds() {
        //默认俩小时 60 * 60 * 2
        return 7200;
    }
}

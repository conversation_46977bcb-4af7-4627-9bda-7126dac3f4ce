package com.qm.tds.security.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * redis操作工具类
 * (基于RedisTemplate)
 */
@Slf4j
@Component
public class RedisSecurityUtils {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 读取缓存
     *
     * @param key
     * @return
     */
    public String get(final String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 写入缓存。
     * 根据yml文件中的dmscommon.redisTtl时长作为缓存有效期，单位为秒。
     */
    public boolean set(final String key, String value) {
        boolean result = false;
        try {
            redisTemplate.opsForValue().set(key, value);
            result = true;
        } catch (Exception e) {
            log.info("---error--"+String.format("写入Redis缓存失败1[key: %s ][value: %s ] %s ", key, value, e.getMessage()), e);
        }
        return result;
    }

    /**
     * 写入缓存。
     *
     * @param key   缓存的Key
     * @param value 缓存值
     * @param nTtl  缓存有效期，单位为秒。0表示永久有效。
     * @return
     */
    public boolean set(final String key, String value, long nTtl) {
        boolean result = false;
        try {
            if (nTtl > 0) {
                redisTemplate.opsForValue().set(key, value, nTtl, TimeUnit.SECONDS);
            } else {
                redisTemplate.opsForValue().set(key, value);
            }
            result = true;
        } catch (Exception e) {
            log.info("---error--"+String.format("写入Redis缓存失败2[key: %s ][value: %s ] %s ", key, value, e.getMessage()), e);
        }
        return result;
    }

    /**
     * 更新缓存
     */
    public boolean getAndSet(final String key, String value) {
        boolean result = false;
        try {
            redisTemplate.opsForValue().getAndSet(key, value);
            result = true;
        } catch (Exception e) {
            log.info("---error--"+String.format("更新Redis缓存失败[key: %s ][value: %s ] %s ", key, value, e.getMessage()), e);
        }
        return result;
    }

    /**
     * 删除缓存
     */
    public boolean delete(final String key) {
        boolean result = false;
        try {
            redisTemplate.delete(key);
            result = true;
        } catch (Exception e) {
            log.info("---error--"+String.format("删除Redis缓存失败[key: %s ] %s ", key, e.getMessage()), e);
        }
        return result;
    }
}